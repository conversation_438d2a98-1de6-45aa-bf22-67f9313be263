# 🏠 BANGHALAU - Système de Distribution des Bungalows

Application web **ultra-moderne** avec interface révolutionnaire pour la gestion et distribution des bungalows au personnel militaire.

## ✅ Version Finale - Interface Ultra-Moderne et Attractive

Le système BANGHALAU a été **complètement transformé** avec une interface utilisateur **ultra-moderne, attractive et claire** avec toutes les fonctionnalités CRUD opérationnelles.

### 🎨 **Nouveau Design Révolutionnaire:**
- ✅ **Couleurs claires et apaisantes** au lieu du noir
- ✅ **Dégradés subtils** et **effets de transparence**
- ✅ **Animations fluides** et **transitions élégantes**
- ✅ **Typographie moderne** avec la police Inter
- ✅ **Cartes flottantes** avec effets de profondeur
- ✅ **Particules animées** en arrière-plan
- ✅ **Interface responsive** pour tous les appareils

## ✨ Nouveau Design Moderne

Le système BANGHALAU a été **complètement redesigné** avec une interface utilisateur moderne et intuitive qui offre:

### 🎨 Interface Révolutionnaire
- **Design Glassmorphism** avec effets de transparence et flou
- **Navigation top-bar moderne** avec recherche globale intégrée
- **Sidebar minimaliste** avec animations fluides
- **Cartes statistiques interactives** avec gradients et ombres
- **Thème sombre automatique** selon les préférences système

### 🚀 Expérience Utilisateur Avancée
- **Animations CSS3** pour toutes les interactions
- **Transitions fluides** entre les pages
- **Effets de survol 3D** sur les éléments
- **Chargement progressif** avec spinners élégants
- **Notifications toast** en temps réel

## Fonctionnalités

- 🏠 Gestion des bungalows (ajout, modification, suppression)
- 👥 Gestion du personnel militaire
- 📊 Tableau de bord avec statistiques
- 🔄 Distribution et assignation des bungalows
- 📅 Gestion des sessions et périodes
- 🔍 Recherche et filtrage avancés
- 📱 Interface responsive (mobile-friendly)
- 🌐 Interface en français

## Structure du Projet

```
BANGHALAU/
├── app.py                 # Application Flask principale
├── run.py                 # Script de démarrage production
├── wsgi.py               # Point d'entrée WSGI
├── requirements.txt      # Dépendances Python
├── database/             # Gestion base de données
├── models/               # Modèles de données
├── templates/            # Templates HTML
├── static/               # Fichiers statiques (CSS, JS)
├── start.bat            # Script démarrage Windows
├── start.sh             # Script démarrage Linux/Mac
├── Dockerfile           # Configuration Docker
└── docker-compose.yml   # Configuration Docker Compose
```

## Installation et Démarrage Rapide

### Option 1: Démarrage Automatique (Recommandé)

**Windows:**
```bash
# Double-cliquez sur start.bat ou exécutez:
start.bat
```

**Linux/Mac:**
```bash
# Rendez le script exécutable et lancez:
chmod +x start.sh
./start.sh
```

### Option 2: Installation Manuelle

1. **Cloner le projet:**
```bash
git clone <repository-url>
cd BANGHALAU
```

2. **Créer un environnement virtuel:**
```bash
python -m venv venv

# Windows:
venv\Scripts\activate

# Linux/Mac:
source venv/bin/activate
```

3. **Installer les dépendances:**
```bash
pip install -r requirements.txt
```

4. **Démarrer l'application:**
```bash
python run.py
```

### Option 3: Docker

```bash
# Construire et démarrer avec Docker Compose
docker-compose up --build

# Ou avec Docker uniquement
docker build -t banghalau .
docker run -p 5000:5000 banghalau
```

## Accès à l'Application

Une fois démarrée, l'application sera accessible à:
- **URL:** http://localhost:5000

### 🔐 Identifiants de Connexion par Défaut
- **Nom d'utilisateur:** `admin`
- **Mot de passe:** `admin123`
- **Email:** `<EMAIL>`

> **Note:** Ces identifiants sont créés automatiquement lors de la première installation. Changez le mot de passe après la première connexion pour des raisons de sécurité.

## Déploiement en Production

### Heroku
```bash
# Installer Heroku CLI puis:
heroku create votre-app-name
git push heroku main
```

### Vercel
```bash
# Installer Vercel CLI puis:
vercel --prod
```

### Google App Engine
```bash
# Installer Google Cloud SDK puis:
gcloud app deploy
```

## Configuration

### Variables d'Environnement

- `PORT`: Port d'écoute (défaut: 5000)
- `HOST`: Adresse d'écoute (défaut: 0.0.0.0)
- `FLASK_ENV`: Environnement Flask (production/development)
- `FLASK_DEBUG`: Mode debug (true/false)

### Base de Données

L'application utilise SQLite par défaut. La base de données est créée automatiquement au premier démarrage dans le fichier `banghalau.db`.

## Utilisation

1. **Connexion:** Utilisez admin/admin123 pour la première connexion
2. **Tableau de Bord:** Vue d'ensemble des statistiques
3. **Bungalows:** Gestion des bungalows disponibles
4. **Personnel:** Gestion du personnel militaire
5. **Distributions:** Assignation des bungalows au personnel

## 🔧 Dépannage

### Problème de Connexion
Si vous ne pouvez pas vous connecter avec admin/admin123:

1. **Réinitialiser la base de données:**
   ```bash
   python setup_db.py
   ```

2. **Vérifier que la base de données existe:**
   - Le fichier `banghalau.db` doit être présent dans le répertoire racine
   - Si absent, exécutez `python setup_db.py`

3. **Problème de mot de passe:**
   - Utilisez exactement: `admin` (nom d'utilisateur) et `admin123` (mot de passe)
   - Respectez la casse (minuscules)

### Problèmes de Démarrage
1. **Python non trouvé:**
   - Installez Python 3.11 ou plus récent
   - Ajoutez Python au PATH système

2. **Modules manquants:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Port déjà utilisé:**
   - Changez le port dans les variables d'environnement
   - Ou arrêtez l'autre application utilisant le port 5000

### Réinitialisation Complète
Pour repartir à zéro:
```bash
# Supprimer la base de données
del banghalau.db  # Windows
rm banghalau.db   # Linux/Mac

# Recréer la base de données
python setup_db.py

# Redémarrer l'application
python run.py
```

## Technologies Utilisées

- **Backend:** Python Flask
- **Frontend:** HTML5, CSS3, JavaScript, Bootstrap 5
- **Base de données:** SQLite
- **Graphiques:** Chart.js
- **Icons:** Font Awesome 6.4.0
- **Animations:** Animate.css
- **Polices:** Google Fonts (Inter)
- **Effets visuels:** CSS3 Gradients, Backdrop Filter, Animations

## Nouvelles Fonctionnalités de l'Interface

### 🎨 Design Moderne
- **Interface Glassmorphism** avec effets de flou et transparence
- **Gradients colorés** pour un look moderne et professionnel
- **Animations fluides** pour une expérience utilisateur améliorée
- **Design responsive** optimisé pour tous les appareils

### ✨ Améliorations Visuelles
- **Cartes statistiques animées** avec effets de survol
- **Tableaux interactifs** avec animations de ligne
- **Boutons avec effets de brillance** et transitions 3D
- **Badges colorés** avec ombres et gradients
- **Sidebar moderne** avec navigation fluide

### 🌐 Localisation Française
- **Interface entièrement en français**
- **Messages d'erreur traduits**
- **Format de date français**
- **Navigation et menus en français**
- **Documentation en français**

### 📱 Expérience Mobile
- **Design responsive** adaptatif
- **Navigation mobile optimisée**
- **Cartes empilées** sur petits écrans
- **Boutons tactiles** agrandis
- **Menu latéral coulissant**

### 🎯 Fonctionnalités Interactives
- **Horloge en temps réel** sur le tableau de bord
- **Indicateurs de statut animés** pour les distributions
- **Chargement avec spinners** pour les données AJAX
- **Messages d'état visuels** avec icônes
- **Tooltips informatifs** sur les éléments

### 🎛️ Sidebar Menu Avancé
- **Navigation moderne** avec animations fluides
- **Compteurs en temps réel** pour chaque section
- **Badges colorés** avec animations de mise à jour
- **Actions rapides** pour les tâches fréquentes
- **Effets de survol** avec transformations 3D
- **Design responsive** avec overlay mobile
- **Indicateurs de statut** visuels
- **Effets ripple** sur les clics
- **Auto-collapse** sur mobile après navigation
- **Mise à jour automatique** des statistiques

## 🔧 Opérations CRUD Complètes

### ✅ **Problèmes Résolus:**
Tous les problèmes d'ajout, modification et suppression ont été corrigés avec:

### 🏠 **Gestion des Bungalows:**
- ✅ **Ajouter** un nouveau bungalow avec validation
- ✅ **Modifier** les informations d'un bungalow existant
- ✅ **Supprimer** un bungalow (avec vérification des dépendances)
- ✅ **Validation** en temps réel des formulaires
- ✅ **Messages d'erreur** informatifs en français

### 👥 **Gestion du Personnel:**
- ✅ **Ajouter** un nouveau personnel militaire
- ✅ **Modifier** les informations du personnel
- ✅ **Supprimer** un personnel (avec vérification des distributions actives)
- ✅ **Sélection** des grades et unités depuis la base de données
- ✅ **Validation** des emails et numéros de téléphone

### 📋 **Gestion des Distributions:**
- ✅ **Créer** une nouvelle distribution
- ✅ **Modifier** une distribution existante
- ✅ **Supprimer** une distribution
- ✅ **Vérification** de disponibilité des bungalows
- ✅ **Gestion** des conflits de dates

### 🛡️ **Sécurité et Validation:**
- ✅ **Validation côté client** avec JavaScript
- ✅ **Validation côté serveur** avec Python
- ✅ **Vérification des dépendances** avant suppression
- ✅ **Messages d'erreur** contextuels
- ✅ **Confirmation** pour les actions destructives

### 📊 **APIs Fonctionnelles:**
- ✅ `/api/bungalows/count` - Nombre de bungalows
- ✅ `/api/personnel/count` - Nombre de personnel
- ✅ `/api/distributions/count` - Nombre de distributions
- ✅ `/api/recent_distributions` - Distributions récentes
- ✅ Mise à jour automatique des compteurs

## 🗑️ Nettoyage et Optimisation

### **Fichiers Supprimés (Interface Ancienne):**
- ❌ `templates/dashboard.html` (ancienne version)
- ❌ `templates/base.html` (ancienne version)
- ❌ `templates/login.html` (ancienne version)
- ❌ `templates/bungalows.html` (ancienne version)
- ❌ `templates/personnel.html` (ancienne version)
- ❌ `templates/distributions.html` (ancienne version)
- ❌ `static/css/style.css` (ancienne version)
- ❌ `static/js/main.js` (ancienne version)
- ❌ `start_modern.bat` (fichier temporaire)

### **Fichiers Renommés (Interface Moderne):**
- ✅ `base_new.html` → `base.html`
- ✅ `dashboard_new.html` → `dashboard.html`
- ✅ `login_new.html` → `login.html`
- ✅ `bungalows_new.html` → `bungalows.html`
- ✅ `modern-style.css` → `style.css`
- ✅ `modern-app.js` → `main.js`

### **Fichiers Ajoutés (Fonctionnalités CRUD):**
- ✅ `templates/add_bungalow.html`
- ✅ `templates/edit_bungalow.html`
- ✅ `templates/add_personnel.html`
- ✅ `templates/edit_personnel.html`
- ✅ `templates/personnel.html` (nouvelle version)
- ✅ `templates/distributions.html` (nouvelle version)

## 🏛️ Nouvelle Fonctionnalité: Gestion des Unités Militaires

### **Fonctionnalités Complètes:**
- ✅ **CRUD Complet** pour les unités militaires
- ✅ **Importation Excel** avec validation des données
- ✅ **Exportation Excel** des unités existantes
- ✅ **Validation des dépendances** avant suppression
- ✅ **Interface moderne** avec recherche et filtres
- ✅ **Compteurs en temps réel** dans la navigation

### **Structure des Unités:**
- 🔢 **Numéro** - Identifiant unique (ex: U001)
- 📝 **Description** - Nom complet de l'unité
- 🏷️ **Raccourci** - Abréviation (ex: INF, CAV, ART)
- 👥 **Personnel** - Nombre de personnel assigné

### **Importation Excel:**
- 📊 **Format supporté:** .xlsx, .xls
- 📋 **Colonnes requises:** numero, description
- 📋 **Colonne optionnelle:** raccourci
- ✅ **Validation automatique** des données
- 🔄 **Gestion des doublons** intelligente
- 📈 **Rapport d'importation** détaillé

### **Nouvelles Pages:**
- ✅ `/unites` - Liste des unités avec statistiques
- ✅ `/unites/add` - Ajouter une nouvelle unité
- ✅ `/unites/edit/<id>` - Modifier une unité existante
- ✅ `/unites/import` - Importer depuis Excel
- ✅ `/api/unites/export` - Exporter vers Excel

### **APIs Ajoutées:**
- ✅ `/api/unites/count` - Nombre d'unités
- ✅ `/api/unites/<id>` - Détails d'une unité
- ✅ `/api/unites/<id>/personnel` - Personnel d'une unité
- ✅ `/api/unites/export` - Exportation Excel

### **Installation du Support Excel:**

Pour utiliser les fonctionnalités d'importation/exportation Excel:

```bash
# Option 1: Installation automatique
install_excel_support.bat

# Option 2: Installation manuelle
pip install pandas==2.1.1
pip install openpyxl==3.1.2
pip install xlrd==2.0.1

# Option 3: Installation depuis requirements.txt
pip install -r requirements.txt
```

### **Utilisation:**

#### **Importation d'Unités:**
1. Préparez un fichier Excel avec les colonnes:
   - `numero` (obligatoire) - Ex: U001, U002
   - `description` (obligatoire) - Ex: 1er Régiment d'Infanterie
   - `raccourci` (optionnel) - Ex: INF, CAV, ART

2. Allez sur `/unites/import`
3. Sélectionnez votre fichier Excel
4. Cliquez sur "Importer les Unités"

#### **Exportation d'Unités:**
1. Allez sur `/unites`
2. Cliquez sur "Exporter"
3. Le fichier Excel sera téléchargé automatiquement

### **Exemples d'Unités:**
- **U001** - 1er Régiment d'Infanterie (INF)
- **U002** - 2ème Escadron de Cavalerie (CAV)
- **U003** - 3ème Groupe d'Artillerie (ART)
- **U004** - Bataillon du Génie (GEN)
- **U005** - Compagnie de Transmissions (TRANS)
- **U006** - Service de Santé Militaire (SSM)

## ⭐ Nouvelle Fonctionnalité: Gestion des Grades Militaires

### **Fonctionnalités Complètes:**
- ✅ **CRUD Complet** pour les grades militaires
- ✅ **Hiérarchie à 5 niveaux** selon l'organisation militaire
- ✅ **Recherche avancée** par nom ou description
- ✅ **Filtrage par niveau** hiérarchique
- ✅ **Validation des dépendances** avant suppression
- ✅ **Interface moderne** avec icônes par niveau

### **Structure des Grades:**
- ⭐ **grade** - Nom du grade (ex: Capitaine, Sergent)
- 📝 **description** - Description détaillée (optionnel)
- 🏆 **niveau** - Niveau hiérarchique (1-5)
- 👥 **personnel_count** - Nombre de personnel avec ce grade

### **Hiérarchie Militaire (5 Niveaux):**

#### **Niveau 1 - Soldats et Caporaux:**
- 🔹 Soldat - Soldat de base
- 🔹 Caporal - Caporal chef d'équipe
- 🔹 Caporal-Chef - Caporal-Chef expérimenté

#### **Niveau 2 - Sous-officiers:**
- 🔸 Sergent - Sergent chef de section
- 🔸 Sergent-Chef - Sergent-Chef expérimenté
- 🔸 Adjudant - Adjudant sous-officier supérieur

#### **Niveau 3 - Officiers subalternes:**
- 🔷 Sous-Lieutenant - Sous-Lieutenant débutant
- 🔷 Lieutenant - Lieutenant chef de peloton
- 🔷 Capitaine - Capitaine commandant de compagnie

#### **Niveau 4 - Officiers supérieurs:**
- 🟡 Commandant - Commandant chef de bataillon
- 🟡 Lieutenant-Colonel - Lieutenant-Colonel adjoint
- 🟡 Colonel - Colonel chef de régiment

#### **Niveau 5 - Officiers généraux:**
- 🔴 Général de Brigade - Général de Brigade
- 🔴 Général de Division - Général de Division
- 🔴 Général de Corps d'Armée - Général de Corps d'Armée

### **Nouvelles Pages:**
- ✅ `/grades` - Liste des grades avec recherche et filtres
- ✅ `/grades/add` - Ajouter un nouveau grade
- ✅ `/grades/edit/<id>` - Modifier un grade existant
- ✅ `/grades/delete/<id>` - Supprimer un grade

### **APIs Ajoutées:**
- ✅ `/api/grades/count` - Nombre de grades
- ✅ `/api/grades/<id>` - Détails d'un grade
- ✅ `/api/grades/<id>/personnel` - Personnel avec ce grade

### **Fonctionnalités de Recherche:**
- 🔍 **Recherche textuelle** dans les noms et descriptions
- 🏆 **Filtrage par niveau** hiérarchique
- 📊 **Tri automatique** par niveau puis par nom
- 📈 **Compteurs en temps réel** pour chaque grade

### **Sécurité et Validation:**
- 🛡️ **Vérification des dépendances** avant suppression
- ✅ **Validation des niveaux** (1-5 uniquement)
- 🚫 **Prévention des doublons** de noms de grades
- 📝 **Messages d'erreur** informatifs en français

## 📅 Nouvelle Fonctionnalité: Gestion des Sessions de Formation

### **Fonctionnalités Complètes:**
- ✅ **CRUD Complet** pour les sessions de formation
- ✅ **Gestion des statuts** avec 4 états différents
- ✅ **Recherche avancée** par numéro, description ou statut
- ✅ **Filtrage par statut** pour un suivi efficace
- ✅ **Validation des dépendances** avant suppression
- ✅ **Interface moderne** avec calendrier intégré

### **Structure des Sessions:**
- 🔢 **numero** - Numéro de session (ex: S001, FORM-2024-01)
- 📝 **description** - Description détaillée de la session
- 📅 **date_debut** - Date de début (obligatoire)
- 📅 **date_fin** - Date de fin (optionnel)
- 📊 **etat_session** - Statut de la session
- 🏠 **distribution_count** - Nombre de distributions liées

### **Statuts de Session (4 États):**

#### **📋 Planifiée:**
- 🔵 **Badge bleu** avec icône horloge
- 📝 **Description:** Session programmée mais pas encore commencée
- 🎯 **Usage:** Nouvelles sessions en préparation

#### **▶️ En cours:**
- 🟢 **Badge vert** avec icône play
- 📝 **Description:** Session actuellement active
- 🎯 **Usage:** Sessions en cours d'exécution

#### **✅ Terminée:**
- ⚫ **Badge gris** avec icône check
- 📝 **Description:** Session complétée avec succès
- 🎯 **Usage:** Sessions finalisées

#### **❌ Annulée:**
- 🔴 **Badge rouge** avec icône X
- 📝 **Description:** Session annulée ou interrompue
- 🎯 **Usage:** Sessions qui n'ont pas eu lieu

### **Types de Sessions Suggérés:**

#### **🎓 Sessions de Formation:**
- **FORM-2024-01** - Formation Initiale des Recrues
- **FORM-2024-02** - Formation Continue des Sous-Officiers
- **FORM-2024-03** - Stage de Perfectionnement Officiers

#### **🛡️ Sessions Spécialisées:**
- **SPEC-2024-01** - Formation Sécurité et Défense
- **SPEC-2024-02** - Formation Logistique Militaire
- **SPEC-2024-03** - Formation Communication Tactique

#### **💪 Sessions d'Entraînement:**
- **ENTR-2024-01** - Entraînement Physique Intensif
- **ENTR-2024-02** - Exercices de Terrain
- **ENTR-2024-03** - Simulation de Combat

#### **📈 Sessions de Perfectionnement:**
- **PERF-2024-01** - Leadership et Management
- **PERF-2024-02** - Techniques Avancées de Combat
- **PERF-2024-03** - Formation aux Nouvelles Technologies

### **Nouvelles Pages:**
- ✅ `/sessions` - Liste des sessions avec recherche et filtres
- ✅ `/sessions/add` - Ajouter une nouvelle session
- ✅ `/sessions/edit/<id>` - Modifier une session existante
- ✅ `/sessions/delete/<id>` - Supprimer une session

### **APIs Ajoutées:**
- ✅ `/api/sessions/count` - Nombre de sessions
- ✅ `/api/sessions/<id>` - Détails d'une session
- ✅ `/api/sessions/<id>/distributions` - Distributions d'une session

### **Fonctionnalités de Recherche:**
- 🔍 **Recherche textuelle** dans numéros, descriptions et statuts
- 📊 **Filtrage par statut** (Planifiée, En cours, Terminée, Annulée)
- 📅 **Tri par date** (plus récentes en premier)
- 📈 **Compteurs en temps réel** pour chaque session

### **Sécurité et Validation:**
- 🛡️ **Vérification des dépendances** avant suppression
- ✅ **Validation des dates** (fin après début)
- 🚫 **Prévention des doublons** de numéros de sessions
- 📝 **Messages d'erreur** informatifs en français

### **Intégration avec les Distributions:**
- 🔗 **Liaison automatique** avec les distributions de bungalows
- 📊 **Comptage des distributions** par session
- 🚫 **Protection contre suppression** si distributions actives
- 📈 **Statistiques en temps réel** dans l'interface
