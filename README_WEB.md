# 🏠 BANGHALAU - نظام إدارة توزيع البنغالوهات (إصدار الويب)

## 📋 نظرة عامة
نظام BANGHALAU هو تطبيق ويب شامل لإدارة توزيع البنغالوهات للأفراد العسكريين. يوفر النظام واجهة ويب سهلة الاستخدام لإدارة البنغالوهات والأفراد وعمليات التوزيع.

## 🚀 التشغيل السريع

### الطريقة الأولى: استخدام ملف التشغيل
1. انقر نقراً مزدوجاً على ملف `تشغيل_BANGHALAU.bat`
2. انتظر حتى يتم تحميل النظام
3. سيتم فتح المتصفح تلقائياً على الرابط: http://localhost:5000

### الطريقة الثانية: التشغيل اليدوي
```bash
# تشغيل النظام
py start_banghalau.py

# أو
python start_banghalau.py
```

## 🔐 بيانات الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 🌐 الوصول للنظام
- **الرابط الرئيسي:** http://localhost:5000
- **صفحة تسجيل الدخول:** http://localhost:5000/login

## 📦 المتطلبات
- Python 3.7 أو أحدث
- Flask (سيتم تثبيته تلقائياً)
- متصفح ويب حديث

## 🛠️ التثبيت اليدوي للمتطلبات
```bash
# تثبيت Flask
pip install flask

# أو باستخدام py
py -m pip install flask
```

## 📁 هيكل المشروع
```
BANGHALAU/
├── start_banghalau.py          # ملف التشغيل الرئيسي
├── تشغيل_BANGHALAU.bat         # ملف التشغيل للويندوز
├── app.py                      # التطبيق الرئيسي
├── banghalau.db               # قاعدة البيانات
├── database/                  # مجلد قاعدة البيانات
├── templates/                 # قوالب HTML
├── static/                    # الملفات الثابتة (CSS, JS)
└── models/                    # نماذج البيانات
```

## 🎯 الميزات الرئيسية

### 🏠 إدارة البنغالوهات
- إضافة بنغالوهات جديدة
- تعديل بيانات البنغالوهات
- حذف البنغالوهات
- عرض حالة الإشغال

### 👥 إدارة الأفراد
- تسجيل الأفراد العسكريين
- إدارة الرتب والوحدات
- تعديل بيانات الأفراد
- البحث والفلترة

### 📋 إدارة التوزيع
- إنشاء توزيعات جديدة
- تتبع فترات الإشغال
- إدارة الجلسات
- التقارير والإحصائيات

### 📊 لوحة التحكم
- إحصائيات شاملة
- معدل الإشغال
- عدد البنغالوهات والأفراد
- التوزيعات النشطة

## 🔧 استكشاف الأخطاء

### مشكلة: Python غير متاح
**الحل:** تأكد من تثبيت Python من الموقع الرسمي: https://python.org

### مشكلة: Flask غير مثبت
**الحل:** 
```bash
py -m pip install flask
```

### مشكلة: المنفذ 5000 مستخدم
**الحل:** أغلق أي تطبيقات أخرى تستخدم المنفذ 5000 أو غير المنفذ في الكود

### مشكلة: قاعدة البيانات غير موجودة
**الحل:** سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول

## 🔄 إعادة تشغيل النظام
لإعادة تشغيل النظام:
1. اضغط `Ctrl+C` في نافذة الأوامر لإيقاف الخادم
2. شغل الملف مرة أخرى

## 📱 الوصول من أجهزة أخرى
لجعل النظام متاحاً من أجهزة أخرى في الشبكة المحلية:
1. غير `host='127.0.0.1'` إلى `host='0.0.0.0'` في ملف `start_banghalau.py`
2. استخدم عنوان IP الخاص بالجهاز بدلاً من localhost

## 🛡️ الأمان
- غير كلمة المرور الافتراضية في بيئة الإنتاج
- استخدم HTTPS في بيئة الإنتاج
- قم بعمل نسخ احتياطية منتظمة لقاعدة البيانات

## 📞 الدعم الفني
في حالة مواجهة أي مشاكل:
1. تأكد من تثبيت Python بشكل صحيح
2. تحقق من توفر Flask
3. تأكد من عدم استخدام المنفذ 5000 من تطبيق آخر
4. راجع رسائل الخطأ في نافذة الأوامر

## 📝 ملاحظات مهمة
- النظام مصمم للاستخدام المحلي أو في الشبكة المحلية
- قاعدة البيانات SQLite مناسبة للاستخدام المتوسط
- للاستخدام المكثف، يُنصح بالترقية إلى قاعدة بيانات أكثر قوة

---
© 2024 BANGHALAU - نظام إدارة توزيع البنغالوهات
