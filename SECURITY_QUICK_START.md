# 🔐 دليل البدء السريع - نظام حماية BANGHALAU

## 🚀 البدء السريع

### **1. تثبيت المتطلبات:**
```bash
python install_security.py
```

### **2. تشغيل التطبيق:**
```bash
python app.py
```

### **3. الوصول لإدارة الأمان:**
- افتح المتصفح: `http://localhost:5000`
- سجل الدخول: `admin` / `admin123`
- انتقل إلى: **إدارة الأمان** في الشريط الجانبي

### **4. تشغيل مدير الأمان (اختياري):**
```bash
python security_manager.py
# أو
run_security.bat
```

## 🔒 الميزات الأمنية الجديدة

### **✅ تم تطبيقه:**
- 🔐 **تشفير البيانات الحساسة**
- 🛡️ **مراقبة الوصول في الوقت الفعلي**
- 🚫 **حظر IP التلقائي** (5 محاولات فاشلة)
- 💾 **نسخ احتياطية مشفرة**
- 📊 **تسجيل شامل للأحداث**
- 🔑 **سياسات كلمات مرور قوية**
- 🚨 **كشف التهديدات**

### **📊 واجهة إدارة الأمان:**
- عرض حالة الحماية
- إدارة عناوين IP المحظورة
- إنشاء نسخ احتياطية مشفرة
- مراقبة الأحداث الأمنية
- تقارير الأمان المفصلة

## 🔧 الإعدادات الافتراضية

### **سياسة كلمات المرور:**
- الطول الأدنى: 12 حرف
- أحرف كبيرة وصغيرة: مطلوبة
- أرقام ورموز خاصة: مطلوبة

### **حماية IP:**
- الحد الأقصى للمحاولات الفاشلة: 5
- مدة الحظر: 5 دقائق
- مراقبة تلقائية للأنشطة المشبوهة

### **الجلسات:**
- مدة الجلسة: ساعة واحدة
- حماية من CSRF
- كوكيز آمنة

## 📁 الملفات المهمة

### **ملفات النظام:**
- `database/security.py` - وحدة الأمان الأساسية
- `database/protection.py` - وحدة الحماية المتقدمة
- `security_manager.py` - مدير الأمان
- `templates/security_dashboard.html` - واجهة إدارة الأمان

### **ملفات الإعدادات:**
- `security_config.json` - إعدادات الأمان
- `database_audit.log` - سجل الأحداث الأمنية
- `protection.log` - سجل الحماية
- `access.log` - سجل الوصول
- `threats.log` - سجل التهديدات

### **المجلدات:**
- `backups/` - النسخ الاحتياطية المشفرة
- `snapshots/` - لقطات قاعدة البيانات
- `logs/` - ملفات السجل

## 🚨 التنبيهات المهمة

### **⚠️ تغيير كلمة المرور الافتراضية:**
```
المستخدم: admin
كلمة المرور الحالية: admin123
```
**يُنصح بشدة بتغيير كلمة المرور الافتراضية فوراً!**

### **🔒 النسخ الاحتياطية:**
- إنشاء نسخة احتياطية مشفرة يومياً
- حفظ كلمات مرور التشفير في مكان آمن
- اختبار استعادة النسخ دورياً

### **📊 المراقبة:**
- مراجعة سجل الأحداث الأمنية يومياً
- التحقق من عناوين IP المحظورة
- مراقبة مؤشرات الأمان

## 🆘 الدعم والمساعدة

### **في حالة المشاكل:**
1. تحقق من ملفات السجل في مجلد `logs/`
2. راجع إعدادات الأمان في `security_config.json`
3. استخدم مدير الأمان لتشخيص المشاكل

### **الاستعادة الطارئة:**
```bash
# إيقاف الحماية مؤقتاً (للطوارئ فقط)
python -c "
from database.protection import DatabaseProtection
db_protection = DatabaseProtection()
db_protection.stop_monitoring()
print('تم إيقاف المراقبة مؤقتاً')
"
```

## 📞 معلومات الاتصال

للدعم التقني أو الاستفسارات الأمنية، يرجى مراجعة:
- `DATABASE_SECURITY_IMPLEMENTATION.md` - التوثيق الشامل
- `security_manager.py` - أدوات الإدارة المتقدمة

---

**🔐 نظام BANGHALAU محمي الآن بأحدث تقنيات الأمان!**
