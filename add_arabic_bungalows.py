#!/usr/bin/env python3
"""
Script pour ajouter des bungalows avec des noms arabes
"""

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def add_arabic_bungalows():
    """Ajouter des bungalows avec des noms arabes"""

    # بيانات بنغالوهات عربية للاختبار
    bungalows_data = [
        {
            'numero': 'B001',
            'endroit': 'المنطقة الشمالية - قطاع الضباط',
            'capacite': 4,
            'caracteristiques': 'مكيف الهواء، حديقة خاصة، إطلالة على البحر، مفروش بالكامل'
        },
        {
            'numero': 'B002',
            'endroit': 'المنطقة الجنوبية - قطاع الصف',
            'capacite': 6,
            'caracteristiques': 'مكيف الهواء، موقف سيارات، قريب من الخدمات'
        },
        {
            'numero': 'B003',
            'endroit': 'المنطقة الوسطى - قطاع العائلات',
            'capacite': 8,
            'caracteristiques': 'مساحة واسعة، حديقة للأطفال، أمان 24 ساعة'
        },
        {
            'numero': 'B004',
            'endroit': 'المنطقة الشرقية - قطاع الضيوف',
            'capacite': 2,
            'caracteristiques': 'فاخر، خدمة الغرف، واي فاي مجاني'
        }
    ]

    # إضافة البيانات إلى قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    success_count = 0
    error_count = 0

    for bungalow in bungalows_data:
        try:
            bungalow_id = db_ops.create_bungalow(
                numero=bungalow['numero'],
                endroit=bungalow['endroit'],
                capacite=bungalow['capacite'],
                caracteristiques=bungalow['caracteristiques']
            )
            if bungalow_id:
                print(f'✅ تم إضافة البنغالو {bungalow["numero"]} بنجاح (ID: {bungalow_id})')
                success_count += 1
            else:
                print(f'❌ خطأ في إضافة البنغالو {bungalow["numero"]}: البنغالو موجود مسبقاً')
                error_count += 1
        except Exception as e:
            print(f'❌ خطأ في البنغالو {bungalow["numero"]}: {str(e)}')
            error_count += 1

    db_manager.close()

    print(f'\n📊 النتائج:')
    print(f'✅ نجح: {success_count}')
    print(f'❌ أخطاء: {error_count}')
    print('🎉 تم الانتهاء من إضافة البنغالوهات العربية')

if __name__ == "__main__":
    add_arabic_bungalows()
