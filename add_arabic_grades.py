#!/usr/bin/env python3
"""
Script pour ajouter des grades avec des noms arabes
"""

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def add_arabic_grades():
    """Ajouter des grades avec des noms arabes"""
    
    # بيانات رتب عربية للاختبار
    grades_data = [
        {
            'grade': 'عقيد',
            'description': 'ضابط برتبة عقيد، يقود فوج أو وحدة كبيرة'
        },
        {
            'grade': 'مقدم',
            'description': 'ضابط برتبة مقدم، نائب قائد الفوج أو قائد كتيبة'
        },
        {
            'grade': 'رائد',
            'description': 'ضابط برتبة رائد، قائد سرية أو مساعد في الكتيبة'
        },
        {
            'grade': 'نقيب',
            'description': 'ضابط برتبة نقيب، قائد فصيلة أو مساعد في السرية'
        },
        {
            'grade': 'ملازم أول',
            'description': 'ضابط برتبة ملازم أول، قائد مجموعة أو مساعد في الفصيلة'
        },
        {
            'grade': 'ملازم',
            'description': 'ضابط برتبة ملازم، ضابط مبتدئ في الوحدة'
        },
        {
            'grade': 'رقيب أول',
            'description': 'صف ضابط برتبة رقيب أول، مساعد قائد الفصيلة'
        },
        {
            'grade': 'رقيب',
            'description': 'صف ضابط برتبة رقيب، قائد مجموعة صغيرة'
        },
        {
            'grade': 'عريف أول',
            'description': 'عريف أول، مساعد الرقيب في قيادة المجموعة'
        },
        {
            'grade': 'عريف',
            'description': 'عريف، قائد فريق صغير من الجنود'
        },
        {
            'grade': 'جندي أول',
            'description': 'جندي أول، جندي ذو خبرة في الوحدة'
        },
        {
            'grade': 'جندي',
            'description': 'جندي، الرتبة الأساسية في الجيش'
        }
    ]

    # إضافة البيانات إلى قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    success_count = 0
    error_count = 0

    for grade_data in grades_data:
        try:
            grade_id = db_ops.create_grade(
                grade=grade_data['grade'],
                description=grade_data['description']
            )
            
            if grade_id:
                print(f'✅ تم إضافة الرتبة {grade_data["grade"]} بنجاح (ID: {grade_id})')
                success_count += 1
            else:
                print(f'❌ خطأ في إضافة الرتبة {grade_data["grade"]}: الرتبة موجودة مسبقاً')
                error_count += 1
        except Exception as e:
            print(f'❌ خطأ في إضافة الرتبة {grade_data["grade"]}: {str(e)}')
            error_count += 1

    db_manager.close()
    
    print(f'\n📊 النتائج:')
    print(f'✅ نجح: {success_count}')
    print(f'❌ أخطاء: {error_count}')
    print('🎉 تم الانتهاء من إضافة الرتب العربية')

if __name__ == "__main__":
    add_arabic_grades()
