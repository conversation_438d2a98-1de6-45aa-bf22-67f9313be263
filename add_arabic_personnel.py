#!/usr/bin/env python3
"""
Script pour ajouter du personnel avec des noms arabes
"""

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def add_arabic_personnel():
    """Ajouter du personnel avec des noms arabes"""

    # بيانات أفراد عربية للاختبار
    personnel_data = [
        {
            'numero': 'M001',
            'nom': 'أحمد',
            'prenom': 'محمد',
            'grade_id': 1,  # سنحتاج لإنشاء رتب أولاً
            'unite_id': 1,  # سنحتاج لإنشاء وحدات أولاً
            'telephone': '+213 555 123 456',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M002',
            'nom': 'فاطمة',
            'prenom': 'زهرة',
            'grade_id': 1,
            'unite_id': 1,
            'telephone': '+213 555 234 567',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M003',
            'nom': 'عبد الرحمن',
            'prenom': 'خالد',
            'grade_id': 1,
            'unite_id': 1,
            'telephone': '+213 555 345 678',
            'email': '<EMAIL>'
        }
    ]

    # إضافة البيانات إلى قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    success_count = 0
    error_count = 0

    # أولاً، دعنا نتحقق من وجود رتب ووحدات
    grades = db_ops.list_grades()
    unites = db_ops.list_unites()

    if not grades:
        print("❌ لا توجد رتب في قاعدة البيانات. يجب إضافة رتب أولاً.")
        db_manager.close()
        return

    if not unites:
        print("❌ لا توجد وحدات في قاعدة البيانات. يجب إضافة وحدات أولاً.")
        db_manager.close()
        return

    # استخدام أول رتبة ووحدة متاحة
    first_grade_id = grades[0]['id']
    first_unite_id = unites[0]['id']

    print(f"📊 سيتم استخدام الرتبة: {grades[0]['grade']}")
    print(f"📊 سيتم استخدام الوحدة: {unites[0]['description']}")

    for person in personnel_data:
        try:
            # تحديث معرفات الرتبة والوحدة
            person['grade_id'] = first_grade_id
            person['unite_id'] = first_unite_id

            person_id = db_ops.create_personnel_militaire(
                matricule=person['numero'],
                nom=person['nom'],
                prenom=person['prenom'],
                grade_id=person['grade_id'],
                unite_id=person['unite_id'],
                numero=person['numero']
            )

            if person_id:
                print(f'✅ تم إضافة الفرد {person["nom"]} {person["prenom"]} بنجاح (ID: {person_id})')
                success_count += 1
            else:
                print(f'❌ خطأ في إضافة الفرد {person["nom"]} {person["prenom"]}: الرقم موجود مسبقاً')
                error_count += 1
        except Exception as e:
            print(f'❌ خطأ في إضافة الفرد {person["nom"]} {person["prenom"]}: {str(e)}')
            error_count += 1

    db_manager.close()

    print(f'\n📊 النتائج:')
    print(f'✅ نجح: {success_count}')
    print(f'❌ أخطاء: {error_count}')
    print('🎉 تم الانتهاء من إضافة الأفراد العرب')

if __name__ == "__main__":
    add_arabic_personnel()
