#!/usr/bin/env python3
"""
Script pour ajouter des distributions de bungalows pour les tests
"""

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations
from datetime import datetime, timedelta

def add_bungalow_distributions():
    """Ajouter des distributions de bungalows pour les tests"""
    
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get available bungalows and personnel
    bungalows = db_ops.list_bungalows()
    personnel = db_ops.list_personnel_militaire()
    
    if not bungalows or not personnel:
        print("❌ Pas de bungalows ou de personnel disponibles")
        db_manager.close()
        return

    # Create some test distributions
    distributions_data = [
        {
            'numero': 'DIST001',
            'bungalow_id': bungalows[0]['id'] if len(bungalows) > 0 else None,
            'personnel_id': personnel[0]['id'] if len(personnel) > 0 else None,
            'session_id': None,
            'date_debut': '2024-01-01',
            'date_fin': '2024-01-31',
            'notes': 'Distribution de test pour janvier 2024'
        },
        {
            'numero': 'DIST002',
            'bungalow_id': bungalows[1]['id'] if len(bungalows) > 1 else bungalows[0]['id'],
            'personnel_id': personnel[1]['id'] if len(personnel) > 1 else personnel[0]['id'],
            'session_id': None,
            'date_debut': '2024-02-01',
            'date_fin': None,  # Distribution en cours
            'notes': 'Distribution en cours depuis février 2024'
        },
        {
            'numero': 'DIST003',
            'bungalow_id': bungalows[0]['id'] if len(bungalows) > 0 else None,
            'personnel_id': personnel[2]['id'] if len(personnel) > 2 else personnel[0]['id'],
            'session_id': None,
            'date_debut': '2024-03-01',
            'date_fin': '2024-03-15',
            'notes': 'Distribution courte de mars 2024'
        }
    ]

    success_count = 0
    error_count = 0

    for dist_data in distributions_data:
        try:
            if not dist_data['bungalow_id'] or not dist_data['personnel_id']:
                print(f"❌ Données manquantes pour {dist_data['numero']}")
                error_count += 1
                continue
                
            dist_id = db_ops.create_distribution_bungalow(
                numero=dist_data['numero'],
                bungalow_id=dist_data['bungalow_id'],
                personnel_id=dist_data['personnel_id'],
                session_id=dist_data['session_id'],
                date_debut=dist_data['date_debut'],
                date_fin=dist_data['date_fin'],
                notes=dist_data['notes']
            )
            
            if dist_id:
                print(f'✅ Distribution {dist_data["numero"]} créée avec succès (ID: {dist_id})')
                success_count += 1
            else:
                print(f'❌ Erreur lors de la création de {dist_data["numero"]}: numéro existant')
                error_count += 1
        except Exception as e:
            print(f'❌ Erreur pour {dist_data["numero"]}: {str(e)}')
            error_count += 1

    db_manager.close()
    
    print(f'\n📊 Résultats:')
    print(f'✅ Succès: {success_count}')
    print(f'❌ Erreurs: {error_count}')
    print('🎉 Fin de la création des distributions de test')

if __name__ == "__main__":
    add_bungalow_distributions()
