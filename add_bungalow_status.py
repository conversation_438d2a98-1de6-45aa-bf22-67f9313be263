#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Migration script to add status and status_notes columns to bungalows table
"""

import sqlite3
import os
from database.db_manager import DatabaseManager

def add_bungalow_status_columns():
    """Add status and status_notes columns to bungalows table"""
    
    print("🔄 Adding status columns to bungalows table...")
    
    # Connect to database
    db_manager = DatabaseManager()
    db_manager.connect()
    
    try:
        # Check if columns already exist
        cursor = db_manager.cursor
        cursor.execute("PRAGMA table_info(bungalows)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add status column if it doesn't exist
        if 'statut' not in columns:
            print("➕ Adding 'statut' column...")
            db_manager.execute('''
                ALTER TABLE bungalows 
                ADD COLUMN statut TEXT DEFAULT 'Disponible'
            ''')
            print("✅ 'statut' column added successfully")
        else:
            print("ℹ️  'statut' column already exists")
        
        # Add status_notes column if it doesn't exist
        if 'notes_statut' not in columns:
            print("➕ Adding 'notes_statut' column...")
            db_manager.execute('''
                ALTER TABLE bungalows 
                ADD COLUMN notes_statut TEXT
            ''')
            print("✅ 'notes_statut' column added successfully")
        else:
            print("ℹ️  'notes_statut' column already exists")
        
        # Update existing bungalows with default status
        print("🔄 Updating existing bungalows with default status...")
        db_manager.execute('''
            UPDATE bungalows 
            SET statut = 'Disponible' 
            WHERE statut IS NULL OR statut = ''
        ''')
        
        # Commit changes
        db_manager.commit()
        print("✅ Migration completed successfully!")
        
        # Show updated table structure
        print("\n📋 Updated bungalows table structure:")
        cursor.execute("PRAGMA table_info(bungalows)")
        columns = cursor.fetchall()
        for column in columns:
            print(f"   - {column[1]} ({column[2]})")
        
        # Show sample data
        print("\n📊 Sample bungalows with status:")
        cursor.execute("SELECT numero, endroit, statut, notes_statut FROM bungalows LIMIT 5")
        bungalows = cursor.fetchall()
        for bungalow in bungalows:
            print(f"   - {bungalow[0]}: {bungalow[1]} - Status: {bungalow[2]}")
            
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        db_manager.rollback()
        return False
    finally:
        db_manager.close()
    
    return True

def update_sample_bungalows():
    """Update some sample bungalows with different statuses for testing"""
    
    print("\n🎯 Updating sample bungalows with different statuses...")
    
    db_manager = DatabaseManager()
    db_manager.connect()
    
    try:
        # Sample status updates
        status_updates = [
            ('B001', 'Disponible', None),
            ('B002', 'Occupé', 'Occupé par la famille Dupont'),
            ('B003', 'Maintenance', 'Réparation de la climatisation'),
            ('B004', 'Réservé', 'Réservé pour la session d\'été'),
            ('B005', 'Disponible', None),
            ('B006', 'En Rénovation', 'Rénovation complète de la cuisine')
        ]
        
        for numero, statut, notes in status_updates:
            db_manager.execute('''
                UPDATE bungalows 
                SET statut = ?, notes_statut = ?
                WHERE numero = ?
            ''', (statut, notes, numero))
            print(f"   ✅ {numero}: {statut}")
        
        db_manager.commit()
        print("✅ Sample data updated successfully!")
        
    except Exception as e:
        print(f"❌ Error updating sample data: {e}")
        db_manager.rollback()
    finally:
        db_manager.close()

if __name__ == "__main__":
    print("🚀 BANGHALAU - Bungalow Status Migration")
    print("=" * 50)
    
    # Run migration
    if add_bungalow_status_columns():
        # Update sample data
        update_sample_bungalows()
        
        print("\n🎉 Migration completed successfully!")
        print("📝 You can now use the status field in bungalow forms.")
        print("🔧 Available statuses:")
        print("   - Disponible")
        print("   - Occupé") 
        print("   - Maintenance")
        print("   - Réservé")
        print("   - Hors Service")
        print("   - En Rénovation")
    else:
        print("\n❌ Migration failed!")
