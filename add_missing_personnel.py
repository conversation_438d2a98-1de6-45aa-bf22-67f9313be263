#!/usr/bin/env python3
"""
Script pour ajouter le personnel manquant avec les grades corrects
"""

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def add_missing_personnel():
    """Ajouter le personnel manquant avec les grades corrects"""
    
    # إضافة الأفراد الذين فشلوا من قبل
    missing_personnel_data = [
        {
            'numero': 'M030',
            'nom': 'سعد',
            'prenom': 'أحمد',
            'grade_name': 'عقيد ',  # مع مسافة
            'unite_description': 'قيادة الناحية',
            'telephone': '+213 555 111 222',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M031',
            'nom': 'خديجة',
            'prenom': 'فاطمة',
            'grade_name': 'رائد ',  # مع مسافة
            'unite_description': 'رئيس الأمانة',
            'telephone': '+213 555 333 444',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M032',
            'nom': 'محمد',
            'prenom': 'عبد الله',
            'grade_name': 'نقيب ',  # مع مسافة
            'unite_description': 'مصلحة التشريفات',
            'telephone': '+213 555 555 666',
            'email': '<EMAIL>'
        }
    ]

    # إضافة البيانات إلى قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    success_count = 0
    error_count = 0

    for person in missing_personnel_data:
        try:
            # البحث عن الرتبة
            grade = db_ops.get_grade_by_name(person['grade_name'])
            if not grade:
                print(f'❌ الرتبة {person["grade_name"]} غير موجودة')
                error_count += 1
                continue
                
            # البحث عن الوحدة
            unites = db_ops.list_unites()
            unite = None
            for u in unites:
                if u['description'] == person['unite_description']:
                    unite = u
                    break
                    
            if not unite:
                print(f'❌ الوحدة {person["unite_description"]} غير موجودة')
                error_count += 1
                continue
            
            person_id = db_ops.create_personnel_militaire(
                matricule=person['numero'],
                nom=person['nom'],
                prenom=person['prenom'],
                grade_id=grade['id'],
                unite_id=unite['id'],
                numero=person['numero']
            )
            
            if person_id:
                print(f'✅ تم إضافة الفرد {person["nom"]} {person["prenom"]} بنجاح (ID: {person_id})')
                print(f'   الرتبة: {person["grade_name"].strip()} | الوحدة: {person["unite_description"]}')
                success_count += 1
            else:
                print(f'❌ خطأ في إضافة الفرد {person["nom"]} {person["prenom"]}: الرقم موجود مسبقاً')
                error_count += 1
        except Exception as e:
            print(f'❌ خطأ في إضافة الفرد {person["nom"]} {person["prenom"]}: {str(e)}')
            error_count += 1

    db_manager.close()
    
    print(f'\n📊 النتائج:')
    print(f'✅ نجح: {success_count}')
    print(f'❌ أخطاء: {error_count}')
    print('🎉 تم الانتهاء من إضافة الأفراد المفقودين')

if __name__ == "__main__":
    add_missing_personnel()
