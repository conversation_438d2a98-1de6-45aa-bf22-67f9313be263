#!/usr/bin/env python3
"""
Script to add Region field to Personnel Militaire table
"""

import sqlite3
import os
from datetime import datetime

def add_region_field():
    """Add Region field to personnel_militaire table"""
    
    db_path = "banghalau.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file not found. Please run setup_db.py first.")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Adding Region field to Personnel Militaire table...")
        
        # Check if Region field already exists
        cursor.execute("PRAGMA table_info(personnel_militaire)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'region' in columns:
            print("ℹ️  Region field already exists in personnel_militaire table.")
            conn.close()
            return True
        
        # Add Region field
        cursor.execute("""
            ALTER TABLE personnel_militaire 
            ADD COLUMN region TEXT
        """)
        
        print("✅ Region field added successfully!")
        
        # Update existing records with default region values (optional)
        print("🔄 Setting default region values for existing personnel...")
        
        # You can customize these default regions based on your needs
        default_regions = [
            'الناحية العسكرية الأولى',
            'الناحية العسكرية الثانية', 
            'الناحية العسكرية الثالثة',
            'الناحية العسكرية الرابعة',
            'الناحية العسكرية الخامسة',
            'الناحية العسكرية السادسة'
        ]
        
        # Get all existing personnel
        cursor.execute("SELECT id FROM personnel_militaire WHERE region IS NULL")
        personnel_ids = cursor.fetchall()
        
        # Assign default regions to existing personnel (cycling through regions)
        for i, (personnel_id,) in enumerate(personnel_ids):
            region = default_regions[i % len(default_regions)]
            cursor.execute(
                "UPDATE personnel_militaire SET region = ? WHERE id = ?",
                (region, personnel_id)
            )
        
        if personnel_ids:
            print(f"✅ Updated {len(personnel_ids)} existing personnel records with default regions.")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("✅ Region field migration completed successfully!")
        print("\nRegion field details:")
        print("- Field name: region")
        print("- Type: TEXT")
        print("- Nullable: Yes")
        print("- Default regions assigned to existing personnel")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to add Region field: {e}")
        return False

def verify_region_field():
    """Verify that the Region field was added correctly"""
    
    db_path = "banghalau.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check table structure
        cursor.execute("PRAGMA table_info(personnel_militaire)")
        columns = cursor.fetchall()
        
        print("\n📋 Current personnel_militaire table structure:")
        print("-" * 60)
        for column in columns:
            print(f"  {column[1]:<15} {column[2]:<10} {'NOT NULL' if column[3] else 'NULL':<8} {f'DEFAULT {column[4]}' if column[4] else ''}")
        
        # Check if region field exists
        region_exists = any(column[1] == 'region' for column in columns)
        
        if region_exists:
            print("\n✅ Region field exists in personnel_militaire table!")
            
            # Show sample data
            cursor.execute("""
                SELECT id, nom, prenom, region 
                FROM personnel_militaire 
                LIMIT 5
            """)
            sample_data = cursor.fetchall()
            
            if sample_data:
                print("\n📊 Sample personnel data with regions:")
                print("-" * 60)
                for row in sample_data:
                    print(f"  ID: {row[0]:<3} | {row[1]} {row[2]:<15} | Region: {row[3] or 'Non définie'}")
        else:
            print("\n❌ Region field not found in personnel_militaire table!")
        
        conn.close()
        return region_exists
        
    except Exception as e:
        print(f"❌ Failed to verify Region field: {e}")
        return False

if __name__ == "__main__":
    print("🚀 BANGHALAU - Adding Region Field to Personnel Militaire")
    print("=" * 60)
    
    # Add the region field
    success = add_region_field()
    
    if success:
        # Verify the changes
        verify_region_field()
        
        print("\n🎯 Next steps:")
        print("1. Update templates to include Region field")
        print("2. Update database operations to handle Region")
        print("3. Update forms to allow Region input")
        print("4. Update Personnel model class")
        print("\nRun the application to test the changes:")
        print("  python app.py")
    else:
        print("\n❌ Migration failed. Please check the error messages above.")
