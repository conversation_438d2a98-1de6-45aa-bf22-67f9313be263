#!/usr/bin/env python
"""
BANGHALAU Web Application - Distribution Bungalows
PDF Download Button Added Successfully!
"""

from flask import Flask, render_template, request, redirect, url_for, jsonify, flash, session
from flask_login import Login<PERSON>anager, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
import sqlite3
import os
import json
import secrets
from datetime import datetime, timedelta
from functools import wraps

# Import database operations
from database.operations import DatabaseOperations
from database.db_manager import DatabaseManager
from database.security import DatabaseSecurity
from database.protection import DatabaseProtection

# Import API routes (commented out for now)
# from api import api

# Create Flask app
app = Flask(__name__)

# Generate secure secret key
app.secret_key = secrets.token_hex(32)  # Secure random key

# Enable debug mode for better error messages
app.config['DEBUG'] = True

# Configure session security
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=1)  # Reduced session time
app.config['SESSION_COOKIE_SECURE'] = False  # Set to True in production with HTTPS
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

# Initialize security systems
db_security = DatabaseSecurity()
db_protection = DatabaseProtection()

# Initialize database and create default user
def initialize_app():
    """Initialize the application and create default user if needed"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_manager.initialize_database()

    db_ops = DatabaseOperations(db_manager)

    # Check if any users exist
    users = db_ops.get_all_users()
    if not users:
        # Create default admin user
        default_user = {
            'username': 'admin',
            'password': generate_password_hash('admin123'),
            'email': '<EMAIL>',
            'role': 'admin',
            'is_active': True,
            'full_name': 'مدير النظام',
            'created_at': datetime.now()
        }

        user_id = db_ops.create_user(default_user)
        if user_id:
            print("Default admin user created: admin/admin123")
        else:
            print("Failed to create default admin user")

    db_manager.close()

# Initialize app on startup
initialize_app()

# Add error handler
@app.errorhandler(500)
def internal_error(error):
    import traceback
    print("Internal Server Error:")
    print(traceback.format_exc())
    return render_template('error.html', error="Internal Server Error"), 500

# Register API blueprint (commented out for now)
# app.register_blueprint(api, url_prefix='/api')

# Initialize login manager
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# User class for Flask-Login
class User:
    def __init__(self, id, username, password_hash):
        self.id = id
        self.username = username
        self.password_hash = password_hash
        self.is_authenticated = True
        self.is_active = True
        self.is_anonymous = False

    def get_id(self):
        return str(self.id)

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    user_data = db_ops.get_user(int(user_id))
    db_manager.close()

    if user_data:
        return User(user_data['id'], user_data['username'], user_data['password_hash'])
    return None

# Routes
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))  # Now redirects to modern dashboard
    return redirect(url_for('login'))

@app.route('/loading')
def loading():
    """Loading page - redirect to login"""
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        ip_address = request.remote_addr or 'localhost'

        # Check if IP is blocked
        if db_protection.is_ip_blocked(ip_address):
            db_protection.log_failed_attempt(username, ip_address, "IP blocked")
            flash('عذراً، تم حظر عنوان IP هذا مؤقتاً بسبب محاولات تسجيل دخول مشبوهة', 'error')
            return render_template('login.html')

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        user_data = db_ops.get_user_by_username(username)

        if user_data and check_password_hash(user_data.get('password_hash', user_data.get('password', '')), password):
            # Successful login
            user = User(user_data['id'], user_data['username'], user_data.get('password_hash', user_data.get('password', '')))
            login_user(user)
            session['user_id'] = user_data['id']
            session.permanent = True

            # Log successful access
            db_protection.log_access(user_data['id'], "LOGIN", ip_address, True)
            db_security.log_security_event("LOGIN_SUCCESS", user_data['id'], f"User {username} logged in from {ip_address}")

            # Update last login
            db_ops.update_user_last_login(user_data['id'])

            db_manager.close()
            return redirect(url_for('dashboard'))
        else:
            # Failed login
            db_protection.log_failed_attempt(username, ip_address, "Invalid credentials")
            db_security.log_security_event("LOGIN_FAILED", username, f"Failed login attempt from {ip_address}")
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

        db_manager.close()

    return render_template('login.html')

@app.route('/logout')
def logout():
    # Log logout event
    if 'user_id' in session:
        ip_address = request.remote_addr or 'localhost'
        db_security.log_security_event("LOGOUT", session['user_id'], f"User logged out from {ip_address}")

    logout_user()
    session.pop('user_id', None)
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get all statistics in optimized queries (Performance Optimization)
    stats = db_ops.get_dashboard_statistics()

    # Get recent activity (last 10 distributions) - optimized
    recent_distributions = db_ops.get_recent_distributions_optimized(10)

    db_manager.close()

    # Use modern dashboard as default
    return render_template('dashboard_modern_inspired.html',
                          bungalow_count=stats.get('bungalow_count', 0),
                          personnel_count=stats.get('personnel_count', 0),
                          distribution_count=stats.get('distribution_count', 0),
                          grades_count=stats.get('grades_count', 0),
                          unites_count=stats.get('unites_count', 0),
                          sessions_count=stats.get('sessions_count', 0),
                          users_count=stats.get('users_count', 0),
                          occupancy_rate=stats.get('occupancy_rate', 0),
                          recent_distributions=recent_distributions)

@app.route('/dashboard/elegant')
@login_required
def dashboard_elegant():
    """Elegant French dashboard - Optimized"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get all statistics in optimized queries (Performance Optimization)
    stats = db_ops.get_dashboard_statistics()

    # Get recent activity (last 10 distributions) - optimized
    recent_distributions = db_ops.get_recent_distributions_optimized(10)

    db_manager.close()

    return render_template('dashboard_elegant.html',
                          bungalow_count=stats.get('bungalow_count', 0),
                          personnel_count=stats.get('personnel_count', 0),
                          distribution_count=stats.get('distribution_count', 0),
                          grades_count=stats.get('grades_count', 0),
                          unites_count=stats.get('unites_count', 0),
                          sessions_count=stats.get('sessions_count', 0),
                          users_count=stats.get('users_count', 0),
                          occupancy_rate=stats.get('occupancy_rate', 0),
                          recent_distributions=recent_distributions)

@app.route('/dashboard_old')
@login_required
def dashboard_old():
    """Keep old dashboard for reference - Optimized"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get statistics in optimized way (Performance Optimization)
    stats = db_ops.get_dashboard_statistics()

    db_manager.close()

    return render_template('dashboard.html',
                          bungalow_count=stats.get('bungalow_count', 0),
                          personnel_count=stats.get('personnel_count', 0),
                          distribution_count=stats.get('distribution_count', 0),
                          occupancy_rate=stats.get('occupancy_rate', 0))

# Bungalow routes
@app.route('/bungalows')
@login_required
def bungalows():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get search parameter
    search_query = request.args.get('search', '').strip()

    # Use optimized search (Performance Optimization)
    if search_query:
        bungalows_list = db_ops.search_bungalows_optimized(search_query)
    else:
        bungalows_list = db_ops.list_bungalows_with_sessions()

    # Calculate statistics for the filtered results
    total_count = len(bungalows_list)
    available_count = sum(1 for b in bungalows_list if b.get('statut') == 'Disponible')
    occupied_count = sum(1 for b in bungalows_list if b.get('statut') == 'Occupé')
    maintenance_count = sum(1 for b in bungalows_list if b.get('statut') == 'Maintenance')

    db_manager.close()

    return render_template('bungalows.html',
                         bungalows=bungalows_list,
                         search_query=search_query,
                         total_count=total_count,
                         available_count=available_count,
                         occupied_count=occupied_count,
                         maintenance_count=maintenance_count)

@app.route('/bungalows/add', methods=['GET', 'POST'])
@login_required
def add_bungalow():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if request.method == 'POST':
        numero = request.form.get('numero')
        endroit = request.form.get('endroit')
        capacite = request.form.get('capacite')
        caracteristiques = request.form.get('caracteristiques')
        statut = request.form.get('statut')
        notes_statut = request.form.get('notes_statut')
        session_id = request.form.get('session_id')

        if not numero or not endroit or not capacite or not statut:
            flash('Tous les champs obligatoires doivent être remplis', 'error')
            sessions = db_ops.list_sessions()
            db_manager.close()
            return render_template('add_bungalow.html', sessions=sessions)

        try:
            capacite = int(capacite)
        except ValueError:
            flash('La capacité doit être un nombre entier', 'error')
            sessions = db_ops.list_sessions()
            db_manager.close()
            return render_template('add_bungalow.html', sessions=sessions)

        # Check if bungalow number already exists
        existing_bungalows = db_ops.list_bungalows()
        if any(b.get('numero') == numero for b in existing_bungalows):
            flash('Un bungalow avec ce numéro existe déjà', 'error')
            sessions = db_ops.list_sessions()
            db_manager.close()
            return render_template('add_bungalow.html', sessions=sessions)

        bungalow_id = db_ops.create_bungalow(numero, endroit, capacite, caracteristiques, statut, notes_statut)

        # Create bungalow-session link if session is selected
        if bungalow_id and session_id:
            try:
                link_id = db_ops.create_bungalow_session_link(bungalow_id, int(session_id), 'active', f'Liaison créée lors de l\'ajout du bungalow {numero}')
                if link_id:
                    flash('Bungalow ajouté et associé à la session avec succès', 'success')
                else:
                    flash('Bungalow ajouté mais erreur lors de l\'association à la session', 'warning')
            except Exception as e:
                flash(f'Bungalow ajouté mais erreur lors de l\'association à la session: {str(e)}', 'warning')
        elif bungalow_id:
            flash('Bungalow ajouté avec succès', 'success')
        else:
            flash('Erreur lors de l\'ajout du bungalow', 'error')

        db_manager.close()

        if bungalow_id:
            return redirect(url_for('bungalows'))

    # GET request - show form with sessions
    sessions = db_ops.list_sessions()
    db_manager.close()
    return render_template('add_bungalow.html', sessions=sessions)

@app.route('/bungalows/edit/<int:bungalow_id>', methods=['GET', 'POST'])
@login_required
def edit_bungalow(bungalow_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if request.method == 'POST':
        numero = request.form.get('numero')
        endroit = request.form.get('endroit')
        capacite = request.form.get('capacite')
        caracteristiques = request.form.get('caracteristiques')
        statut = request.form.get('statut')
        notes_statut = request.form.get('notes_statut')
        session_id = request.form.get('session_id')

        if not numero or not endroit or not capacite or not statut:
            flash('Tous les champs obligatoires doivent être remplis', 'error')
            bungalow = db_ops.get_bungalow_by_id(bungalow_id)
            sessions = db_ops.list_sessions()
            current_session = db_ops.get_bungalow_current_session(bungalow_id)
            db_manager.close()
            return render_template('edit_bungalow.html', bungalow=bungalow, sessions=sessions, current_session=current_session)

        try:
            capacite = int(capacite)
        except ValueError:
            flash('La capacité doit être un nombre entier', 'error')
            bungalow = db_ops.get_bungalow_by_id(bungalow_id)
            sessions = db_ops.list_sessions()
            current_session = db_ops.get_bungalow_current_session(bungalow_id)
            db_manager.close()
            return render_template('edit_bungalow.html', bungalow=bungalow, sessions=sessions, current_session=current_session)

        success = db_ops.update_bungalow(bungalow_id, numero, endroit, capacite, caracteristiques, statut, notes_statut)

        # Handle session association changes
        if success:
            current_session = db_ops.get_bungalow_current_session(bungalow_id)

            # If session changed
            if session_id and (not current_session or str(current_session.get('id')) != session_id):
                # Remove old association if exists
                if current_session:
                    db_ops.remove_bungalow_session_link(bungalow_id, current_session['id'])

                # Add new association
                link_id = db_ops.create_bungalow_session_link(bungalow_id, int(session_id), 'active', f'Liaison mise à jour pour le bungalow {numero}')
                if link_id:
                    flash('Bungalow modifié et association à la session mise à jour avec succès', 'success')
                else:
                    flash('Bungalow modifié mais erreur lors de la mise à jour de l\'association à la session', 'warning')
            elif not session_id and current_session:
                # Remove association if session was cleared
                db_ops.remove_bungalow_session_link(bungalow_id, current_session['id'])
                flash('Bungalow modifié et association à la session supprimée', 'success')
            else:
                flash('Bungalow modifié avec succès', 'success')
        else:
            flash('Erreur lors de la modification du bungalow', 'error')

        db_manager.close()

        if success:
            return redirect(url_for('bungalows'))

    # GET request - show form with sessions and current association
    bungalow = db_ops.get_bungalow_by_id(bungalow_id)
    sessions = db_ops.list_sessions()
    current_session = db_ops.get_bungalow_current_session(bungalow_id)
    db_manager.close()

    if not bungalow:
        flash('Bungalow non trouvé', 'error')
        return redirect(url_for('bungalows'))

    return render_template('edit_bungalow.html', bungalow=bungalow, sessions=sessions, current_session=current_session)

@app.route('/bungalows/delete/<int:bungalow_id>', methods=['POST'])
@login_required
def delete_bungalow(bungalow_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Check if bungalow is in use
    distributions = db_ops.list_distribution_bungalows()
    active_distributions = [d for d in distributions if d.get('bungalow_id') == bungalow_id and
                           (not d.get('date_fin') or d.get('date_fin') >= datetime.now().strftime('%Y-%m-%d'))]

    if active_distributions:
        flash('Impossible de supprimer ce bungalow car il est actuellement utilisé dans des distributions actives', 'error')
        db_manager.close()
        return redirect(url_for('bungalows'))

    success = db_ops.delete_bungalow(bungalow_id)
    db_manager.close()

    if success:
        flash('Bungalow supprimé avec succès', 'success')
    else:
        flash('Erreur lors de la suppression du bungalow', 'error')

    return redirect(url_for('bungalows'))

@app.route('/bungalows/view/<int:bungalow_id>')
@login_required
def view_bungalow(bungalow_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalow = db_ops.get_bungalow_by_id(bungalow_id)

    if not bungalow:
        flash('Bungalow non trouvé', 'error')
        db_manager.close()
        return redirect(url_for('bungalows'))

    # Get current and past distributions for this bungalow with details
    bungalow_distributions = db_ops.get_distribution_bungalows_by_bungalow_with_details(bungalow_id)

    # Get current occupancy status
    current_date = datetime.now().strftime('%Y-%m-%d')
    current_distribution = None
    for dist in bungalow_distributions:
        if (dist.get('date_debut') <= current_date and
            (not dist.get('date_fin') or dist.get('date_fin') >= current_date)):
            current_distribution = dist
            break

    db_manager.close()

    return render_template('view_bungalow.html',
                         bungalow=bungalow,
                         distributions=bungalow_distributions,
                         current_distribution=current_distribution)

@app.route('/bungalows/status')
@login_required
def bungalows_status():
    date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalows_with_status = db_ops.get_bungalows_with_occupancy_status(date)
    db_manager.close()

    return render_template('bungalows_status.html', bungalows=bungalows_with_status, date=date)

# Personnel routes
@app.route('/personnel')
@login_required
def personnel():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get search parameter
    search_query = request.args.get('search', '').strip()

    # Use optimized search (Performance Optimization)
    if search_query:
        personnel_list = db_ops.search_personnel_optimized(search_query)
    else:
        personnel_list = db_ops.list_personnel_with_grades_and_unites()

    db_manager.close()

    return render_template('personnel.html', personnel=personnel_list, search_query=search_query)

@app.route('/personnel/add', methods=['GET', 'POST'])
@login_required
def add_personnel():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    try:
        if request.method == 'POST':
            numero = request.form.get('numero')
            nom = request.form.get('nom')
            prenom = request.form.get('prenom')
            grade_id = request.form.get('grade_id')
            unite_id = request.form.get('unite_id')
            region = request.form.get('region')

            if not numero or not nom or not prenom or not grade_id or not unite_id:
                flash('Tous les champs obligatoires doivent être remplis', 'error')
                grades = db_ops.list_grades()
                unites = db_ops.list_unites()
                db_manager.close()
                return render_template('add_personnel.html', grades=grades, unites=unites)

            # Check if personnel number already exists
            existing_personnel = db_ops.list_personnel_militaire()
            if any(p.get('numero') == numero for p in existing_personnel):
                flash('Un personnel avec ce numéro existe déjà', 'error')
                grades = db_ops.list_grades()
                unites = db_ops.list_unites()
                db_manager.close()
                return render_template('add_personnel.html', grades=grades, unites=unites)

            # Use numero as matricule since they're the same in this context
            personnel_id = db_ops.create_personnel_militaire(
                matricule=numero, nom=nom, prenom=prenom,
                grade_id=int(grade_id), unite_id=int(unite_id), numero=numero, region=region
            )

            if personnel_id:
                flash('Personnel ajouté avec succès', 'success')
                db_manager.close()
                return redirect(url_for('personnel'))
            else:
                flash('Erreur lors de l\'ajout du personnel', 'error')

        # Get grades and units for the form
        grades = db_ops.list_grades()
        unites = db_ops.list_unites()

        if not grades:
            flash('Aucun grade disponible. Veuillez d\'abord ajouter des grades.', 'warning')

        if not unites:
            flash('Aucune unité disponible. Veuillez d\'abord ajouter des unités.', 'warning')

        db_manager.close()
        return render_template('add_personnel.html', grades=grades, unites=unites)

    except Exception as e:
        db_manager.close()
        flash(f'Erreur interne: {str(e)}', 'error')
        return render_template('add_personnel.html', grades=[], unites=[])

@app.route('/personnel/edit/<int:personnel_id>', methods=['GET', 'POST'])
@login_required
def edit_personnel(personnel_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if request.method == 'POST':
        numero = request.form.get('numero')
        nom = request.form.get('nom')
        prenom = request.form.get('prenom')
        grade_id = request.form.get('grade_id')
        unite_id = request.form.get('unite_id')
        region = request.form.get('region')

        if not numero or not nom or not prenom or not grade_id or not unite_id:
            flash('Tous les champs obligatoires doivent être remplis', 'error')
            personnel = db_ops.get_personnel_by_id(personnel_id)
            grades = db_ops.list_grades()
            unites = db_ops.list_unites()
            db_manager.close()
            return render_template('edit_personnel.html', personnel=personnel, grades=grades, unites=unites)

        success = db_ops.update_personnel_militaire(
            personnel_id, matricule=numero, nom=nom, prenom=prenom,
            grade_id=int(grade_id), unite_id=int(unite_id), numero=numero, region=region
        )
        db_manager.close()

        if success:
            flash('Personnel modifié avec succès', 'success')
            return redirect(url_for('personnel'))
        else:
            flash('Erreur lors de la modification du personnel', 'error')

    personnel = db_ops.get_personnel_by_id(personnel_id)
    grades = db_ops.list_grades()
    unites = db_ops.list_unites()
    db_manager.close()

    if not personnel:
        flash('Personnel non trouvé', 'error')
        return redirect(url_for('personnel'))

    return render_template('edit_personnel.html', personnel=personnel, grades=grades, unites=unites)

@app.route('/personnel/delete/<int:personnel_id>', methods=['POST'])
@login_required
def delete_personnel(personnel_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Check if personnel is in use
    distributions = db_ops.list_distribution_bungalows()
    active_distributions = [d for d in distributions if d.get('personnel_id') == personnel_id and
                           (not d.get('date_fin') or d.get('date_fin') >= datetime.now().strftime('%Y-%m-%d'))]

    if active_distributions:
        flash('Impossible de supprimer ce personnel car il est actuellement dans des distributions actives', 'error')
        db_manager.close()
        return redirect(url_for('personnel'))

    success = db_ops.delete_personnel_militaire(personnel_id)
    db_manager.close()

    if success:
        flash('Personnel supprimé avec succès', 'success')
    else:
        flash('Erreur lors de la suppression du personnel', 'error')

    return redirect(url_for('personnel'))

@app.route('/personnel/view/<int:personnel_id>')
@login_required
def view_personnel(personnel_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get personnel with grade and unit details
    personnel_list = db_ops.list_personnel_with_grades_and_unites()
    personnel = None
    for p in personnel_list:
        if p['id'] == personnel_id:
            personnel = p
            break

    if not personnel:
        flash('Personnel non trouvé', 'error')
        db_manager.close()
        return redirect(url_for('personnel'))

    # Get distributions for this personnel
    personnel_distributions = db_ops.get_distribution_bungalows_by_personnel(personnel_id)

    # Get current distribution
    current_date = datetime.now().strftime('%Y-%m-%d')
    current_distribution = None
    for dist in personnel_distributions:
        if (dist.get('date_debut') <= current_date and
            (not dist.get('date_fin') or dist.get('date_fin') >= current_date)):
            current_distribution = dist
            break

    # Get distribution details with bungalow info
    distributions_with_details = []
    for dist in personnel_distributions:
        dist_detail = db_ops.get_distribution_with_details(dist['id'])
        if dist_detail:
            distributions_with_details.append(dist_detail)

    db_manager.close()

    return render_template('view_personnel.html',
                         personnel=personnel,
                         distributions=distributions_with_details,
                         current_distribution=current_distribution)

# Distribution routes
@app.route('/distributions')
@login_required
def distributions():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    distributions_list = db_ops.list_distributions_with_details()
    unites_list = db_ops.list_unites()
    current_date = datetime.now().strftime('%Y-%m-%d')

    # Calculate bungalow statistics
    all_bungalows = db_ops.list_bungalows()
    total_bungalows = len(all_bungalows)

    # Get currently distributed bungalows
    distributed_bungalows = set()
    available_bungalows = set()

    for distribution in distributions_list:
        # Check if distribution is currently active
        if (distribution.get('date_debut') and distribution.get('date_debut') <= current_date and
            (not distribution.get('date_fin') or distribution.get('date_fin') >= current_date)):
            if distribution.get('bungalow_id'):
                distributed_bungalows.add(distribution.get('bungalow_id'))

    # Calculate available bungalows
    for bungalow in all_bungalows:
        if bungalow['id'] not in distributed_bungalows:
            available_bungalows.add(bungalow['id'])

    distributed_count = len(distributed_bungalows)
    available_count = len(available_bungalows)

    # Calculate statistics by status
    active_distributions = 0
    upcoming_distributions = 0
    expired_distributions = 0

    for distribution in distributions_list:
        if not distribution.get('date_debut'):
            continue
        elif distribution.get('date_debut') > current_date:
            upcoming_distributions += 1
        elif not distribution.get('date_fin') or distribution.get('date_fin') >= current_date:
            active_distributions += 1
        else:
            expired_distributions += 1

    db_manager.close()

    return render_template('distributions.html',
                         distributions=distributions_list,
                         unites=unites_list,
                         today=current_date,
                         total_bungalows=total_bungalows,
                         distributed_count=distributed_count,
                         available_count=available_count,
                         active_distributions=active_distributions,
                         upcoming_distributions=upcoming_distributions,
                         expired_distributions=expired_distributions)

@app.route('/distributions/new', methods=['GET', 'POST'])
@login_required
def new_distribution():
    if request.method == 'POST':
        numero = request.form.get('numero')
        bungalow_id = request.form.get('bungalow_id')
        personnel_id = request.form.get('personnel_id')
        session_id = request.form.get('session_id')
        date_debut = request.form.get('date_debut')
        date_fin = request.form.get('date_fin')
        notes = request.form.get('notes')

        # Validate required fields
        if not numero or not bungalow_id or not personnel_id or not date_debut:
            flash('Les champs Numéro, Bungalow, Personnel et Date de début sont obligatoires', 'error')
            # Return to form with data
            db_manager = DatabaseManager()
            db_manager.connect()
            db_ops = DatabaseOperations(db_manager)
            bungalows = db_ops.list_bungalows()
            personnel = db_ops.list_personnel_with_grades_and_unites()
            sessions = db_ops.list_sessions()
            grades = db_ops.list_grades()
            unites = db_ops.list_unites()
            db_manager.close()
            return render_template('new_distribution.html',
                                  bungalows=bungalows,
                                  personnel=personnel,
                                  sessions=sessions,
                                  grades=grades,
                                  unites=unites)

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Check if distribution number already exists
        existing_numero = db_ops.get_distribution_bungalow_by_numero(numero)
        if existing_numero:
            flash(f'Une distribution avec le numéro "{numero}" existe déjà', 'error')
            # Return to form with data
            bungalows = db_ops.list_bungalows()
            personnel = db_ops.list_personnel_with_grades_and_unites()
            sessions = db_ops.list_sessions()
            grades = db_ops.list_grades()
            unites = db_ops.list_unites()
            db_manager.close()
            return render_template('new_distribution.html',
                                  bungalows=bungalows,
                                  personnel=personnel,
                                  sessions=sessions,
                                  grades=grades,
                                  unites=unites)

        # Check if personnel already has an active distribution
        if personnel_id:
            existing_distribution = db_ops.check_personnel_has_active_distribution(int(personnel_id))
            if existing_distribution:
                db_manager.close()
                flash(f'Ce personnel militaire a déjà une distribution active (N° {existing_distribution.get("numero", "N/A")}) jusqu\'au {existing_distribution.get("date_fin", "indéterminée")}. Un personnel ne peut bénéficier que d\'une seule distribution à la fois.', 'error')
                # Return to form with data
                db_manager = DatabaseManager()
                db_manager.connect()
                db_ops = DatabaseOperations(db_manager)
                bungalows = db_ops.list_bungalows()
                personnel = db_ops.list_personnel_with_grades_and_unites()
                sessions = db_ops.list_sessions()
                grades = db_ops.list_grades()
                unites = db_ops.list_unites()
                db_manager.close()
                return render_template('new_distribution.html',
                                      bungalows=bungalows,
                                      personnel=personnel,
                                      sessions=sessions,
                                      grades=grades,
                                      unites=unites)

        # Debug information
        print(f"🔥 DEBUG: Creating distribution with data:")
        print(f"   - numero: {numero}")
        print(f"   - bungalow_id: {bungalow_id}")
        print(f"   - personnel_id: {personnel_id}")
        print(f"   - session_id: {session_id}")
        print(f"   - date_debut: {date_debut}")
        print(f"   - date_fin: {date_fin}")
        print(f"   - notes: {notes}")

        distribution_id = db_ops.create_distribution_bungalow(
            numero,
            int(bungalow_id) if bungalow_id else None,
            int(personnel_id) if personnel_id else None,
            int(session_id) if session_id else None,
            date_debut,
            date_fin,
            notes
        )

        print(f"🔥 DEBUG: Distribution creation result: {distribution_id}")

        db_manager.close()

        if distribution_id:
            flash('Distribution créée avec succès', 'success')
            return redirect(url_for('distributions'))
        else:
            flash('Échec de la création de la distribution', 'error')

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalows = db_ops.list_bungalows()
    personnel = db_ops.list_personnel_with_grades_and_unites()
    sessions = db_ops.list_sessions()
    grades = db_ops.list_grades()
    unites = db_ops.list_unites()

    # Debug: Print data counts
    print(f"🔥 DEBUG: Sending to template - Bungalows: {len(bungalows)}, Personnel: {len(personnel)}, Sessions: {len(sessions)}")
    if bungalows:
        print(f"🔥 DEBUG: Sample bungalow: {bungalows[0]}")

    db_manager.close()

    return render_template('new_distribution.html',
                          bungalows=bungalows,
                          personnel=personnel,
                          sessions=sessions,
                          grades=grades,
                          unites=unites)

@app.route('/distributions/edit/<int:distribution_id>', methods=['GET', 'POST'])
@login_required
def edit_distribution(distribution_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if request.method == 'POST':
        numero = request.form.get('numero')
        bungalow_id = request.form.get('bungalow_id')
        personnel_id = request.form.get('personnel_id')
        session_id = request.form.get('session_id')
        date_debut = request.form.get('date_debut')
        date_fin = request.form.get('date_fin')
        notes = request.form.get('notes')

        if not numero or not bungalow_id or not personnel_id or not session_id or not date_debut:
            flash('Tous les champs obligatoires doivent être remplis', 'error')
            distribution = db_ops.get_distribution_by_id(distribution_id)
            bungalows = db_ops.list_bungalows()
            personnel = db_ops.list_personnel_militaire()
            sessions = db_ops.list_sessions()
            db_manager.close()
            return render_template('edit_distribution.html',
                                 distribution=distribution,
                                 bungalows=bungalows,
                                 personnel=personnel,
                                 sessions=sessions)

        success = db_ops.update_distribution_bungalow(
            distribution_id,
            numero,
            int(bungalow_id),
            int(personnel_id),
            int(session_id),
            date_debut,
            date_fin,
            notes
        )
        db_manager.close()

        if success:
            flash('Distribution modifiée avec succès', 'success')
            return redirect(url_for('distributions'))
        else:
            flash('Erreur lors de la modification de la distribution', 'error')

    distribution = db_ops.get_distribution_by_id(distribution_id)
    bungalows = db_ops.list_bungalows()
    personnel = db_ops.list_personnel_militaire()
    sessions = db_ops.list_sessions()
    db_manager.close()

    if not distribution:
        flash('Distribution non trouvée', 'error')
        return redirect(url_for('distributions'))

    return render_template('edit_distribution.html',
                         distribution=distribution,
                         bungalows=bungalows,
                         personnel=personnel,
                         sessions=sessions)

@app.route('/distributions/delete/<int:distribution_id>', methods=['POST'])
@login_required
def delete_distribution(distribution_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    success = db_ops.delete_distribution_bungalow(distribution_id)
    db_manager.close()

    if success:
        flash('Distribution supprimée avec succès', 'success')
    else:
        flash('Erreur lors de la suppression de la distribution', 'error')

    return redirect(url_for('distributions'))

@app.route('/distributions/view/<int:distribution_id>')
@login_required
def view_distribution(distribution_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    distribution = db_ops.get_distribution_with_details(distribution_id)

    if not distribution:
        flash('Distribution non trouvée', 'error')
        db_manager.close()
        return redirect(url_for('distributions'))

    # Get current date for status calculation
    current_date = datetime.now().strftime('%Y-%m-%d')

    # Calculate status
    status = 'unknown'
    if not distribution.get('date_debut'):
        status = 'not_planned'
    elif distribution.get('date_debut') > current_date:
        status = 'upcoming'
    elif not distribution.get('date_fin') or distribution.get('date_fin') >= current_date:
        status = 'active'
    else:
        status = 'expired'

    db_manager.close()

    return render_template('view_distribution.html',
                         distribution=distribution,
                         status=status,
                         today=current_date)











# Grades routes
@app.route('/grades')
@login_required
def grades():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get search parameter
    search = request.args.get('search', '').strip()

    if search:
        grades_list = db_ops.search_grades(search)
    else:
        grades_list = db_ops.list_grades_with_personnel_count()

    db_manager.close()

    return render_template('grades.html', grades=grades_list, search=search)

@app.route('/grades/add', methods=['GET', 'POST'])
@login_required
def add_grade():
    if request.method == 'POST':
        grade = request.form.get('grade')
        description = request.form.get('description')

        if not grade:
            flash('Le nom du grade est obligatoire', 'error')
            return render_template('add_grade.html')

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Check if grade already exists
        existing = db_ops.get_grade_by_name(grade)
        if existing:
            flash(f'Un grade avec le nom "{grade}" existe déjà', 'error')
            db_manager.close()
            return render_template('add_grade.html')

        grade_id = db_ops.create_grade(grade, description)
        db_manager.close()

        if grade_id:
            flash('Grade créé avec succès', 'success')
            return redirect(url_for('grades'))
        else:
            flash('Erreur lors de la création du grade', 'error')

    return render_template('add_grade.html')

@app.route('/grades/edit/<int:grade_id>', methods=['GET', 'POST'])
@login_required
def edit_grade(grade_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if request.method == 'POST':
        grade = request.form.get('grade')
        description = request.form.get('description')

        if not grade:
            flash('Le nom du grade est obligatoire', 'error')
            grade_data = db_ops.get_grade_by_id(grade_id)
            db_manager.close()
            return render_template('edit_grade.html', grade=grade_data)

        success = db_ops.update_grade(grade_id, grade, description)
        db_manager.close()

        if success:
            flash('Grade modifié avec succès', 'success')
            return redirect(url_for('grades'))
        else:
            flash('Erreur lors de la modification du grade', 'error')

    grade = db_ops.get_grade_by_id(grade_id)
    db_manager.close()

    if not grade:
        flash('Grade non trouvé', 'error')
        return redirect(url_for('grades'))

    return render_template('edit_grade.html', grade=grade)

@app.route('/grades/delete/<int:grade_id>', methods=['POST'])
@login_required
def delete_grade(grade_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    success, message = db_ops.delete_grade(grade_id)
    db_manager.close()

    if success:
        flash('Grade supprimé avec succès', 'success')
    else:
        flash(message, 'error')

    return redirect(url_for('grades'))

@app.route('/api/grades/count')
@login_required
def api_grades_count():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    grades = db_ops.list_grades()
    count = len(grades)

    db_manager.close()
    return jsonify({'count': count})

@app.route('/api/grades/<int:grade_id>')
@login_required
def api_grade_details(grade_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    grade = db_ops.get_grade_by_id(grade_id)
    db_manager.close()

    if not grade:
        return jsonify({'error': 'Grade non trouvé'}), 404

    return jsonify(grade)

@app.route('/api/grades/<int:grade_id>/personnel')
@login_required
def api_grade_personnel(grade_id):
    db_manager = DatabaseManager()
    db_manager.connect()

    # Count personnel with this grade
    db_manager.execute("SELECT COUNT(*) as count FROM personnel_militaire WHERE grade_id = ?", (grade_id,))
    result = db_manager.cursor.fetchone()
    count = result['count'] if result else 0

    db_manager.close()

    return jsonify({'count': count})

@app.route('/api/bungalows/search')
@login_required
def api_bungalows_search():
    """Search bungalows for autocomplete"""
    query = request.args.get('q', '').strip()

    if not query or len(query) < 2:
        return jsonify([])

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    try:
        # Get all bungalows
        bungalows = db_ops.list_bungalows()

        # Filter bungalows based on search query
        matching_bungalows = []
        for bungalow in bungalows:
            numero = str(bungalow.get('numero', '')).lower()
            endroit = str(bungalow.get('endroit', '')).lower()
            query_lower = query.lower()

            if (query_lower in numero or
                query_lower in endroit or
                numero.startswith(query_lower)):
                matching_bungalows.append({
                    'id': bungalow.get('id'),
                    'numero': bungalow.get('numero'),
                    'endroit': bungalow.get('endroit'),
                    'capacite': bungalow.get('capacite'),
                    'statut': bungalow.get('statut')
                })

        # Limit results to 5 for better UX
        matching_bungalows = matching_bungalows[:5]

        db_manager.close()
        return jsonify(matching_bungalows)

    except Exception as e:
        db_manager.close()
        return jsonify([]), 500

# Unites routes
@app.route('/unites')
@login_required
def unites():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    unites_list = db_ops.list_unites_with_personnel_count()
    db_manager.close()

    return render_template('unites.html', unites=unites_list)

@app.route('/unites/add', methods=['GET', 'POST'])
@login_required
def add_unite():
    if request.method == 'POST':
        numero = request.form.get('numero')
        description = request.form.get('description')
        raccourci = request.form.get('raccourci')

        if not numero or not description:
            flash('Le numéro et la description sont obligatoires', 'error')
            return render_template('add_unite.html')

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Check if unite already exists
        existing = db_ops.get_unite_by_numero(numero)
        if existing:
            flash(f'Une unité avec le numéro {numero} existe déjà', 'error')
            db_manager.close()
            return render_template('add_unite.html')

        unite_id = db_ops.create_unite(numero, description, raccourci)
        db_manager.close()

        if unite_id:
            flash('Unité créée avec succès', 'success')
            return redirect(url_for('unites'))
        else:
            flash('Erreur lors de la création de l\'unité', 'error')

    return render_template('add_unite.html')

@app.route('/unites/edit/<int:unite_id>', methods=['GET', 'POST'])
@login_required
def edit_unite(unite_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if request.method == 'POST':
        numero = request.form.get('numero')
        description = request.form.get('description')
        raccourci = request.form.get('raccourci')

        if not numero or not description:
            flash('Le numéro et la description sont obligatoires', 'error')
            unite = db_ops.get_unite_by_id(unite_id)
            db_manager.close()
            return render_template('edit_unite.html', unite=unite)

        success = db_ops.update_unite(unite_id, numero, description, raccourci)
        db_manager.close()

        if success:
            flash('Unité modifiée avec succès', 'success')
            return redirect(url_for('unites'))
        else:
            flash('Erreur lors de la modification de l\'unité', 'error')

    unite = db_ops.get_unite_by_id(unite_id)
    db_manager.close()

    if not unite:
        flash('Unité non trouvée', 'error')
        return redirect(url_for('unites'))

    return render_template('edit_unite.html', unite=unite)

@app.route('/unites/delete/<int:unite_id>', methods=['POST'])
@login_required
def delete_unite(unite_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    success, message = db_ops.delete_unite(unite_id)
    db_manager.close()

    if success:
        flash('Unité supprimée avec succès', 'success')
    else:
        flash(message, 'error')

    return redirect(url_for('unites'))

@app.route('/unites/import', methods=['GET', 'POST'])
@login_required
def import_unites():
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('Aucun fichier sélectionné', 'error')
            return render_template('import_unites.html')

        file = request.files['file']
        if file.filename == '':
            flash('Aucun fichier sélectionné', 'error')
            return render_template('import_unites.html')

        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            flash('Format de fichier non supporté. Utilisez un fichier Excel (.xlsx ou .xls)', 'error')
            return render_template('import_unites.html')

        try:
            import pandas as pd

            # Read Excel file
            df = pd.read_excel(file)

            # Validate required columns
            required_columns = ['numero', 'description']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                flash(f'Colonnes manquantes dans le fichier: {", ".join(missing_columns)}', 'error')
                return render_template('import_unites.html')

            # Convert DataFrame to list of dictionaries
            unites_data = []
            for index, row in df.iterrows():
                unite_data = {
                    'numero': row.get('numero', ''),
                    'description': row.get('description', ''),
                    'raccourci': row.get('raccourci', '') if 'raccourci' in df.columns else None
                }
                unites_data.append(unite_data)

            # Import data
            db_manager = DatabaseManager()
            db_manager.connect()
            db_ops = DatabaseOperations(db_manager)

            results = db_ops.import_unites_from_data(unites_data)
            db_manager.close()

            # Show results
            if results['success'] > 0:
                flash(f'{results["success"]} unités importées avec succès', 'success')

            if results['duplicates'] > 0:
                flash(f'{results["duplicates"]} unités ignorées (doublons)', 'warning')

            if results['errors'] > 0:
                flash(f'{results["errors"]} erreurs lors de l\'importation', 'error')

            return redirect(url_for('unites'))

        except ImportError:
            flash('Pandas n\'est pas installé. Installez-le avec: pip install pandas openpyxl', 'error')
            return render_template('import_unites.html')
        except Exception as e:
            flash(f'Erreur lors de la lecture du fichier: {str(e)}', 'error')
            return render_template('import_unites.html')

    return render_template('import_unites.html')

@app.route('/api/unites/count')
@login_required
def api_unites_count():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    unites = db_ops.list_unites()
    count = len(unites)

    db_manager.close()
    return jsonify({'count': count})

@app.route('/api/unites/export')
@login_required
def export_unites():
    try:
        import pandas as pd
        from io import BytesIO
        from flask import send_file

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        unites = db_ops.list_unites_with_personnel_count()
        db_manager.close()

        # Convert to DataFrame
        df = pd.DataFrame(unites)

        # Create Excel file in memory
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Unites', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'unites_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except ImportError:
        flash('Pandas n\'est pas installé pour l\'exportation Excel', 'error')
        return redirect(url_for('unites'))
    except Exception as e:
        flash(f'Erreur lors de l\'exportation: {str(e)}', 'error')
        return redirect(url_for('unites'))

@app.route('/api/unites/<int:unite_id>')
@login_required
def api_unite_details(unite_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    unite = db_ops.get_unite_by_id(unite_id)
    db_manager.close()

    if not unite:
        return jsonify({'error': 'Unité non trouvée'}), 404

    return jsonify(unite)

@app.route('/api/unites/<int:unite_id>/personnel')
@login_required
def api_unite_personnel(unite_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Count personnel in this unite
    db_manager.execute("SELECT COUNT(*) as count FROM personnel_militaire WHERE unite_id = ?", (unite_id,))
    result = db_manager.cursor.fetchone()
    count = result['count'] if result else 0

    db_manager.close()

    return jsonify({'count': count})

@app.route('/api/current-distributions')
@login_required
def api_current_distributions():
    """Get current active distributions"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get all distributions
    distributions = db_ops.list_distributions_with_details()

    # Filter only current active distributions
    current_date = datetime.now().strftime('%Y-%m-%d')
    active_distributions = []

    for dist in distributions:
        date_debut = dist.get('date_debut')
        date_fin = dist.get('date_fin')

        # Check if distribution is currently active
        if (date_debut and date_debut <= current_date and
            (not date_fin or date_fin >= current_date)):
            active_distributions.append(dist)

    db_manager.close()

    return jsonify(active_distributions)

@app.route('/api/bungalow-statistics')
@login_required
def api_bungalow_statistics():
    """Get accurate bungalow statistics"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get all bungalows
    all_bungalows = db_ops.list_bungalows()
    total_bungalows = len(all_bungalows)

    # Calculate total capacity
    total_capacity = sum(int(b.get('capacite', 0)) for b in all_bungalows)

    # Get current active distributions
    distributions = db_ops.list_distributions_with_details()
    current_date = datetime.now().strftime('%Y-%m-%d')

    # Find currently occupied bungalows and their capacities
    occupied_bungalow_ids = set()
    occupied_capacity = 0

    for dist in distributions:
        date_debut = dist.get('date_debut')
        date_fin = dist.get('date_fin')
        bungalow_id = dist.get('bungalow_id')

        # Check if distribution is currently active
        if (bungalow_id and date_debut and date_debut <= current_date and
            (not date_fin or date_fin >= current_date)):
            occupied_bungalow_ids.add(bungalow_id)

            # Find the bungalow capacity
            for bungalow in all_bungalows:
                if bungalow.get('id') == bungalow_id:
                    occupied_capacity += int(bungalow.get('capacite', 0))
                    break

    occupied_count = len(occupied_bungalow_ids)
    available_count = total_bungalows - occupied_count
    available_capacity = total_capacity - occupied_capacity

    db_manager.close()

    return jsonify({
        'total': total_bungalows,
        'total_capacity': total_capacity,
        'occupied': occupied_count,
        'occupied_capacity': occupied_capacity,
        'available': available_count,
        'available_capacity': available_capacity,
        'occupied_bungalow_ids': list(occupied_bungalow_ids)
    })

@app.route('/api/bungalow/<int:bungalow_id>/occupancy')
@login_required
def api_bungalow_occupancy(bungalow_id):
    """Get occupancy information for a specific bungalow"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get bungalow details
    bungalow = db_ops.get_bungalow_by_id(bungalow_id)
    if not bungalow:
        db_manager.close()
        return jsonify({'error': 'Bungalow not found'}), 404

    # Get current active distributions for this bungalow
    distributions = db_ops.list_distributions_with_details()
    current_date = datetime.now().strftime('%Y-%m-%d')

    current_occupancy = 0
    for dist in distributions:
        if (dist.get('bungalow_id') == bungalow_id and
            dist.get('date_debut') and dist.get('date_debut') <= current_date and
            (not dist.get('date_fin') or dist.get('date_fin') >= current_date)):
            current_occupancy += 1

    capacity = int(bungalow.get('capacite', 0))
    remaining = capacity - current_occupancy

    db_manager.close()

    return jsonify({
        'bungalow_id': bungalow_id,
        'capacity': capacity,
        'current': current_occupancy,
        'remaining': remaining,
        'is_full': remaining <= 0
    })

@app.route('/api/bungalow/<int:bungalow_id>/conflicts')
@login_required
def api_bungalow_conflicts(bungalow_id):
    """Check for date conflicts in a specific bungalow"""
    date_debut = request.args.get('date_debut')
    date_fin = request.args.get('date_fin')

    if not date_debut:
        return jsonify({'error': 'Date de début requise'}), 400

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get bungalow details
    bungalow = db_ops.get_bungalow_by_id(bungalow_id)
    if not bungalow:
        db_manager.close()
        return jsonify({'error': 'Bungalow not found'}), 404

    # Get all distributions for this bungalow
    distributions = db_ops.list_distributions_with_details()

    conflicting_distributions = []
    for dist in distributions:
        if dist.get('bungalow_id') != bungalow_id:
            continue

        dist_debut = dist.get('date_debut')
        dist_fin = dist.get('date_fin')

        if not dist_debut:
            continue

        # Check for date overlap
        has_conflict = False

        if date_fin:
            # Both periods have end dates - check for overlap
            if (date_debut <= (dist_fin or dist_debut) and
                date_fin >= dist_debut):
                has_conflict = True
        else:
            # New distribution has no end date - check if it starts during existing period
            if dist_fin:
                if date_debut <= dist_fin and date_debut >= dist_debut:
                    has_conflict = True
            else:
                # Both have no end date - conflict if same start date
                if date_debut == dist_debut:
                    has_conflict = True

        if has_conflict:
            conflicting_distributions.append({
                'id': dist.get('id'),
                'numero': dist.get('numero'),
                'date_debut': dist_debut,
                'date_fin': dist_fin,
                'personnel_nom': dist.get('personnel_nom'),
                'personnel_prenom': dist.get('personnel_prenom')
            })

    db_manager.close()

    return jsonify({
        'bungalow_id': bungalow_id,
        'has_conflict': len(conflicting_distributions) > 0,
        'conflicting_distributions': conflicting_distributions,
        'check_period': {
            'date_debut': date_debut,
            'date_fin': date_fin
        }
    })

@app.route('/api/bungalow/<int:bungalow_id>/sessions')
@login_required
def api_bungalow_sessions(bungalow_id):
    """Get sessions associated with a specific bungalow"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get sessions associated with this bungalow through different methods
    sessions = []

    # Method 1: Through distribution_bungalows table
    db_manager.execute("""
        SELECT DISTINCT s.id, s.numero, s.description, s.date_debut, s.date_fin, s.etat_session,
               'distribution' as association_type
        FROM sessions s
        JOIN distribution_bungalows d ON s.id = d.session_id
        WHERE d.bungalow_id = ?
    """, (bungalow_id,))
    distribution_sessions = [dict(row) for row in db_manager.cursor.fetchall()]
    sessions.extend(distribution_sessions)

    # Method 2: Through bungalow_sessions table (if exists)
    try:
        db_manager.execute("""
            SELECT DISTINCT s.id, s.numero, s.description, s.date_debut, s.date_fin, s.etat_session,
                   'direct_link' as association_type
            FROM sessions s
            JOIN bungalow_sessions bs ON s.id = bs.session_id
            WHERE bs.bungalow_id = ? AND bs.statut = 'active'
        """, (bungalow_id,))
        direct_sessions = [dict(row) for row in db_manager.cursor.fetchall()]
        sessions.extend(direct_sessions)
    except:
        pass  # Table might not exist

    # Method 3: Through bungalows.session_id field (if exists)
    try:
        db_manager.execute("""
            SELECT DISTINCT s.id, s.numero, s.description, s.date_debut, s.date_fin, s.etat_session,
                   'bungalow_field' as association_type
            FROM sessions s
            JOIN bungalows b ON s.id = b.session_id
            WHERE b.id = ?
        """, (bungalow_id,))
        field_sessions = [dict(row) for row in db_manager.cursor.fetchall()]
        sessions.extend(field_sessions)
    except:
        pass  # Column might not exist

    # Remove duplicates based on session ID
    unique_sessions = {}
    for session in sessions:
        if session['id'] not in unique_sessions:
            unique_sessions[session['id']] = session

    db_manager.close()

    return jsonify(list(unique_sessions.values()))

# Sessions routes
@app.route('/sessions')
@login_required
def sessions():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get search parameter
    search = request.args.get('search', '').strip()
    status_filter = request.args.get('status', '').strip()

    if search:
        sessions_list = db_ops.search_sessions(search)
    elif status_filter:
        sessions_list = db_ops.get_sessions_by_status(status_filter)
    else:
        sessions_list = db_ops.list_sessions_with_distribution_count()

    db_manager.close()

    return render_template('sessions.html', sessions=sessions_list, search=search, status_filter=status_filter)

@app.route('/sessions/add', methods=['GET', 'POST'])
@login_required
def add_session():
    if request.method == 'POST':
        numero = request.form.get('numero')
        description = request.form.get('description')
        date_debut = request.form.get('date_debut')
        date_fin = request.form.get('date_fin')
        etat_session = request.form.get('etat_session')

        if not numero or not description or not date_debut:
            flash('Le numéro, la description et la date de début sont obligatoires', 'error')
            return render_template('add_session.html')

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Check if session already exists
        existing = db_ops.get_session_by_numero(numero)
        if existing:
            flash(f'Une session avec le numéro "{numero}" existe déjà', 'error')
            db_manager.close()
            return render_template('add_session.html')

        session_id = db_ops.create_session(numero, description, date_debut, date_fin, etat_session)
        db_manager.close()

        if session_id:
            flash('Session créée avec succès', 'success')
            return redirect(url_for('sessions'))
        else:
            flash('Erreur lors de la création de la session', 'error')

    return render_template('add_session.html')

@app.route('/sessions/edit/<int:session_id>', methods=['GET', 'POST'])
@login_required
def edit_session(session_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if request.method == 'POST':
        numero = request.form.get('numero')
        description = request.form.get('description')
        date_debut = request.form.get('date_debut')
        date_fin = request.form.get('date_fin')
        etat_session = request.form.get('etat_session')

        if not numero or not description or not date_debut:
            flash('Le numéro, la description et la date de début sont obligatoires', 'error')
            session_data = db_ops.get_session_by_id(session_id)
            db_manager.close()
            return render_template('edit_session.html', session=session_data)

        success = db_ops.update_session(session_id, numero, description, date_debut, date_fin, etat_session)
        db_manager.close()

        if success:
            flash('Session modifiée avec succès', 'success')
            return redirect(url_for('sessions'))
        else:
            flash('Erreur lors de la modification de la session', 'error')

    session = db_ops.get_session_by_id(session_id)
    db_manager.close()

    if not session:
        flash('Session non trouvée', 'error')
        return redirect(url_for('sessions'))

    return render_template('edit_session.html', session=session)

@app.route('/sessions/delete/<int:session_id>', methods=['POST'])
@login_required
def delete_session(session_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    success, message = db_ops.delete_session(session_id)
    db_manager.close()

    if success:
        flash('Session supprimée avec succès', 'success')
    else:
        flash(message, 'error')

    return redirect(url_for('sessions'))

@app.route('/api/sessions/count')
@login_required
def api_sessions_count():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    sessions = db_ops.list_sessions()
    count = len(sessions)

    db_manager.close()
    return jsonify({'count': count})

@app.route('/api/links/count')
@login_required
def api_links_count():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    links = db_ops.list_bungalow_session_links()
    count = len(links)

    db_manager.close()
    return jsonify({'count': count})

@app.route('/api/sessions/<int:session_id>')
@login_required
def api_session_details(session_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    session = db_ops.get_session_by_id(session_id)
    db_manager.close()

    if not session:
        return jsonify({'error': 'Session non trouvée'}), 404

    return jsonify(session)

@app.route('/api/sessions/<int:session_id>/distributions')
@login_required
def api_session_distributions(session_id):
    db_manager = DatabaseManager()
    db_manager.connect()

    # Count distributions for this session
    db_manager.execute("SELECT COUNT(*) as count FROM distribution_bungalows WHERE session_id = ?", (session_id,))
    result = db_manager.cursor.fetchone()
    count = result['count'] if result else 0

    db_manager.close()

    return jsonify({'count': count})

# API routes for AJAX calls
@app.route('/api/available_bungalows', methods=['GET'])
@login_required
def api_available_bungalows():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    if not start_date or not end_date:
        return jsonify({'error': 'Start date and end date are required'}), 400

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalows = db_ops.get_available_bungalows(start_date, end_date)
    db_manager.close()

    return jsonify(bungalows)

@app.route('/api/personnel/<int:personnel_id>/check_distribution')
@login_required
def api_check_personnel_distribution(personnel_id):
    """Check if personnel has active distribution"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    existing_distribution = db_ops.check_personnel_has_active_distribution(personnel_id)
    db_manager.close()

    if existing_distribution:
        return jsonify({
            'has_active_distribution': True,
            'distribution': {
                'numero': existing_distribution.get('numero'),
                'date_fin': existing_distribution.get('date_fin'),
                'bungalow_numero': existing_distribution.get('bungalow_numero'),
                'session_numero': existing_distribution.get('session_numero')
            }
        })
    else:
        return jsonify({'has_active_distribution': False})

@app.route('/api/bungalows/count', methods=['GET'])
@login_required
def api_bungalows_count():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    count = len(db_ops.list_bungalows())
    db_manager.close()

    return jsonify({'count': count})

@app.route('/api/personnel/count', methods=['GET'])
@login_required
def api_personnel_count():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    count = len(db_ops.list_personnel_militaire())
    db_manager.close()

    return jsonify({'count': count})

# API endpoints for data loading
@app.route('/api/bungalows', methods=['GET'])
@login_required
def api_bungalows():
    """Get all bungalows for search"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalows = db_ops.list_bungalows()
    db_manager.close()

    return jsonify(bungalows)

@app.route('/api/personnel', methods=['GET'])
@login_required
def api_personnel():
    """Get all personnel for search"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    personnel = db_ops.list_personnel_with_grades_and_unites()
    db_manager.close()

    return jsonify(personnel)

@app.route('/api/sessions', methods=['GET'])
@login_required
def api_sessions():
    """Get all sessions for search"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    sessions = db_ops.list_sessions()
    db_manager.close()

    return jsonify(sessions)

# Route pour la nouvelle page de distribution
@app.route('/nouvelle_distribution', methods=['GET', 'POST'])
@login_required
def nouvelle_distribution():
    if request.method == 'POST':
        numero = request.form.get('numero')
        bungalow_id = request.form.get('bungalow_id')
        personnel_id = request.form.get('personnel_id')
        session_id = request.form.get('session_id')
        date_debut = request.form.get('date_debut')
        date_fin = request.form.get('date_fin')
        notes = request.form.get('notes')

        # Validation des champs obligatoires
        if not numero or not bungalow_id or not personnel_id or not date_debut:
            flash('Les champs Numéro, Bungalow, Personnel et Date de début sont obligatoires', 'error')
            return render_template('nouvelle_distribution.html')

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Vérifier si le numéro existe déjà
        existing_numero = db_ops.get_distribution_bungalow_by_numero(numero)
        if existing_numero:
            flash(f'Une distribution avec le numéro "{numero}" existe déjà', 'error')
            db_manager.close()
            return render_template('nouvelle_distribution.html')

        # Vérifier si le personnel a déjà une distribution active
        if personnel_id:
            existing_distribution = db_ops.check_personnel_has_active_distribution(int(personnel_id))
            if existing_distribution:
                db_manager.close()
                flash(f'Ce personnel militaire a déjà une distribution active (N° {existing_distribution.get("numero", "N/A")}) jusqu\'au {existing_distribution.get("date_fin", "indéterminée")}. Un personnel ne peut bénéficier que d\'une seule distribution à la fois.', 'error')
                return render_template('nouvelle_distribution.html')

        # Vérifier la capacité du bungalow
        if bungalow_id:
            bungalow = db_ops.get_bungalow_by_id(int(bungalow_id))
            if bungalow and bungalow.get('capacite', 0) <= 0:
                db_manager.close()
                flash(f'Le bungalow sélectionné n\'a plus de places disponibles. Capacité restante: 0', 'error')
                return render_template('nouvelle_distribution.html')

        # Créer la distribution
        distribution_id = db_ops.create_distribution_bungalow(
            numero,
            int(bungalow_id) if bungalow_id else None,
            int(personnel_id) if personnel_id else None,
            int(session_id) if session_id else None,
            date_debut,
            date_fin,
            notes
        )

        if distribution_id:
            # Get updated capacity stats for success message
            if bungalow_id:
                capacity_stats = db_ops.get_bungalow_capacity_stats(int(bungalow_id))
                if capacity_stats:
                    flash(f'Distribution créée avec succès! Capacité restante du bungalow: {capacity_stats["remaining"]} places', 'success')
                else:
                    flash('Distribution créée avec succès', 'success')
            else:
                flash('Distribution créée avec succès', 'success')
            db_manager.close()
            return redirect(url_for('distributions'))
        else:
            flash('Échec de la création de la distribution', 'error')
            db_manager.close()

    return render_template('nouvelle_distribution.html')

@app.route('/api/distributions', methods=['POST'])
@login_required
def api_create_distribution():
    """API endpoint for creating distributions via AJAX"""
    try:
        numero = request.form.get('numero')
        bungalow_id = request.form.get('bungalow_id')
        personnel_id = request.form.get('personnel_id')
        session_id = request.form.get('session_id')
        grade_id = request.form.get('grade_id')
        unite_id = request.form.get('unite_id')
        date_debut = request.form.get('date_debut')
        date_fin = request.form.get('date_fin')
        notes = request.form.get('notes')

        if not numero or not bungalow_id:
            return jsonify({'error': 'Le numéro et le bungalow sont obligatoires'}), 400

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Check if personnel already has an active distribution
        if personnel_id:
            existing_distribution = db_ops.check_personnel_has_active_distribution(int(personnel_id))
            if existing_distribution:
                db_manager.close()
                return jsonify({
                    'error': 'Personnel déjà assigné',
                    'message': 'Ce personnel militaire bénéficie actuellement d\'une distribution active.',
                    'details': {
                        'distribution_numero': existing_distribution.get("numero", "N/A"),
                        'bungalow_numero': existing_distribution.get("bungalow_numero", "N/A"),
                        'session_numero': existing_distribution.get("session_numero", "N/A"),
                        'date_fin': existing_distribution.get("date_fin", "Indéterminée")
                    },
                    'suggestion': 'Attendez la fin de la distribution actuelle ou modifiez-la pour libérer ce personnel.',
                    'type': 'validation_error'
                }), 400

        distribution_id = db_ops.create_distribution_bungalow(
            numero,
            int(bungalow_id) if bungalow_id else None,
            int(personnel_id) if personnel_id else None,
            int(session_id) if session_id else None,
            date_debut,
            date_fin,
            notes
        )

        db_manager.close()

        if distribution_id:
            return jsonify({'success': True, 'distribution_id': distribution_id, 'message': 'Distribution créée avec succès'})
        else:
            return jsonify({'error': 'Échec de la création de la distribution'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/bungalow/<int:bungalow_id>/capacity', methods=['GET'])
@login_required
def api_bungalow_capacity(bungalow_id):
    """API endpoint for getting bungalow capacity statistics"""
    try:
        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        capacity_stats = db_ops.get_bungalow_capacity_stats(bungalow_id)
        db_manager.close()

        if capacity_stats:
            return jsonify(capacity_stats)
        else:
            return jsonify({'error': 'Bungalow not found'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/distributions/count', methods=['GET'])
@login_required
def api_distributions_count():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    count = len(db_ops.list_distribution_bungalows())
    db_manager.close()

    return jsonify({'count': count})

@app.route('/api/recent_distributions', methods=['GET'])
@login_required
def api_recent_distributions():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    distributions = db_ops.list_distributions_with_details()
    # Get the 5 most recent distributions
    recent_distributions = sorted(distributions, key=lambda x: x.get('created_at', ''), reverse=True)[:5]

    db_manager.close()

    return jsonify(recent_distributions)

# Bungalow-Session Links routes
@app.route('/bungalow-session-links')
@login_required
def bungalow_session_links():
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    links = db_ops.list_bungalow_session_links()
    bungalows = db_ops.list_bungalows()
    sessions = db_ops.list_sessions()

    db_manager.close()

    return render_template('bungalow_session_links.html',
                         links=links,
                         bungalows=bungalows,
                         sessions=sessions)

@app.route('/bungalow-session-links/create', methods=['POST'])
@login_required
def create_bungalow_session_link():
    bungalow_id = request.form.get('bungalow_id')
    session_id = request.form.get('session_id')
    statut = request.form.get('statut', 'active')
    notes = request.form.get('notes')

    if not bungalow_id or not session_id:
        flash('Veuillez sélectionner un bungalow et une session', 'error')
        return redirect(url_for('bungalow_session_links'))

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    link_id = db_ops.create_bungalow_session_link(bungalow_id, session_id, statut, notes)

    if link_id:
        flash('Liaison créée avec succès', 'success')
    else:
        flash('Erreur lors de la création de la liaison', 'error')

    db_manager.close()
    return redirect(url_for('bungalow_session_links'))

@app.route('/bungalow-session-links/edit/<int:link_id>', methods=['POST'])
@login_required
def edit_bungalow_session_link(link_id):
    statut = request.form.get('statut')
    notes = request.form.get('notes')

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    success = db_ops.update_bungalow_session_link(link_id, statut, notes)

    if success:
        flash('Liaison modifiée avec succès', 'success')
    else:
        flash('Erreur lors de la modification de la liaison', 'error')

    db_manager.close()
    return redirect(url_for('bungalow_session_links'))

@app.route('/bungalow-session-links/delete/<int:link_id>', methods=['POST'])
@login_required
def delete_bungalow_session_link(link_id):
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    success = db_ops.delete_bungalow_session_link(link_id)

    if success:
        flash('Liaison supprimée avec succès', 'success')
    else:
        flash('Erreur lors de la suppression de la liaison', 'error')

    db_manager.close()
    return redirect(url_for('bungalow_session_links'))

# Print routes
@app.route('/print/distributions')
@login_required
def print_distributions():
    """Print distributions list"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get filter parameters
    search_term = request.args.get('search', '').strip()
    status_filter = request.args.get('status', '').strip()
    unite_filter = request.args.get('unite', '').strip()
    date_filter = request.args.get('date', '').strip()

    # Get distributions with details
    distributions = db_ops.list_distributions_with_details()

    # Apply filters if provided
    filtered_distributions = distributions
    filters_applied = bool(search_term or status_filter or unite_filter or date_filter)

    if search_term:
        filtered_distributions = [d for d in filtered_distributions
                                if search_term.lower() in str(d).lower()]

    if unite_filter:
        filtered_distributions = [d for d in filtered_distributions
                                if d.get('unite_description') and unite_filter in d.get('unite_description')]

    # Calculate statistics
    today = datetime.now().date()

    active_distributions = 0
    upcoming_distributions = 0
    expired_distributions = 0
    not_planned_distributions = 0

    for dist in filtered_distributions:
        date_debut = dist.get('date_debut')
        date_fin = dist.get('date_fin')

        if not date_debut:
            not_planned_distributions += 1
            dist['status'] = 'not_planned'
        else:
            try:
                debut_date = datetime.strptime(date_debut, '%Y-%m-%d').date()
                if debut_date > today:
                    upcoming_distributions += 1
                    dist['status'] = 'upcoming'
                elif not date_fin:
                    active_distributions += 1
                    dist['status'] = 'active'
                else:
                    try:
                        fin_date = datetime.strptime(date_fin, '%Y-%m-%d').date()
                        if fin_date >= today:
                            active_distributions += 1
                            dist['status'] = 'active'
                        else:
                            expired_distributions += 1
                            dist['status'] = 'expired'
                    except (ValueError, TypeError):
                        active_distributions += 1
                        dist['status'] = 'active'
            except (ValueError, TypeError):
                not_planned_distributions += 1
                dist['status'] = 'not_planned'

    # Get bungalow statistics
    all_bungalows = db_ops.list_bungalows()
    total_bungalows = len(all_bungalows)

    # Count distributed bungalows (active distributions)
    distributed_bungalows = set()
    for dist in filtered_distributions:
        date_debut = dist.get('date_debut')
        date_fin = dist.get('date_fin')
        bungalow_id = dist.get('bungalow_id')

        if date_debut and bungalow_id:
            try:
                debut_date = datetime.strptime(date_debut, '%Y-%m-%d').date()
                if debut_date <= today:
                    if not date_fin:
                        distributed_bungalows.add(bungalow_id)
                    else:
                        try:
                            fin_date = datetime.strptime(date_fin, '%Y-%m-%d').date()
                            if fin_date >= today:
                                distributed_bungalows.add(bungalow_id)
                        except (ValueError, TypeError):
                            distributed_bungalows.add(bungalow_id)
            except (ValueError, TypeError):
                pass

    distributed_count = len(distributed_bungalows)
    available_count = total_bungalows - distributed_count

    db_manager.close()

    # Prepare print data
    print_data = {
        'distributions': filtered_distributions,
        'today': today,
        'active_distributions': active_distributions,
        'upcoming_distributions': upcoming_distributions,
        'expired_distributions': expired_distributions,
        'not_planned_distributions': not_planned_distributions,
        'distributed_count': distributed_count,
        'available_count': available_count,
        'total_bungalows': total_bungalows,
        'filters_applied': filters_applied,
        'search_term': search_term,
        'status_filter': status_filter,
        'date_filter': date_filter,
        'document_title': 'Liste des Distributions',
        'organization_name': 'BANGHALAU - SYSTÈME DE GESTION',
        'print_date': datetime.now().strftime('%d/%m/%Y'),
        'print_time': datetime.now().strftime('%H:%M'),
        'current_user_name': 'Administrateur'  # You can get this from session
    }

    return render_template('Imprimer.html', **print_data)



@app.route('/print/distribution/<int:distribution_id>')
@login_required
def print_single_distribution(distribution_id):
    """Print single distribution document"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get distribution with details
    distribution = db_ops.get_distribution_with_details(distribution_id)

    if not distribution:
        flash('Distribution non trouvée', 'error')
        db_manager.close()
        return redirect(url_for('distributions'))

    # Prepare print data for single distribution
    print_data = {
        'distributions': [distribution],  # Single distribution in a list for template compatibility
        'today': datetime.now().strftime('%Y-%m-%d'),
        'document_title': f'Décision de Distribution - {distribution.get("numero", "")}',
        'organization_name': 'BANGHALAU - SYSTÈME DE GESTION',
        'print_date': datetime.now().strftime('%d/%m/%Y'),
        'print_time': datetime.now().strftime('%H:%M'),
        'current_user_name': 'Administrateur'
    }

    db_manager.close()
    return render_template('Imprimer.html', **print_data)



@app.route('/print/distributions/export')
@login_required
def export_distributions():
    """Export distributions to various formats"""
    format_type = request.args.get('format', 'pdf').lower()

    if format_type == 'pdf':
        # Redirect to print page with auto-print parameter
        return redirect(url_for('print_distributions') + '?auto_print=true')
    elif format_type == 'excel':
        # TODO: Implement Excel export
        flash('Export Excel en développement', 'info')
        return redirect(url_for('distributions'))
    elif format_type == 'csv':
        # TODO: Implement CSV export
        flash('Export CSV en développement', 'info')
        return redirect(url_for('distributions'))
    else:
        flash('Format non supporté', 'error')
        return redirect(url_for('distributions'))

# Backup and Restore routes
@app.route('/backup-restore')
@login_required
def backup_restore():
    """Backup and restore page"""
    return render_template('backup_restore.html')

@app.route('/api/backup/create', methods=['POST'])
@login_required
def create_backup():
    """Create database backup"""
    try:
        import shutil
        import zipfile
        from datetime import datetime

        backup_name = request.form.get('name', f'backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
        include_files = request.form.get('include_files') == 'true'

        # Create backups directory if it doesn't exist
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # Generate backup filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"{backup_name}_{timestamp}.zip"
        backup_path = os.path.join(backup_dir, backup_filename)

        # Create zip file
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add database file
            if os.path.exists('banghalau.db'):
                zipf.write('banghalau.db', 'banghalau.db')

            # Add uploaded files if requested
            if include_files and os.path.exists('uploads'):
                for root, dirs, files in os.walk('uploads'):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, '.')
                        zipf.write(file_path, arcname)

        # Save backup info to database
        db_manager = DatabaseManager()
        db_manager.connect()

        cursor = db_manager.connection.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                filename TEXT NOT NULL,
                size INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                type TEXT DEFAULT 'full',
                status TEXT DEFAULT 'success'
            )
        ''')

        file_size = os.path.getsize(backup_path)
        cursor.execute('''
            INSERT INTO backups (name, filename, size, type, status)
            VALUES (?, ?, ?, ?, ?)
        ''', (backup_name, backup_filename, file_size, 'full', 'success'))

        db_manager.connection.commit()
        db_manager.close()

        return jsonify({
            'success': True,
            'message': 'Sauvegarde créée avec succès',
            'filename': backup_filename,
            'size': file_size
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erreur lors de la création de la sauvegarde: {str(e)}'
        }), 500

@app.route('/api/backup/restore', methods=['POST'])
@login_required
def restore_backup():
    """Restore database from backup file"""
    try:
        import shutil
        import zipfile
        import tempfile

        if 'backup_file' not in request.files:
            return jsonify({'error': 'Aucun fichier fourni'}), 400

        file = request.files['backup_file']
        if file.filename == '':
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400

        # Create temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save uploaded file
            temp_file_path = os.path.join(temp_dir, file.filename)
            file.save(temp_file_path)

            # Handle different file types
            if file.filename.endswith('.zip'):
                # Extract zip file
                with zipfile.ZipFile(temp_file_path, 'r') as zipf:
                    zipf.extractall(temp_dir)

                # Find database file in extracted content
                db_file = None
                for root, dirs, files in os.walk(temp_dir):
                    for f in files:
                        if f.endswith('.db') or f == 'banghalau.db':
                            db_file = os.path.join(root, f)
                            break
                    if db_file:
                        break

                if not db_file:
                    return jsonify({'error': 'Aucun fichier de base de données trouvé dans l\'archive'}), 400

            elif file.filename.endswith(('.db', '.sqlite', '.sqlite3')):
                db_file = temp_file_path
            else:
                return jsonify({'error': 'Format de fichier non supporté'}), 400

            # Backup current database
            if os.path.exists('banghalau.db'):
                backup_current = f'banghalau_backup_before_restore_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
                shutil.copy2('banghalau.db', backup_current)

            # Replace current database
            shutil.copy2(db_file, 'banghalau.db')

        return jsonify({
            'success': True,
            'message': 'Base de données restaurée avec succès'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erreur lors de la restauration: {str(e)}'
        }), 500

@app.route('/api/backup/history', methods=['GET'])
@login_required
def backup_history():
    """Get backup history"""
    try:
        db_manager = DatabaseManager()
        db_manager.connect()

        cursor = db_manager.connection.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                filename TEXT NOT NULL,
                size INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                type TEXT DEFAULT 'full',
                status TEXT DEFAULT 'success'
            )
        ''')

        cursor.execute('''
            SELECT id, name, filename, size, created_at, type, status
            FROM backups
            ORDER BY created_at DESC
        ''')

        backups = []
        for row in cursor.fetchall():
            backups.append({
                'id': row[0],
                'name': row[1],
                'filename': row[2],
                'size': row[3] or 0,
                'date': row[4],
                'type': row[5],
                'status': row[6]
            })

        db_manager.close()

        return jsonify({
            'success': True,
            'backups': backups
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erreur lors du chargement de l\'historique: {str(e)}'
        }), 500

@app.route('/api/backup/system-info', methods=['GET'])
@login_required
def system_info():
    """Get system information"""
    try:
        import os
        from datetime import datetime

        # Database size
        db_size = 0
        if os.path.exists('banghalau.db'):
            db_size = os.path.getsize('banghalau.db')

        # Last modified
        last_modified = datetime.now()
        if os.path.exists('banghalau.db'):
            last_modified = datetime.fromtimestamp(os.path.getmtime('banghalau.db'))

        # Count tables
        db_manager = DatabaseManager()
        db_manager.connect()

        cursor = db_manager.connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        table_count = cursor.fetchone()[0]

        db_manager.close()

        return jsonify({
            'success': True,
            'db_size': db_size,
            'table_count': table_count,
            'last_modified': last_modified.isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erreur lors du chargement des informations système: {str(e)}'
        }), 500

@app.route('/api/backup/download/<int:backup_id>')
@login_required
def download_backup(backup_id):
    """Download backup file"""
    try:
        from flask import send_file

        db_manager = DatabaseManager()
        db_manager.connect()

        cursor = db_manager.connection.cursor()
        cursor.execute('SELECT filename FROM backups WHERE id = ?', (backup_id,))
        result = cursor.fetchone()

        if not result:
            return jsonify({'error': 'Sauvegarde non trouvée'}), 404

        filename = result[0]
        backup_path = os.path.join('backups', filename)

        if not os.path.exists(backup_path):
            return jsonify({'error': 'Fichier de sauvegarde non trouvé'}), 404

        db_manager.close()

        return send_file(backup_path, as_attachment=True, download_name=filename)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erreur lors du téléchargement: {str(e)}'
        }), 500

@app.route('/api/backup/delete/<int:backup_id>', methods=['DELETE'])
@login_required
def delete_backup(backup_id):
    """Delete backup"""
    try:
        db_manager = DatabaseManager()
        db_manager.connect()

        cursor = db_manager.connection.cursor()
        cursor.execute('SELECT filename FROM backups WHERE id = ?', (backup_id,))
        result = cursor.fetchone()

        if not result:
            return jsonify({'error': 'Sauvegarde non trouvée'}), 404

        filename = result[0]
        backup_path = os.path.join('backups', filename)

        # Delete file if exists
        if os.path.exists(backup_path):
            os.remove(backup_path)

        # Delete from database
        cursor.execute('DELETE FROM backups WHERE id = ?', (backup_id,))
        db_manager.connection.commit()
        db_manager.close()

        return jsonify({
            'success': True,
            'message': 'Sauvegarde supprimée avec succès'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erreur lors de la suppression: {str(e)}'
        }), 500

# Security management routes
@app.route('/security')
@login_required
def security_dashboard():
    """Security management dashboard"""
    # Get security status
    protection_status = db_protection.get_protection_status()
    security_report = db_security.get_security_report()

    return render_template('security_dashboard.html',
                         protection_status=protection_status,
                         security_report=security_report)

@app.route('/security/backup', methods=['POST'])
@login_required
def create_secure_backup():
    """Create encrypted backup"""
    try:
        backup_name = request.form.get('backup_name', f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        password = request.form.get('password')

        if not password:
            return jsonify({'success': False, 'message': 'كلمة مرور التشفير مطلوبة'})

        # Validate password
        is_valid, errors = db_security.validate_password_policy(password)
        if not is_valid:
            return jsonify({'success': False, 'message': 'كلمة المرور لا تلبي متطلبات الأمان', 'errors': errors})

        backup_path = f"backups/{backup_name}.bak"
        success, message = db_security.create_secure_backup(backup_path, password)

        if success:
            # Log security event
            db_security.log_security_event("BACKUP_CREATED", session.get('user_id'), f"Encrypted backup created: {backup_path}")
            return jsonify({'success': True, 'message': message, 'backup_path': backup_path})
        else:
            return jsonify({'success': False, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'})

@app.route('/security/unblock_ip', methods=['POST'])
@login_required
def unblock_ip():
    """Unblock an IP address"""
    try:
        ip_address = request.form.get('ip_address')

        if not ip_address:
            return jsonify({'success': False, 'message': 'عنوان IP مطلوب'})

        success = db_protection.unblock_ip(ip_address)

        if success:
            db_security.log_security_event("IP_UNBLOCKED", session.get('user_id'), f"IP {ip_address} unblocked")
            return jsonify({'success': True, 'message': f'تم إلغاء حظر عنوان IP: {ip_address}'})
        else:
            return jsonify({'success': False, 'message': f'عنوان IP {ip_address} غير محظور'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في إلغاء الحظر: {str(e)}'})

# Run the app
if __name__ == '__main__':
    print("🚀 Démarrage de BANGHALAU...")
    print("📍 URL: http://localhost:5000")
    print("👤 Utilisateur: admin")
    print("🔑 Mot de passe: admin123")
    print("🔒 Security: Enhanced Protection Enabled")
    print("=" * 50)

# Print distributions table route
@app.route('/print/distributions/table')
@login_required
def print_distributions_table():
    """Print distributions table with filtering"""
    return render_template('print_distributions_table.html')

# Filtre distributions route
@app.route('/filtre_distributions')
@login_required
def filtre_distributions():
    """Filtre des distributions avec filtres par unité et région"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get all data needed for filters
    unites_list = db_ops.list_unites()
    distributions_list = db_ops.list_distributions_with_details()

    # Define regions
    regions = [
        'الناحية العسكرية الأولى',
        'الناحية العسكرية الثانية',
        'الناحية العسكرية الثالثة',
        'الناحية العسكرية الرابعة',
        'الناحية العسكرية الخامسة',
        'الناحية العسكرية السادسة'
    ]

    # Add region to each distribution based on unite
    for dist in distributions_list:
        if dist.get('unite_id'):
            # Find the unite for this distribution
            unite = next((u for u in unites_list if u['id'] == dist['unite_id']), None)
            if unite and unite.get('region'):
                dist['region'] = unite['region']
            else:
                dist['region'] = 'غير محدد'
        else:
            dist['region'] = 'غير محدد'

    db_manager.close()

    return render_template('filtre_distributions.html',
                         distributions=distributions_list,
                         unites=unites_list,
                         regions=regions)

# Liste distributions with filters route
@app.route('/distributions/liste')
@login_required
def liste_distributions():
    """Liste des distributions avec filtres par unité et région"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get all data needed for filters
    unites_list = db_ops.list_unites()
    distributions_list = db_ops.list_distributions_with_details()

    # Define regions
    regions = [
        'الناحية العسكرية الأولى',
        'الناحية العسكرية الثانية',
        'الناحية العسكرية الثالثة',
        'الناحية العسكرية الرابعة',
        'الناحية العسكرية الخامسة',
        'الناحية العسكرية السادسة'
    ]

    # Add region to each distribution based on personnel region or unite_id
    for dist in distributions_list:
        # First check if personnel has a region field
        if hasattr(dist, 'personnel_region') and dist.get('personnel_region'):
            dist['region'] = dist['personnel_region']
        elif dist.get('unite_id'):
            # Assign region based on unite_id (you can customize this logic)
            unite_id = int(dist['unite_id'])
            if unite_id <= 10:
                dist['region'] = 'الناحية العسكرية الأولى'
            elif unite_id <= 20:
                dist['region'] = 'الناحية العسكرية الثانية'
            elif unite_id <= 30:
                dist['region'] = 'الناحية العسكرية الثالثة'
            elif unite_id <= 40:
                dist['region'] = 'الناحية العسكرية الرابعة'
            elif unite_id <= 50:
                dist['region'] = 'الناحية العسكرية الخامسة'
            else:
                dist['region'] = 'الناحية العسكرية السادسة'
        else:
            dist['region'] = 'غير محدد'

    db_manager.close()

    return render_template('filtre_distributions.html',
                         distributions=distributions_list,
                         unites=unites_list,
                         regions=regions)

@app.route('/distributions/print-preview')
@login_required
def print_preview_distributions():
    """Print preview for filtered distributions"""
    try:
        # Get filter parameters
        region = request.args.get('region', '')
        unite_id = request.args.get('unite', '')
        status = request.args.get('status', '')
        search = request.args.get('search', '')

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Get all distributions
        distributions_list = db_ops.list_distributions_with_details()

        # Add region to each distribution
        for dist in distributions_list:
            if hasattr(dist, 'personnel_region') and dist.get('personnel_region'):
                dist['region'] = dist['personnel_region']
            elif dist.get('unite_id'):
                unite_id_val = int(dist['unite_id'])
                if unite_id_val <= 10:
                    dist['region'] = 'الناحية العسكرية الأولى'
                elif unite_id_val <= 20:
                    dist['region'] = 'الناحية العسكرية الثانية'
                elif unite_id_val <= 30:
                    dist['region'] = 'الناحية العسكرية الثالثة'
                elif unite_id_val <= 40:
                    dist['region'] = 'الناحية العسكرية الرابعة'
                elif unite_id_val <= 50:
                    dist['region'] = 'الناحية العسكرية الخامسة'
                else:
                    dist['region'] = 'الناحية العسكرية السادسة'
            else:
                dist['region'] = 'غير محدد'

        # Apply filters
        filtered_distributions = []
        today = datetime.now().date()

        for dist in distributions_list:
            # Region filter
            if region and dist.get('region') != region:
                continue

            # Unite filter
            if unite_id and str(dist.get('unite_id', '')) != str(unite_id):
                continue

            # Status filter
            dist_status = 'upcoming'
            if dist.get('date_debut'):
                try:
                    start_date = datetime.strptime(dist['date_debut'], '%Y-%m-%d').date()
                    if start_date <= today:
                        if dist.get('date_fin'):
                            end_date = datetime.strptime(dist['date_fin'], '%Y-%m-%d').date()
                            dist_status = 'active' if end_date >= today else 'expired'
                        else:
                            dist_status = 'active'
                except (ValueError, TypeError):
                    dist_status = 'upcoming'

            if status and dist_status != status:
                continue

            # Search filter
            if search:
                search_fields = [
                    str(dist.get('numero', '')),
                    str(dist.get('personnel_nom', '')),
                    str(dist.get('personnel_prenom', '')),
                    str(dist.get('personnel_grade', '')),
                    str(dist.get('unite_description', '')),
                    str(dist.get('bungalow_numero', '')),
                    str(dist.get('bungalow_endroit', ''))
                ]
                search_text = ' '.join(search_fields).lower()
                if search.lower() not in search_text:
                    continue

            filtered_distributions.append(dist)

        # Calculate statistics
        total_count = len(filtered_distributions)
        active_count = sum(1 for d in filtered_distributions if d.get('date_debut') and
                          (not d.get('date_fin') or datetime.strptime(d['date_fin'], '%Y-%m-%d').date() >= today))
        expired_count = sum(1 for d in filtered_distributions if d.get('date_fin') and
                           datetime.strptime(d['date_fin'], '%Y-%m-%d').date() < today)
        upcoming_count = sum(1 for d in filtered_distributions if d.get('date_debut') and
                            datetime.strptime(d['date_debut'], '%Y-%m-%d').date() > today)

        db_manager.close()

        # Prepare context
        context = {
            'distributions': filtered_distributions,
            'total_count': total_count,
            'active_count': active_count,
            'expired_count': expired_count,
            'upcoming_count': upcoming_count,
            'filters': {
                'region': region,
                'unite': unite_id,
                'status': status,
                'search': search
            },
            'print_date': datetime.now().strftime('%Y-%m-%d'),
            'print_time': datetime.now().strftime('%H:%M'),
            'total_available': len(filtered_distributions)
        }

        return render_template('Imprimer -Globale.html', **context)

    except Exception as e:
        print(f"Error in print_preview_distributions: {str(e)}")
        return render_template('error.html',
                             error_message="خطأ في إنشاء معاينة الطباعة",
                             error_details=str(e)), 500

# API routes for the new print table
@app.route('/api/distributions/detailed')
@login_required
def api_distributions_detailed():
    """Get detailed distributions data for table"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    try:
        # Get distributions with all details
        distributions = db_ops.list_distributions_with_details()

        # Add status calculation and region info
        today = datetime.now().date()

        for dist in distributions:
            # Calculate status
            date_debut = dist.get('date_debut')
            date_fin = dist.get('date_fin')

            if not date_debut:
                dist['status'] = 'not_planned'
            else:
                try:
                    debut_date = datetime.strptime(date_debut, '%Y-%m-%d').date()
                    if debut_date > today:
                        dist['status'] = 'upcoming'
                    elif not date_fin:
                        dist['status'] = 'active'
                    else:
                        try:
                            fin_date = datetime.strptime(date_fin, '%Y-%m-%d').date()
                            if fin_date >= today:
                                dist['status'] = 'active'
                            else:
                                dist['status'] = 'expired'
                        except (ValueError, TypeError):
                            dist['status'] = 'active'
                except (ValueError, TypeError):
                    dist['status'] = 'not_planned'

            # Add region info based on unite description
            unite_desc = dist.get('unite_description', '')
            if 'الناحية العسكرية الأولى' in unite_desc or 'ن ع 1' in unite_desc:
                dist['region'] = 'الناحية العسكرية الأولى'
            elif 'الناحية العسكرية الثانية' in unite_desc or 'ن ع 2' in unite_desc:
                dist['region'] = 'الناحية العسكرية الثانية'
            elif 'الناحية العسكرية الثالثة' in unite_desc or 'ن ع 3' in unite_desc:
                dist['region'] = 'الناحية العسكرية الثالثة'
            elif 'الناحية العسكرية الرابعة' in unite_desc or 'ن ع 4' in unite_desc:
                dist['region'] = 'الناحية العسكرية الرابعة'
            elif 'الناحية العسكرية الخامسة' in unite_desc or 'ن ع 5' in unite_desc:
                dist['region'] = 'الناحية العسكرية الخامسة'
            elif 'الناحية العسكرية السادسة' in unite_desc or 'ن ع 6' in unite_desc:
                dist['region'] = 'الناحية العسكرية السادسة'
            else:
                dist['region'] = 'غير محدد'

        db_manager.close()
        return jsonify(distributions)

    except Exception as e:
        db_manager.close()
        return jsonify({'error': str(e)}), 500

@app.route('/print/distributions/bulk')
@login_required
def print_distributions_bulk():
    """Print multiple distributions"""
    db_manager = DatabaseManager()

    try:
        db_manager.connect()

        # Get distribution IDs from query parameter
        ids_param = request.args.get('ids', '')
        print(f"Received IDs parameter: {ids_param}")  # Debug log

        if not ids_param:
            flash('Aucune distribution sélectionnée', 'error')
            return redirect(url_for('distributions'))

        # Parse IDs
        try:
            distribution_ids = [int(id.strip()) for id in ids_param.split(',') if id.strip()]
            print(f"Parsed distribution IDs: {distribution_ids}")  # Debug log
        except ValueError as ve:
            print(f"ValueError parsing IDs: {ve}")
            flash('IDs de distribution invalides', 'error')
            return redirect(url_for('distributions'))

        if not distribution_ids:
            flash('Aucune distribution sélectionnée', 'error')
            return redirect(url_for('distributions'))

        # Get distributions with details using the existing method
        db_ops = DatabaseOperations(db_manager)
        all_distributions = db_ops.list_distributions_with_details()

        # Filter distributions by selected IDs
        distributions = []
        for dist in all_distributions:
            if dist.get('id') in distribution_ids:
                distributions.append(dist)

        print(f"Found {len(distributions)} distributions")  # Debug log

        if not distributions:
            flash('Aucune distribution valide trouvée', 'error')
            return redirect(url_for('distributions'))

        # Prepare print data
        print_data = {
            'distributions': distributions,
            'total_count': len(distributions),
            'document_title': f'Décisions de Distribution - {len(distributions)} distribution(s)',
            'organization_name': 'BANGHALAU - SYSTÈME DE GESTION',
            'print_date': datetime.now().strftime('%d/%m/%Y'),
            'print_time': datetime.now().strftime('%H:%M'),
            'current_user_name': 'Administrateur',
            'is_bulk_print': True
        }

        return render_template('Imprimer.html', **print_data)

    except Exception as e:
        print(f"Error in bulk print: {e}")
        import traceback
        traceback.print_exc()
        flash('حدث خطأ أثناء تحضير الطباعة الجماعية', 'error')
        return redirect(url_for('distributions'))

    finally:
        try:
            db_manager.close()
        except:
            pass

@app.route('/distributions/bulk-delete', methods=['POST'])
@login_required
def bulk_delete_distributions():
    """Delete multiple distributions"""
    db_manager = DatabaseManager()

    try:
        db_manager.connect()

        # Get distribution IDs from form
        distribution_ids = request.form.getlist('distribution_ids')
        print(f"Received distribution IDs for deletion: {distribution_ids}")  # Debug log

        if not distribution_ids:
            flash('Aucune distribution sélectionnée pour suppression', 'error')
            return redirect(url_for('distributions'))

        # Convert to integers
        try:
            distribution_ids = [int(id) for id in distribution_ids]
        except ValueError:
            flash('IDs de distribution invalides', 'error')
            return redirect(url_for('distributions'))

        # Delete distributions
        db_ops = DatabaseOperations(db_manager)
        deleted_count = 0

        for dist_id in distribution_ids:
            try:
                if db_ops.delete_distribution(dist_id):
                    deleted_count += 1
            except Exception as e:
                print(f"Error deleting distribution {dist_id}: {e}")

        if deleted_count > 0:
            flash(f'{deleted_count} distribution(s) supprimée(s) avec succès', 'success')
        else:
            flash('Aucune distribution n\'a pu être supprimée', 'error')

        return redirect(url_for('distributions'))

    except Exception as e:
        print(f"Error in bulk delete: {e}")
        import traceback
        traceback.print_exc()
        flash('حدث خطأ أثناء حذف التوزيعات', 'error')
        return redirect(url_for('distributions'))

    finally:
        try:
            db_manager.close()
        except:
            pass

@app.route('/api/distributions/export/csv')
@login_required
def export_distributions_csv():
    """Export selected distributions to CSV"""
    try:
        # Get distribution IDs from query parameter
        ids_param = request.args.get('ids', '')

        if not ids_param:
            return jsonify({'error': 'No distributions selected'}), 400

        # Parse IDs
        try:
            distribution_ids = [int(id.strip()) for id in ids_param.split(',') if id.strip()]
        except ValueError:
            return jsonify({'error': 'Invalid distribution IDs'}), 400

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Get distributions with details
        all_distributions = db_ops.list_distributions_with_details()

        # Filter distributions by selected IDs
        distributions = []
        for dist in all_distributions:
            if dist.get('id') in distribution_ids:
                distributions.append(dist)

        db_manager.close()

        if not distributions:
            return jsonify({'error': 'No valid distributions found'}), 404

        # Create CSV content
        import csv
        import io

        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'Numéro', 'Personnel', 'Grade', 'Unité', 'Bungalow', 'Endroit',
            'Session', 'Date Début', 'Date Fin', 'Statut'
        ])

        # Write data
        for dist in distributions:
            writer.writerow([
                dist.get('numero', ''),
                f"{dist.get('personnel_prenom', '')} {dist.get('personnel_nom', '')}".strip(),
                dist.get('personnel_grade', ''),
                dist.get('unite_description', ''),
                dist.get('bungalow_numero', ''),
                dist.get('bungalow_endroit', ''),
                dist.get('session_numero', ''),
                dist.get('date_debut', ''),
                dist.get('date_fin', ''),
                'Active' if dist.get('date_fin') and dist.get('date_fin') >= datetime.now().strftime('%Y-%m-%d') else 'Expired'
            ])

        # Prepare response
        from flask import Response

        output.seek(0)
        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment; filename=distributions_selected_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            }
        )

    except Exception as e:
        print(f"Error in CSV export: {e}")
        return jsonify({'error': 'Export failed'}), 500



@app.route('/test_form')
@login_required
def test_form():
    """Test form page for debugging form submissions"""
    return render_template('test_form.html')

@app.route('/test_submit', methods=['POST'])
@login_required
def test_submit():
    """Test form submission handler"""
    test_name = request.form.get('test_name')
    print(f"TEST FORM SUBMITTED: {test_name}")
    flash(f'تم إرسال الاختبار بنجاح: {test_name}', 'success')
    return redirect(url_for('test_form'))

@app.route('/bungalows/add_simple')
@login_required
def add_bungalow_simple():
    """Simple bungalow add page without JavaScript"""
    return render_template('add_bungalow_simple.html')





@app.route('/users_management')
@login_required
def users_management():
    """Users management page"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Get all users
    users = db_ops.get_all_users()

    # Calculate statistics
    active_users_count = sum(1 for user in users if user.get('is_active', True))
    admin_users_count = sum(1 for user in users if user.get('role') == 'admin')

    # Calculate recent logins (last 24 hours)
    recent_logins_count = 0
    for user in users:
        if user.get('last_login'):
            try:
                # Parse the datetime string
                last_login = datetime.fromisoformat(user['last_login'].replace('Z', '+00:00'))
                if (datetime.now() - last_login).days < 1:
                    recent_logins_count += 1
            except (ValueError, AttributeError):
                # Skip if date parsing fails
                pass

    db_manager.close()

    return render_template('users_management.html',
                         users=users,
                         active_users_count=active_users_count,
                         admin_users_count=admin_users_count,
                         recent_logins_count=recent_logins_count)

@app.route('/users_simple')
@login_required
def users_simple():
    """Simple users management page"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    users = db_ops.get_all_users()
    db_manager.close()

    return render_template('users_simple.html', users=users)

@app.route('/control_panel')
@login_required
def control_panel():
    """Control panel for interface customization"""
    return render_template('control_panel.html')

@app.route('/api/users/add', methods=['POST'])
@login_required
def add_user_simple():
    """Add user - simple method"""
    try:
        username = request.form.get('username')
        full_name = request.form.get('full_name')
        email = request.form.get('email')
        password = request.form.get('password')
        role = request.form.get('role')
        is_active = request.form.get('is_active', '1') == '1'

        if not username or not password or not role:
            flash('يرجى ملء جميع الحقول المطلوبة', 'error')
            return redirect(url_for('users_simple'))

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Check if username exists
        existing_user = db_ops.get_user_by_username(username)
        if existing_user:
            flash('اسم المستخدم موجود بالفعل', 'error')
            db_manager.close()
            return redirect(url_for('users_simple'))

        # Create user
        user_data = {
            'username': username,
            'password': generate_password_hash(password),
            'full_name': full_name,
            'email': email,
            'role': role,
            'is_active': is_active,
            'created_at': datetime.now()
        }

        user_id = db_ops.create_user(user_data)
        db_manager.close()

        if user_id:
            flash('تم إضافة المستخدم بنجاح', 'success')
        else:
            flash('خطأ في إضافة المستخدم', 'error')

        return redirect(url_for('users_simple'))

    except Exception as e:
        print(f"Error adding user: {e}")
        flash('خطأ في إضافة المستخدم', 'error')
        return redirect(url_for('users_simple'))

@app.route('/users/edit/<int:user_id>')
@login_required
def edit_user_simple(user_id):
    """Edit user - simple method"""
    try:
        new_username = request.args.get('username')
        new_role = request.args.get('role')

        if new_username and new_role:
            db_manager = DatabaseManager()
            db_manager.connect()
            db_ops = DatabaseOperations(db_manager)

            success = db_ops.update_user(user_id, {
                'username': new_username,
                'role': new_role
            })

            db_manager.close()

            if success:
                flash('تم تعديل المستخدم بنجاح', 'success')
            else:
                flash('خطأ في تعديل المستخدم', 'error')

        return redirect(url_for('users_simple'))

    except Exception as e:
        print(f"Error editing user: {e}")
        flash('خطأ في تعديل المستخدم', 'error')
        return redirect(url_for('users_simple'))

@app.route('/users/delete/<int:user_id>')
@login_required
def delete_user_simple(user_id):
    """Delete user - simple method"""
    try:
        # Prevent deleting current user
        if user_id == current_user.id:
            flash('لا يمكن حذف المستخدم الحالي', 'error')
            return redirect(url_for('users_simple'))

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        success = db_ops.delete_user(user_id)
        db_manager.close()

        if success:
            flash('تم حذف المستخدم بنجاح', 'success')
        else:
            flash('خطأ في حذف المستخدم', 'error')

        return redirect(url_for('users_simple'))

    except Exception as e:
        print(f"Error deleting user: {e}")
        flash('خطأ في حذف المستخدم', 'error')
        return redirect(url_for('users_simple'))

# API Routes for Users Management
@app.route('/api/users', methods=['POST'])
@login_required
def api_create_user():
    """Create a new user"""
    try:
        data = request.get_json()

        # Validate required fields
        if not data.get('username') or not data.get('password'):
            return jsonify({'success': False, 'message': 'Nom d\'utilisateur et mot de passe requis'}), 400

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Check if username already exists
        existing_user = db_ops.get_user_by_username(data['username'])
        if existing_user:
            db_manager.close()
            return jsonify({'success': False, 'message': 'Nom d\'utilisateur déjà existant'}), 400

        # Create user
        user_data = {
            'username': data['username'],
            'password': generate_password_hash(data['password']),
            'full_name': data.get('full_name', ''),
            'email': data.get('email', ''),
            'role': data.get('role', 'user'),
            'is_active': data.get('is_active', True),
            'created_at': datetime.now()
        }

        user_id = db_ops.create_user(user_data)
        db_manager.close()

        return jsonify({'success': True, 'message': 'Utilisateur créé avec succès', 'user_id': user_id})

    except Exception as e:
        print(f"Error creating user: {e}")
        return jsonify({'success': False, 'message': 'Erreur lors de la création de l\'utilisateur'}), 500

@app.route('/api/users/<int:user_id>', methods=['GET'])
@login_required
def api_get_user(user_id):
    """Get user by ID"""
    try:
        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        user = db_ops.get_user_by_id(user_id)
        db_manager.close()

        if user:
            return jsonify({'success': True, 'user': user})
        else:
            return jsonify({'success': False, 'message': 'المستخدم غير موجود'}), 404

    except Exception as e:
        print(f"Error getting user: {e}")
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء جلب بيانات المستخدم'}), 500

@app.route('/api/users/<int:user_id>', methods=['PUT'])
@login_required
def api_update_user(user_id):
    """Update user"""
    try:
        data = request.get_json()

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Check if user exists
        user = db_ops.get_user_by_id(user_id)
        if not user:
            db_manager.close()
            return jsonify({'success': False, 'message': 'المستخدم غير موجود'}), 404

        # Update user data
        update_data = {
            'username': data.get('username', user['username']),
            'full_name': data.get('full_name', user.get('full_name', '')),
            'email': data.get('email', user.get('email', '')),
            'role': data.get('role', user.get('role', 'user')),
            'is_active': data.get('is_active', user.get('is_active', True))
        }

        success = db_ops.update_user(user_id, update_data)
        db_manager.close()

        if success:
            return jsonify({'success': True, 'message': 'تم تحديث المستخدم بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'فشل في تحديث المستخدم'}), 500

    except Exception as e:
        print(f"Error updating user: {e}")
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء تحديث المستخدم'}), 500

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
@login_required
def api_delete_user(user_id):
    """Delete user"""
    try:
        # Prevent deleting current user
        if user_id == current_user.id:
            return jsonify({'success': False, 'message': 'لا يمكن حذف المستخدم الحالي'}), 400

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        success = db_ops.delete_user(user_id)
        db_manager.close()

        if success:
            return jsonify({'success': True, 'message': 'تم حذف المستخدم بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'فشل في حذف المستخدم'}), 500

    except Exception as e:
        print(f"Error deleting user: {e}")
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف المستخدم'}), 500

@app.route('/api/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
def api_toggle_user_status(user_id):
    """Toggle user active status"""
    try:
        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Get current user status
        user = db_ops.get_user_by_id(user_id)
        if not user:
            db_manager.close()
            return jsonify({'success': False, 'message': 'المستخدم غير موجود'}), 404

        # Toggle status
        new_status = not user.get('is_active', True)
        success = db_ops.update_user(user_id, {'is_active': new_status})
        db_manager.close()

        if success:
            status_text = 'تم تفعيل' if new_status else 'تم إلغاء تفعيل'
            return jsonify({'success': True, 'message': f'{status_text} المستخدم بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'فشل في تغيير حالة المستخدم'}), 500

    except Exception as e:
        print(f"Error toggling user status: {e}")
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء تغيير حالة المستخدم'}), 500

@app.route('/api/users/<int:user_id>/reset-password', methods=['POST'])
@login_required
def api_reset_user_password(user_id):
    """Reset user password"""
    try:
        data = request.get_json()
        new_password = data.get('new_password')

        if not new_password or len(new_password) < 6:
            return jsonify({'success': False, 'message': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'}), 400

        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)

        # Hash new password
        hashed_password = generate_password_hash(new_password)
        success = db_ops.update_user(user_id, {'password': hashed_password})
        db_manager.close()

        if success:
            return jsonify({'success': True, 'message': 'تم إعادة تعيين كلمة المرور بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'فشل في إعادة تعيين كلمة المرور'}), 500

    except Exception as e:
        print(f"Error resetting password: {e}")
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء إعادة تعيين كلمة المرور'}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
