#!/usr/bin/env python3
"""
BANGHALAU Desktop Application - No Web Server Required
نظام إدارة توزيع البنغالوهات - تطبيق سطح المكتب
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
import os
from datetime import datetime

class BanghalauDesktop:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BANGHALAU - نظام إدارة توزيع البنغالوهات")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # Initialize database
        self.init_database()

        # Create main interface
        self.create_main_interface()

        # Show login first
        self.show_login()

    def init_database(self):
        """Initialize SQLite database"""
        self.conn = sqlite3.connect('banghalau_desktop.db')
        self.cursor = self.conn.cursor()

        # Create tables
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE,
                password TEXT,
                email TEXT
            )
        ''')

        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS bungalows (
                id INTEGER PRIMARY KEY,
                number TEXT UNIQUE,
                location TEXT,
                capacity INTEGER,
                status TEXT DEFAULT 'available'
            )
        ''')

        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS personnel (
                id INTEGER PRIMARY KEY,
                name TEXT,
                rank TEXT,
                unit TEXT,
                service_number TEXT UNIQUE
            )
        ''')

        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS allocations (
                id INTEGER PRIMARY KEY,
                personnel_id INTEGER,
                bungalow_id INTEGER,
                start_date TEXT,
                end_date TEXT,
                status TEXT DEFAULT 'active',
                FOREIGN KEY (personnel_id) REFERENCES personnel (id),
                FOREIGN KEY (bungalow_id) REFERENCES bungalows (id)
            )
        ''')

        # Insert default admin user if not exists
        self.cursor.execute("SELECT * FROM users WHERE username = 'admin'")
        if not self.cursor.fetchone():
            self.cursor.execute(
                "INSERT INTO users (username, password, email) VALUES (?, ?, ?)",
                ('admin', 'admin123', '<EMAIL>')
            )

        self.conn.commit()

    def create_main_interface(self):
        """Create the main application interface"""
        # Main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = tk.Label(
            self.main_frame,
            text="🏠 BANGHALAU - نظام إدارة توزيع البنغالوهات",
            font=("Arial", 18, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=10)

        # Notebook for tabs
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=10)

        # Create tabs
        self.create_dashboard_tab()
        self.create_bungalows_tab()
        self.create_personnel_tab()
        self.create_allocations_tab()
        self.create_reports_tab()

        # Status bar
        self.status_bar = tk.Label(
            self.root,
            text="جاهز - مرحباً بك في نظام BANGHALAU",
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#34495e',
            fg='white'
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Hide main interface initially
        self.main_frame.pack_forget()

    def create_dashboard_tab(self):
        """Create dashboard tab"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="📊 لوحة التحكم")

        # Welcome message
        welcome_label = tk.Label(
            dashboard_frame,
            text="مرحباً بك في نظام إدارة توزيع البنغالوهات",
            font=("Arial", 16, "bold"),
            fg='#2c3e50'
        )
        welcome_label.pack(pady=20)

        # Statistics frame
        stats_frame = ttk.LabelFrame(dashboard_frame, text="الإحصائيات", padding=20)
        stats_frame.pack(fill=tk.X, padx=20, pady=10)

        # Create statistics display
        self.stats_labels = {}
        stats_info = [
            ("total_bungalows", "إجمالي البنغالوهات"),
            ("available_bungalows", "البنغالوهات المتاحة"),
            ("occupied_bungalows", "البنغالوهات المشغولة"),
            ("total_personnel", "إجمالي الأفراد")
        ]

        for i, (key, label) in enumerate(stats_info):
            row = i // 2
            col = i % 2

            stat_frame = tk.Frame(stats_frame)
            stat_frame.grid(row=row, column=col, padx=20, pady=10, sticky="ew")

            tk.Label(stat_frame, text=label, font=("Arial", 12)).pack()
            self.stats_labels[key] = tk.Label(
                stat_frame,
                text="0",
                font=("Arial", 20, "bold"),
                fg='#3498db'
            )
            self.stats_labels[key].pack()

        # Configure grid weights
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)

        # Refresh button
        refresh_btn = tk.Button(
            dashboard_frame,
            text="🔄 تحديث الإحصائيات",
            command=self.refresh_statistics,
            bg='#3498db',
            fg='white',
            font=("Arial", 12),
            padx=20,
            pady=10
        )
        refresh_btn.pack(pady=20)

        # Load initial statistics
        self.refresh_statistics()

    def create_bungalows_tab(self):
        """Create bungalows management tab"""
        bungalows_frame = ttk.Frame(self.notebook)
        self.notebook.add(bungalows_frame, text="🏠 إدارة البنغالوهات")

        # Control buttons
        btn_frame = tk.Frame(bungalows_frame)
        btn_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Button(
            btn_frame, text="➕ إضافة بنغالو",
            command=self.add_bungalow,
            bg='#27ae60', fg='white', font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)

        tk.Button(
            btn_frame, text="✏️ تعديل",
            command=self.edit_bungalow,
            bg='#f39c12', fg='white', font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)

        tk.Button(
            btn_frame, text="🗑️ حذف",
            command=self.delete_bungalow,
            bg='#e74c3c', fg='white', font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)

        tk.Button(
            btn_frame, text="🔄 تحديث",
            command=self.refresh_bungalows,
            bg='#3498db', fg='white', font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)

        # Bungalows list
        columns = ("ID", "رقم البنغالو", "الموقع", "السعة", "الحالة")
        self.bungalows_tree = ttk.Treeview(bungalows_frame, columns=columns, show="headings")

        for col in columns:
            self.bungalows_tree.heading(col, text=col)
            self.bungalows_tree.column(col, width=150)

        # Scrollbar for bungalows list
        bungalows_scroll = ttk.Scrollbar(bungalows_frame, orient=tk.VERTICAL, command=self.bungalows_tree.yview)
        self.bungalows_tree.configure(yscrollcommand=bungalows_scroll.set)

        self.bungalows_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        bungalows_scroll.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)

        # Load bungalows data
        self.refresh_bungalows()

    def create_personnel_tab(self):
        """Create personnel management tab"""
        personnel_frame = ttk.Frame(self.notebook)
        self.notebook.add(personnel_frame, text="👥 إدارة الأفراد")

        # Control buttons
        btn_frame = tk.Frame(personnel_frame)
        btn_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Button(
            btn_frame, text="➕ إضافة فرد",
            command=self.add_personnel,
            bg='#27ae60', fg='white', font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)

        tk.Button(
            btn_frame, text="✏️ تعديل",
            command=self.edit_personnel,
            bg='#f39c12', fg='white', font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)

        tk.Button(
            btn_frame, text="🗑️ حذف",
            command=self.delete_personnel,
            bg='#e74c3c', fg='white', font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)

        tk.Button(
            btn_frame, text="🔄 تحديث",
            command=self.refresh_personnel,
            bg='#3498db', fg='white', font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)

        # Personnel list
        columns = ("ID", "الاسم", "الرتبة", "الوحدة", "الرقم العسكري")
        self.personnel_tree = ttk.Treeview(personnel_frame, columns=columns, show="headings")

        for col in columns:
            self.personnel_tree.heading(col, text=col)
            self.personnel_tree.column(col, width=150)

        # Scrollbar for personnel list
        personnel_scroll = ttk.Scrollbar(personnel_frame, orient=tk.VERTICAL, command=self.personnel_tree.yview)
        self.personnel_tree.configure(yscrollcommand=personnel_scroll.set)

        self.personnel_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        personnel_scroll.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)

        # Load personnel data
        self.refresh_personnel()

    def create_allocations_tab(self):
        """Create allocations management tab"""
        allocations_frame = ttk.Frame(self.notebook)
        self.notebook.add(allocations_frame, text="📋 إدارة التوزيع")

        # Control buttons
        btn_frame = tk.Frame(allocations_frame)
        btn_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Button(
            btn_frame, text="➕ توزيع جديد",
            command=self.add_allocation,
            bg='#27ae60', fg='white', font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)

        tk.Button(
            btn_frame, text="🔚 إنهاء التوزيع",
            command=self.end_allocation,
            bg='#e74c3c', fg='white', font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)

        tk.Button(
            btn_frame, text="🔄 تحديث",
            command=self.refresh_allocations,
            bg='#3498db', fg='white', font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)

        # Allocations list
        columns = ("ID", "اسم الفرد", "رقم البنغالو", "تاريخ البداية", "تاريخ النهاية", "الحالة")
        self.allocations_tree = ttk.Treeview(allocations_frame, columns=columns, show="headings")

        for col in columns:
            self.allocations_tree.heading(col, text=col)
            self.allocations_tree.column(col, width=150)

        # Scrollbar for allocations list
        allocations_scroll = ttk.Scrollbar(allocations_frame, orient=tk.VERTICAL, command=self.allocations_tree.yview)
        self.allocations_tree.configure(yscrollcommand=allocations_scroll.set)

        self.allocations_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        allocations_scroll.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)

        # Load allocations data
        self.refresh_allocations()

    def create_reports_tab(self):
        """Create reports tab"""
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="📊 التقارير")

        # Reports content
        tk.Label(
            reports_frame,
            text="📊 تقارير النظام",
            font=("Arial", 16, "bold"),
            fg='#2c3e50'
        ).pack(pady=20)

        # Report buttons
        report_buttons = [
            ("📋 تقرير البنغالوهات المتاحة", self.show_available_bungalows_report),
            ("🏠 تقرير البنغالوهات المشغولة", self.show_occupied_bungalows_report),
            ("👥 تقرير الأفراد", self.show_personnel_report),
            ("📊 تقرير الإحصائيات العامة", self.show_general_statistics_report)
        ]

        for text, command in report_buttons:
            tk.Button(
                reports_frame,
                text=text,
                command=command,
                bg='#3498db',
                fg='white',
                font=("Arial", 12),
                padx=20,
                pady=10,
                width=30
            ).pack(pady=10)

    def show_login(self):
        """Show login dialog"""
        login_window = tk.Toplevel(self.root)
        login_window.title("تسجيل الدخول - BANGHALAU")
        login_window.geometry("400x300")
        login_window.configure(bg='#ecf0f1')
        login_window.transient(self.root)
        login_window.grab_set()

        # Center the login window
        login_window.geometry("+{}+{}".format(
            int(self.root.winfo_screenwidth()/2 - 200),
            int(self.root.winfo_screenheight()/2 - 150)
        ))

        # Login form
        tk.Label(
            login_window,
            text="🏠 BANGHALAU",
            font=("Arial", 20, "bold"),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).pack(pady=20)

        tk.Label(
            login_window,
            text="نظام إدارة توزيع البنغالوهات",
            font=("Arial", 12),
            bg='#ecf0f1',
            fg='#7f8c8d'
        ).pack(pady=5)

        # Credentials info
        info_frame = tk.Frame(login_window, bg='#d5dbdb', relief=tk.RAISED, bd=2)
        info_frame.pack(pady=20, padx=40, fill=tk.X)

        tk.Label(
            info_frame,
            text="بيانات تسجيل الدخول:",
            font=("Arial", 10, "bold"),
            bg='#d5dbdb'
        ).pack(pady=5)

        tk.Label(
            info_frame,
            text="اسم المستخدم: admin",
            font=("Arial", 10),
            bg='#d5dbdb'
        ).pack()

        tk.Label(
            info_frame,
            text="كلمة المرور: admin123",
            font=("Arial", 10),
            bg='#d5dbdb'
        ).pack(pady=(0, 5))

        # Login form
        form_frame = tk.Frame(login_window, bg='#ecf0f1')
        form_frame.pack(pady=20, padx=40, fill=tk.X)

        tk.Label(form_frame, text="اسم المستخدم:", bg='#ecf0f1').pack(anchor=tk.W)
        username_entry = tk.Entry(form_frame, font=("Arial", 12))
        username_entry.pack(fill=tk.X, pady=(0, 10))
        username_entry.insert(0, "admin")  # Pre-fill

        tk.Label(form_frame, text="كلمة المرور:", bg='#ecf0f1').pack(anchor=tk.W)
        password_entry = tk.Entry(form_frame, show="*", font=("Arial", 12))
        password_entry.pack(fill=tk.X, pady=(0, 20))
        password_entry.insert(0, "admin123")  # Pre-fill

        def login():
            username = username_entry.get()
            password = password_entry.get()

            # Check credentials
            self.cursor.execute(
                "SELECT * FROM users WHERE username = ? AND password = ?",
                (username, password)
            )

            if self.cursor.fetchone():
                login_window.destroy()
                self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                self.status_bar.config(text=f"مرحباً {username} - تم تسجيل الدخول بنجاح")
                messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح!")
            else:
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")

        tk.Button(
            form_frame,
            text="🚀 تسجيل الدخول",
            command=login,
            bg='#3498db',
            fg='white',
            font=("Arial", 12),
            padx=20,
            pady=5
        ).pack()

        # Bind Enter key to login
        login_window.bind('<Return>', lambda e: login())
        username_entry.focus()

    def refresh_statistics(self):
        """Refresh dashboard statistics"""
        # Get bungalows statistics
        self.cursor.execute("SELECT COUNT(*) FROM bungalows")
        total_bungalows = self.cursor.fetchone()[0]

        self.cursor.execute("SELECT COUNT(*) FROM bungalows WHERE status = 'available'")
        available_bungalows = self.cursor.fetchone()[0]

        occupied_bungalows = total_bungalows - available_bungalows

        # Get personnel count
        self.cursor.execute("SELECT COUNT(*) FROM personnel")
        total_personnel = self.cursor.fetchone()[0]

        # Update labels
        self.stats_labels["total_bungalows"].config(text=str(total_bungalows))
        self.stats_labels["available_bungalows"].config(text=str(available_bungalows))
        self.stats_labels["occupied_bungalows"].config(text=str(occupied_bungalows))
        self.stats_labels["total_personnel"].config(text=str(total_personnel))

    # Bungalows management methods
    def add_bungalow(self):
        """Add new bungalow"""
        dialog = BungalowDialog(self.root, "إضافة بنغالو جديد")
        if dialog.result:
            try:
                self.cursor.execute(
                    "INSERT INTO bungalows (number, location, capacity, status) VALUES (?, ?, ?, ?)",
                    (dialog.result['number'], dialog.result['location'],
                     dialog.result['capacity'], 'available')
                )
                self.conn.commit()
                self.refresh_bungalows()
                self.refresh_statistics()
                messagebox.showinfo("نجح", "تم إضافة البنغالو بنجاح")
            except sqlite3.IntegrityError:
                messagebox.showerror("خطأ", "رقم البنغالو موجود مسبقاً")

    def edit_bungalow(self):
        """Edit selected bungalow"""
        selection = self.bungalows_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار بنغالو للتعديل")
            return

        item = self.bungalows_tree.item(selection[0])
        bungalow_id = item['values'][0]

        # Get current data
        self.cursor.execute("SELECT * FROM bungalows WHERE id = ?", (bungalow_id,))
        current_data = self.cursor.fetchone()

        dialog = BungalowDialog(self.root, "تعديل البنغالو", current_data)
        if dialog.result:
            try:
                self.cursor.execute(
                    "UPDATE bungalows SET number = ?, location = ?, capacity = ? WHERE id = ?",
                    (dialog.result['number'], dialog.result['location'],
                     dialog.result['capacity'], bungalow_id)
                )
                self.conn.commit()
                self.refresh_bungalows()
                messagebox.showinfo("نجح", "تم تعديل البنغالو بنجاح")
            except sqlite3.IntegrityError:
                messagebox.showerror("خطأ", "رقم البنغالو موجود مسبقاً")

    def delete_bungalow(self):
        """Delete selected bungalow"""
        selection = self.bungalows_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار بنغالو للحذف")
            return

        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا البنغالو؟"):
            item = self.bungalows_tree.item(selection[0])
            bungalow_id = item['values'][0]

            self.cursor.execute("DELETE FROM bungalows WHERE id = ?", (bungalow_id,))
            self.conn.commit()
            self.refresh_bungalows()
            self.refresh_statistics()
            messagebox.showinfo("نجح", "تم حذف البنغالو بنجاح")

    def refresh_bungalows(self):
        """Refresh bungalows list"""
        for item in self.bungalows_tree.get_children():
            self.bungalows_tree.delete(item)

        self.cursor.execute("SELECT * FROM bungalows")
        for row in self.cursor.fetchall():
            status_text = "متاح" if row[4] == "available" else "مشغول"
            self.bungalows_tree.insert("", tk.END, values=(row[0], row[1], row[2], row[3], status_text))

    # Personnel management methods
    def add_personnel(self):
        """Add new personnel"""
        dialog = PersonnelDialog(self.root, "إضافة فرد جديد")
        if dialog.result:
            try:
                self.cursor.execute(
                    "INSERT INTO personnel (name, rank, unit, service_number) VALUES (?, ?, ?, ?)",
                    (dialog.result['name'], dialog.result['rank'],
                     dialog.result['unit'], dialog.result['service_number'])
                )
                self.conn.commit()
                self.refresh_personnel()
                self.refresh_statistics()
                messagebox.showinfo("نجح", "تم إضافة الفرد بنجاح")
            except sqlite3.IntegrityError:
                messagebox.showerror("خطأ", "الرقم العسكري موجود مسبقاً")

    def edit_personnel(self):
        """Edit selected personnel"""
        selection = self.personnel_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فرد للتعديل")
            return

        item = self.personnel_tree.item(selection[0])
        personnel_id = item['values'][0]

        # Get current data
        self.cursor.execute("SELECT * FROM personnel WHERE id = ?", (personnel_id,))
        current_data = self.cursor.fetchone()

        dialog = PersonnelDialog(self.root, "تعديل بيانات الفرد", current_data)
        if dialog.result:
            try:
                self.cursor.execute(
                    "UPDATE personnel SET name = ?, rank = ?, unit = ?, service_number = ? WHERE id = ?",
                    (dialog.result['name'], dialog.result['rank'],
                     dialog.result['unit'], dialog.result['service_number'], personnel_id)
                )
                self.conn.commit()
                self.refresh_personnel()
                messagebox.showinfo("نجح", "تم تعديل بيانات الفرد بنجاح")
            except sqlite3.IntegrityError:
                messagebox.showerror("خطأ", "الرقم العسكري موجود مسبقاً")

    def delete_personnel(self):
        """Delete selected personnel"""
        selection = self.personnel_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فرد للحذف")
            return

        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا الفرد؟"):
            item = self.personnel_tree.item(selection[0])
            personnel_id = item['values'][0]

            self.cursor.execute("DELETE FROM personnel WHERE id = ?", (personnel_id,))
            self.conn.commit()
            self.refresh_personnel()
            self.refresh_statistics()
            messagebox.showinfo("نجح", "تم حذف الفرد بنجاح")

    def refresh_personnel(self):
        """Refresh personnel list"""
        for item in self.personnel_tree.get_children():
            self.personnel_tree.delete(item)

        self.cursor.execute("SELECT * FROM personnel")
        for row in self.cursor.fetchall():
            self.personnel_tree.insert("", tk.END, values=row)

    # Allocations management methods
    def add_allocation(self):
        """Add new allocation"""
        dialog = AllocationDialog(self.root, self.cursor, "توزيع جديد")
        if dialog.result:
            try:
                # Update bungalow status
                self.cursor.execute(
                    "UPDATE bungalows SET status = 'occupied' WHERE id = ?",
                    (dialog.result['bungalow_id'],)
                )

                # Add allocation
                self.cursor.execute(
                    "INSERT INTO allocations (personnel_id, bungalow_id, start_date, status) VALUES (?, ?, ?, ?)",
                    (dialog.result['personnel_id'], dialog.result['bungalow_id'],
                     datetime.now().strftime("%Y-%m-%d"), 'active')
                )

                self.conn.commit()
                self.refresh_allocations()
                self.refresh_bungalows()
                self.refresh_statistics()
                messagebox.showinfo("نجح", "تم التوزيع بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def end_allocation(self):
        """End selected allocation"""
        selection = self.allocations_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار توزيع لإنهائه")
            return

        if messagebox.askyesno("تأكيد", "هل أنت متأكد من إنهاء هذا التوزيع؟"):
            item = self.allocations_tree.item(selection[0])
            allocation_id = item['values'][0]

            # Get allocation details
            self.cursor.execute(
                "SELECT bungalow_id FROM allocations WHERE id = ?",
                (allocation_id,)
            )
            bungalow_id = self.cursor.fetchone()[0]

            # Update allocation
            self.cursor.execute(
                "UPDATE allocations SET status = 'ended', end_date = ? WHERE id = ?",
                (datetime.now().strftime("%Y-%m-%d"), allocation_id)
            )

            # Update bungalow status
            self.cursor.execute(
                "UPDATE bungalows SET status = 'available' WHERE id = ?",
                (bungalow_id,)
            )

            self.conn.commit()
            self.refresh_allocations()
            self.refresh_bungalows()
            self.refresh_statistics()
            messagebox.showinfo("نجح", "تم إنهاء التوزيع بنجاح")

    def refresh_allocations(self):
        """Refresh allocations list"""
        for item in self.allocations_tree.get_children():
            self.allocations_tree.delete(item)

        self.cursor.execute("""
            SELECT a.id, p.name, b.number, a.start_date, a.end_date, a.status
            FROM allocations a
            JOIN personnel p ON a.personnel_id = p.id
            JOIN bungalows b ON a.bungalow_id = b.id
            ORDER BY a.id DESC
        """)

        for row in self.cursor.fetchall():
            status_text = "نشط" if row[5] == "active" else "منتهي"
            end_date = row[4] if row[4] else "غير محدد"
            self.allocations_tree.insert("", tk.END, values=(row[0], row[1], row[2], row[3], end_date, status_text))

    # Reports methods
    def show_available_bungalows_report(self):
        """Show available bungalows report"""
        self.cursor.execute("SELECT number, location, capacity FROM bungalows WHERE status = 'available'")
        data = self.cursor.fetchall()

        report_text = "تقرير البنغالوهات المتاحة\n" + "="*50 + "\n\n"
        if data:
            for row in data:
                report_text += f"رقم البنغالو: {row[0]}\nالموقع: {row[1]}\nالسعة: {row[2]}\n{'-'*30}\n"
        else:
            report_text += "لا توجد بنغالوهات متاحة حالياً"

        self.show_report_window("تقرير البنغالوهات المتاحة", report_text)

    def show_occupied_bungalows_report(self):
        """Show occupied bungalows report"""
        self.cursor.execute("""
            SELECT b.number, b.location, p.name, p.rank
            FROM bungalows b
            JOIN allocations a ON b.id = a.bungalow_id
            JOIN personnel p ON a.personnel_id = p.id
            WHERE b.status = 'occupied' AND a.status = 'active'
        """)
        data = self.cursor.fetchall()

        report_text = "تقرير البنغالوهات المشغولة\n" + "="*50 + "\n\n"
        if data:
            for row in data:
                report_text += f"رقم البنغالو: {row[0]}\nالموقع: {row[1]}\nاسم الساكن: {row[2]}\nالرتبة: {row[3]}\n{'-'*30}\n"
        else:
            report_text += "لا توجد بنغالوهات مشغولة حالياً"

        self.show_report_window("تقرير البنغالوهات المشغولة", report_text)

    def show_personnel_report(self):
        """Show personnel report"""
        self.cursor.execute("SELECT name, rank, unit, service_number FROM personnel")
        data = self.cursor.fetchall()

        report_text = "تقرير الأفراد\n" + "="*50 + "\n\n"
        if data:
            for row in data:
                report_text += f"الاسم: {row[0]}\nالرتبة: {row[1]}\nالوحدة: {row[2]}\nالرقم العسكري: {row[3]}\n{'-'*30}\n"
        else:
            report_text += "لا يوجد أفراد مسجلين"

        self.show_report_window("تقرير الأفراد", report_text)

    def show_general_statistics_report(self):
        """Show general statistics report"""
        # Get statistics
        self.cursor.execute("SELECT COUNT(*) FROM bungalows")
        total_bungalows = self.cursor.fetchone()[0]

        self.cursor.execute("SELECT COUNT(*) FROM bungalows WHERE status = 'available'")
        available_bungalows = self.cursor.fetchone()[0]

        self.cursor.execute("SELECT COUNT(*) FROM personnel")
        total_personnel = self.cursor.fetchone()[0]

        self.cursor.execute("SELECT COUNT(*) FROM allocations WHERE status = 'active'")
        active_allocations = self.cursor.fetchone()[0]

        occupied_bungalows = total_bungalows - available_bungalows

        report_text = f"""تقرير الإحصائيات العامة
{'='*50}

📊 إحصائيات البنغالوهات:
   • إجمالي البنغالوهات: {total_bungalows}
   • البنغالوهات المتاحة: {available_bungalows}
   • البنغالوهات المشغولة: {occupied_bungalows}

👥 إحصائيات الأفراد:
   • إجمالي الأفراد: {total_personnel}
   • التوزيعات النشطة: {active_allocations}

📈 معدل الإشغال:
   • نسبة الإشغال: {(occupied_bungalows/total_bungalows*100):.1f}% إذا كان هناك بنغالوهات
   • نسبة التوفر: {(available_bungalows/total_bungalows*100):.1f}% إذا كان هناك بنغالوهات

تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""

        self.show_report_window("تقرير الإحصائيات العامة", report_text)

    def show_report_window(self, title, content):
        """Show report in a new window"""
        report_window = tk.Toplevel(self.root)
        report_window.title(title)
        report_window.geometry("600x500")

        # Text widget with scrollbar
        text_frame = tk.Frame(report_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Arial", 11))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)

        # Close button
        tk.Button(
            report_window,
            text="إغلاق",
            command=report_window.destroy,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 12),
            padx=20,
            pady=5
        ).pack(pady=10)

    def run(self):
        """Run the application"""
        self.root.mainloop()
        self.conn.close()


# Dialog classes
class BungalowDialog:
    def __init__(self, parent, title, data=None):
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.geometry("+{}+{}".format(
            int(parent.winfo_rootx() + 50),
            int(parent.winfo_rooty() + 50)
        ))

        # Form fields
        tk.Label(self.dialog, text="رقم البنغالو:", font=("Arial", 12)).pack(pady=5)
        self.number_entry = tk.Entry(self.dialog, font=("Arial", 12))
        self.number_entry.pack(pady=5, padx=20, fill=tk.X)

        tk.Label(self.dialog, text="الموقع:", font=("Arial", 12)).pack(pady=5)
        self.location_entry = tk.Entry(self.dialog, font=("Arial", 12))
        self.location_entry.pack(pady=5, padx=20, fill=tk.X)

        tk.Label(self.dialog, text="السعة:", font=("Arial", 12)).pack(pady=5)
        self.capacity_entry = tk.Entry(self.dialog, font=("Arial", 12))
        self.capacity_entry.pack(pady=5, padx=20, fill=tk.X)

        # Pre-fill if editing
        if data:
            self.number_entry.insert(0, data[1])
            self.location_entry.insert(0, data[2])
            self.capacity_entry.insert(0, str(data[3]))

        # Buttons
        btn_frame = tk.Frame(self.dialog)
        btn_frame.pack(pady=20)

        tk.Button(
            btn_frame, text="حفظ", command=self.save,
            bg='#27ae60', fg='white', font=("Arial", 12), padx=20
        ).pack(side=tk.LEFT, padx=10)

        tk.Button(
            btn_frame, text="إلغاء", command=self.cancel,
            bg='#e74c3c', fg='white', font=("Arial", 12), padx=20
        ).pack(side=tk.LEFT, padx=10)

        self.number_entry.focus()

    def save(self):
        number = self.number_entry.get().strip()
        location = self.location_entry.get().strip()
        capacity = self.capacity_entry.get().strip()

        if not all([number, location, capacity]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
            return

        try:
            capacity = int(capacity)
            if capacity <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح للسعة")
            return

        self.result = {
            'number': number,
            'location': location,
            'capacity': capacity
        }
        self.dialog.destroy()

    def cancel(self):
        self.dialog.destroy()


class PersonnelDialog:
    def __init__(self, parent, title, data=None):
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("4003")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.geometry("+{}+{}".format(
            int(parent.winfo_rootx() + 50),
            int(parent.winfo_rooty() + 50)
        ))

        # Form fields
        tk.Label(self.dialog, text="الاسم:", font=("Arial", 12)).pack(pady=5)
        self.name_entry = tk.Entry(self.dialog, font=("Arial", 12))
        self.name_entry.pack(pady=5, padx=20, fill=tk.X)

        tk.Label(self.dialog, text="الرتبة:", font=("Arial", 12)).pack(pady=5)
        self.rank_entry = tk.Entry(self.dialog, font=("Arial", 12))
        self.rank_entry.pack(pady=5, padx=20, fill=tk.X)

        tk.Label(self.dialog, text="الوحدة:", font=("Arial", 12)).pack(pady=5)
        self.unit_entry = tk.Entry(self.dialog, font=("Arial", 12))
        self.unit_entry.pack(pady=5, padx=20, fill=tk.X)

        tk.Label(self.dialog, text="الرقم العسكري:", font=("Arial", 12)).pack(pady=5)
        self.service_number_entry = tk.Entry(self.dialog, font=("Arial", 12))
        self.service_number_entry.pack(pady=5, padx=20, fill=tk.X)

        # Pre-fill if editing
        if data:
            self.name_entry.insert(0, data[1])
            self.rank_entry.insert(0, data[2])
            self.unit_entry.insert(0, data[3])
            self.service_number_entry.insert(0, data[4])

        # Buttons
        btn_frame = tk.Frame(self.dialog)
        btn_frame.pack(pady=20)

        tk.Button(
            btn_frame, text="حفظ", command=self.save,
            bg='#27ae60', fg='white', font=("Arial", 12), padx=20
        ).pack(side=tk.LEFT, padx=10)

        tk.Button(
            btn_frame, text="إلغاء", command=self.cancel,
            bg='#e74c3c', fg='white', font=("Arial", 12), padx=20
        ).pack(side=tk.LEFT, padx=10)

        self.name_entry.focus()

    def save(self):
        name = self.name_entry.get().strip()
        rank = self.rank_entry.get().strip()
        unit = self.unit_entry.get().strip()
        service_number = self.service_number_entry.get().strip()

        if not all([name, rank, unit, service_number]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
            return

        self.result = {
            'name': name,
            'rank': rank,
            'unit': unit,
            'service_number': service_number
        }
        self.dialog.destroy()

    def cancel(self):
        self.dialog.destroy()


class AllocationDialog:
    def __init__(self, parent, cursor, title):
        self.result = None
        self.cursor = cursor

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.geometry("+{}+{}".format(
            int(parent.winfo_rootx() + 50),
            int(parent.winfo_rooty() + 50)
        ))

        # Personnel selection
        tk.Label(self.dialog, text="اختر الفرد:", font=("Arial", 12)).pack(pady=5)
        self.personnel_var = tk.StringVar()
        self.personnel_combo = ttk.Combobox(self.dialog, textvariable=self.personnel_var, state="readonly")
        self.personnel_combo.pack(pady=5, padx=20, fill=tk.X)

        # Load personnel
        self.cursor.execute("SELECT id, name, rank FROM personnel")
        personnel_data = self.cursor.fetchall()
        personnel_options = [f"{row[0]} - {row[1]} ({row[2]})" for row in personnel_data]
        self.personnel_combo['values'] = personnel_options

        # Bungalow selection
        tk.Label(self.dialog, text="اختر البنغالو:", font=("Arial", 12)).pack(pady=5)
        self.bungalow_var = tk.StringVar()
        self.bungalow_combo = ttk.Combobox(self.dialog, textvariable=self.bungalow_var, state="readonly")
        self.bungalow_combo.pack(pady=5, padx=20, fill=tk.X)

        # Load available bungalows
        self.cursor.execute("SELECT id, number, location FROM bungalows WHERE status = 'available'")
        bungalow_data = self.cursor.fetchall()
        bungalow_options = [f"{row[0]} - {row[1]} ({row[2]})" for row in bungalow_data]
        self.bungalow_combo['values'] = bungalow_options

        # Buttons
        btn_frame = tk.Frame(self.dialog)
        btn_frame.pack(pady=20)

        tk.Button(
            btn_frame, text="توزيع", command=self.save,
            bg='#27ae60', fg='white', font=("Arial", 12), padx=20
        ).pack(side=tk.LEFT, padx=10)

        tk.Button(
            btn_frame, text="إلغاء", command=self.cancel,
            bg='#e74c3c', fg='white', font=("Arial", 12), padx=20
        ).pack(side=tk.LEFT, padx=10)

    def save(self):
        personnel_selection = self.personnel_var.get()
        bungalow_selection = self.bungalow_var.get()

        if not personnel_selection or not bungalow_selection:
            messagebox.showerror("خطأ", "يرجى اختيار الفرد والبنغالو")
            return

        personnel_id = int(personnel_selection.split(' - ')[0])
        bungalow_id = int(bungalow_selection.split(' - ')[0])

        self.result = {
            'personnel_id': personnel_id,
            'bungalow_id': bungalow_id
        }
        self.dialog.destroy()

    def cancel(self):
        self.dialog.destroy()


if __name__ == "__main__":
    app = BanghalauDesktop()
    app.run()
