#!/usr/bin/env python3
"""
BANGHALAU Server - Enhanced startup script
"""

import os
import sys
import time
import webbrowser
import threading

def print_banner():
    print("=" * 60)
    print("🏠 BANGHALAU - نظام إدارة توزيع البنغالوهات")
    print("=" * 60)
    print("🚀 بدء تشغيل الخادم...")
    print()

def check_requirements():
    """Check if required packages are available"""
    try:
        import flask
        print("✅ Flask متوفر - الإصدار:", flask.__version__)
        return True
    except ImportError:
        print("❌ Flask غير متوفر")
        return False

def open_browser_delayed():
    """Open browser after a delay"""
    time.sleep(3)
    print("🌐 فتح المتصفح...")
    webbrowser.open('http://localhost:5000')

def main():
    print_banner()
    
    # Check requirements
    if not check_requirements():
        print("⚠️ يرجى تثبيت المتطلبات أولاً")
        return
    
    try:
        # Import the Flask app
        print("📦 تحميل التطبيق...")
        from app import app
        print("✅ تم تحميل التطبيق بنجاح")
        
        # Setup database
        print("🗄️ إعداد قاعدة البيانات...")
        try:
            from database.db_manager import DatabaseManager
            from database.operations import DatabaseOperations
            
            db_manager = DatabaseManager()
            db_manager.connect()
            db_manager.initialize_database()
            
            # Check for admin user
            db_ops = DatabaseOperations(db_manager)
            users = db_ops.list_users()
            
            if users:
                print("✅ قاعدة البيانات جاهزة مع المستخدمين الموجودين")
            else:
                print("⚠️ لم يتم العثور على مستخدمين")
                
            db_manager.close()
            
        except Exception as e:
            print(f"⚠️ تحذير في قاعدة البيانات: {e}")
        
        # Print access information
        print()
        print("🌐 معلومات الوصول:")
        print("   📍 الرابط: http://localhost:5000")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        print()
        print("🔄 بدء الخادم...")
        print("   (اضغط Ctrl+C لإيقاف الخادم)")
        print("=" * 60)
        
        # Start browser in background
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start the Flask server
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
