#!/usr/bin/env python3
"""
Script pour vérifier les données existantes
"""

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def check_existing_data():
    """Vérifier les données existantes"""
    
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    print('الرتب الموجودة:')
    grades = db_ops.list_grades()
    for grade in grades:
        print(f'ID: {grade["id"]}, Grade: {grade["grade"]}')

    print('\nالوحدات الموجودة:')
    unites = db_ops.list_unites()
    for unite in unites:
        print(f'ID: {unite["id"]}, Description: {unite["description"]}')

    print('\nالأفراد الموجودون:')
    personnel = db_ops.list_personnel_with_grades_and_unites()
    for person in personnel:
        print(f'ID: {person["id"]}, Nom: {person["nom"]} {person["prenom"]}, Grade: {person.get("grade_name", "N/A")}, Unite: {person.get("unite_description", "N/A")}')

    db_manager.close()

if __name__ == "__main__":
    check_existing_data()
