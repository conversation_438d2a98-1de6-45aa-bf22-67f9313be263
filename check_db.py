#!/usr/bin/env python3
"""
Script pour vérifier les unités dans la base de données
"""

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def check_database():
    """Vérifier les unités dans la base de données"""
    
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)
    
    unites = db_ops.list_unites()
    print(f'📊 عدد الوحدات في قاعدة البيانات: {len(unites)}')
    
    if unites:
        print('\n📋 الوحدات الموجودة:')
        for i, unite in enumerate(unites, 1):
            print(f'  {i}. {unite["numero"]}: {unite["description"]} ({unite.get("raccourci", "N/A")})')
    else:
        print('📭 لا توجد وحدات في قاعدة البيانات')
    
    db_manager.close()

if __name__ == "__main__":
    check_database()
