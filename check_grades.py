#!/usr/bin/env python3
"""
Script pour vérifier les grades spécifiques
"""

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def check_specific_grades():
    """Vérifier les grades spécifiques"""
    
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # البحث عن الرتب المحددة
    grades_to_find = ['عقيد', 'رائد', 'نقيب']
    for grade_name in grades_to_find:
        grade = db_ops.get_grade_by_name(grade_name)
        if grade:
            print(f'✅ الرتبة {grade_name} موجودة: ID {grade["id"]}')
        else:
            print(f'❌ الرتبة {grade_name} غير موجودة')
            # البحث بالمسافات
            grade_with_space = db_ops.get_grade_by_name(grade_name + ' ')
            if grade_with_space:
                print(f'   ✅ وجدت بمسافة: ID {grade_with_space["id"]}')
            else:
                # البحث في جميع الرتب للعثور على تطابق جزئي
                all_grades = db_ops.list_grades()
                for g in all_grades:
                    if grade_name in g['grade'] or g['grade'] in grade_name:
                        print(f'   🔍 تطابق جزئي: "{g["grade"]}" (ID: {g["id"]})')

    db_manager.close()

if __name__ == "__main__":
    check_specific_grades()
