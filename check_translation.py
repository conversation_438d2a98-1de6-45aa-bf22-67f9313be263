#!/usr/bin/env python3
"""
Script to check if the French translation is applied correctly
"""

import os

def check_users_management_translation():
    """Check if users management page is translated to French"""
    print("🔍 Checking Users Management Translation...")
    
    file_path = "templates/users_management.html"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for French translations
    french_terms = [
        "Gestion des Utilisateurs",
        "Ajouter un utilisateur",
        "Modifier l'utilisateur",
        "Réinitialiser le mot de passe",
        "Nom d'utilisateur",
        "Utilisateur actif",
        "Administrateur",
        "Enregistrer les modifications",
        "Annuler"
    ]
    
    # Check for Arabic terms that should be translated
    arabic_terms = [
        "إدارة المستخدمين",
        "تعديل المستخدم",
        "إعادة تعيين كلمة المرور",
        "اسم المستخدم",
        "المستخدم نشط",
        "مدير النظام",
        "حفظ التغييرات",
        "إلغاء"
    ]
    
    print("\n✅ French terms found:")
    french_found = 0
    for term in french_terms:
        if term in content:
            print(f"  ✓ {term}")
            french_found += 1
        else:
            print(f"  ✗ {term} - NOT FOUND")
    
    print(f"\n❌ Arabic terms still present:")
    arabic_found = 0
    for term in arabic_terms:
        if term in content:
            print(f"  ✗ {term} - STILL PRESENT")
            arabic_found += 1
        else:
            print(f"  ✓ {term} - REMOVED")
    
    print(f"\n📊 Translation Status:")
    print(f"  French terms found: {french_found}/{len(french_terms)}")
    print(f"  Arabic terms remaining: {arabic_found}/{len(arabic_terms)}")
    
    if french_found >= len(french_terms) * 0.8 and arabic_found == 0:
        print("✅ Translation appears to be successful!")
        return True
    else:
        print("❌ Translation incomplete or has issues")
        return False

def check_security_dashboard_translation():
    """Check if security dashboard is translated to French"""
    print("\n🔍 Checking Security Dashboard Translation...")
    
    file_path = "templates/security_dashboard.html"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for French translations
    french_terms = [
        "Gestion de la Sécurité",
        "Surveillance Active",
        "Chiffrement des Sauvegardes",
        "Adresses IP Bloquées",
        "Créer une Sauvegarde",
        "Débloquer"
    ]
    
    print("✅ French terms found:")
    french_found = 0
    for term in french_terms:
        if term in content:
            print(f"  ✓ {term}")
            french_found += 1
        else:
            print(f"  ✗ {term} - NOT FOUND")
    
    print(f"\n📊 Security Translation Status:")
    print(f"  French terms found: {french_found}/{len(french_terms)}")
    
    if french_found >= len(french_terms) * 0.8:
        print("✅ Security translation appears to be successful!")
        return True
    else:
        print("❌ Security translation incomplete")
        return False

def check_base_template():
    """Check if base template navigation is translated"""
    print("\n🔍 Checking Base Template Navigation...")
    
    file_path = "templates/base.html"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for French navigation terms
    french_nav_terms = [
        "Gestion Utilisateurs",
        "Gestion de la Sécurité"
    ]
    
    print("✅ French navigation terms found:")
    nav_found = 0
    for term in french_nav_terms:
        if term in content:
            print(f"  ✓ {term}")
            nav_found += 1
        else:
            print(f"  ✗ {term} - NOT FOUND")
    
    print(f"\n📊 Navigation Translation Status:")
    print(f"  French nav terms found: {nav_found}/{len(french_nav_terms)}")
    
    if nav_found >= len(french_nav_terms) * 0.8:
        print("✅ Navigation translation appears to be successful!")
        return True
    else:
        print("❌ Navigation translation incomplete")
        return False

def main():
    """Main function to run all checks"""
    print("🇫🇷 BANGHALAU French Translation Checker")
    print("=" * 50)
    
    results = []
    
    # Check users management translation
    results.append(check_users_management_translation())
    
    # Check security dashboard translation
    results.append(check_security_dashboard_translation())
    
    # Check base template navigation
    results.append(check_base_template())
    
    print("\n" + "=" * 50)
    print("🎯 Final Results:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"  Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All translations are working correctly!")
        print("✅ The French interface should be visible in the browser.")
        print("\n💡 If you're still seeing Arabic text:")
        print("  1. Clear your browser cache (Ctrl+F5)")
        print("  2. Try opening in incognito/private mode")
        print("  3. Restart the Flask application")
    else:
        print("❌ Some translations are missing or incomplete.")
        print("🔧 Please check the files and apply missing translations.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 Translation check completed successfully!")
    else:
        print("\n⚠️ Translation check found issues!")
