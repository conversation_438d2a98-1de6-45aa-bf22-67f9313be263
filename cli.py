#!/usr/bin/env python
"""
Command-line interface for BANGHALAU database
"""

import argparse
import sys
import json
from database import DatabaseManager, DatabaseOperations
from models import User, Item, Category, PersonnelMilitaire


def init_db(args):
    """Initialize the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_manager.initialize_database()
    db_manager.close()
    print("Database initialized successfully.")


def add_user(args):
    """Add a user to the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    user_id = db_ops.create_user(args.username, args.email)
    if user_id:
        print(f"User created with ID: {user_id}")
    else:
        print("Failed to create user.")

    db_manager.close()


def get_user(args):
    """Get a user from the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if args.username:
        user = db_ops.get_user_by_username(args.username)
    else:
        user = db_ops.get_user(args.id)

    if user:
        print(json.dumps(user, indent=2, default=str))
    else:
        print("User not found.")

    db_manager.close()


def list_users(args):
    """List all users in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    users = db_ops.list_users()
    if users:
        print(json.dumps(users, indent=2, default=str))
    else:
        print("No users found.")

    db_manager.close()


def add_item(args):
    """Add an item to the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    item_id = db_ops.create_item(
        args.name,
        description=args.description,
        price=args.price,
        user_id=args.user_id
    )

    if item_id:
        print(f"Item created with ID: {item_id}")
    else:
        print("Failed to create item.")

    db_manager.close()


def get_item(args):
    """Get an item from the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    item = db_ops.get_item(args.id)
    if item:
        print(json.dumps(item, indent=2, default=str))
    else:
        print("Item not found.")

    db_manager.close()


def list_items(args):
    """List all items in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    items = db_ops.list_items(user_id=args.user_id)
    if items:
        print(json.dumps(items, indent=2, default=str))
    else:
        print("No items found.")

    db_manager.close()


def add_personnel(args):
    """Add a military personnel to the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Convert grade_id to integer if provided
    grade_id = int(args.grade_id) if args.grade_id else None

    personnel_id = db_ops.create_personnel_militaire(
        args.matricule,
        args.nom,
        args.prenom,
        grade_id=grade_id,
        unite=args.unite,
        numero=args.numero
    )

    if personnel_id:
        print(f"Personnel created with ID: {personnel_id}")
    else:
        print("Failed to create personnel.")

    db_manager.close()


def get_personnel(args):
    """Get a military personnel from the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Determine if we should include grade information
    include_grade = args.include_grade if hasattr(args, 'include_grade') else False

    if args.matricule:
        if include_grade:
            personnel = db_ops.get_personnel_with_grade_by_matricule(args.matricule)
        else:
            personnel = db_ops.get_personnel_by_matricule(args.matricule)
    else:
        if include_grade:
            personnel = db_ops.get_personnel_with_grade(args.id)
        else:
            personnel = db_ops.get_personnel_militaire(args.id)

    if personnel:
        print(json.dumps(personnel, indent=2, default=str))
    else:
        print("Personnel not found.")

    db_manager.close()


def list_personnel(args):
    """List all military personnel in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Determine if we should include grade information
    include_grade = args.include_grade if hasattr(args, 'include_grade') else False

    # Check if we should filter by grade
    grade_id = args.grade_id if hasattr(args, 'grade_id') else None

    if grade_id:
        # Convert grade_id to integer
        grade_id = int(grade_id)
        personnel_list = db_ops.get_personnel_by_grade(grade_id)
    elif include_grade:
        personnel_list = db_ops.list_personnel_with_grades()
    else:
        personnel_list = db_ops.list_personnel_militaire()

    if personnel_list:
        print(json.dumps(personnel_list, indent=2, default=str))
    else:
        print("No personnel found.")

    db_manager.close()


def add_grade(args):
    """Add a grade to the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    grade_id = db_ops.create_grade(
        args.numero,
        args.grade,
        description=args.description
    )

    if grade_id:
        print(f"Grade created with ID: {grade_id}")
    else:
        print("Failed to create grade.")

    db_manager.close()


def get_grade(args):
    """Get a grade from the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if args.numero:
        grade = db_ops.get_grade_by_numero(args.numero)
    elif args.grade_name:
        grade = db_ops.get_grade_by_name(args.grade_name)
    else:
        grade = db_ops.get_grade(args.id)

    if grade:
        print(json.dumps(grade, indent=2, default=str))
    else:
        print("Grade not found.")

    db_manager.close()


def list_grades(args):
    """List all grades in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Determine if we should include personnel count
    include_count = args.include_count if hasattr(args, 'include_count') else False

    if include_count:
        grades = db_ops.list_grades_with_personnel_count()
    else:
        grades = db_ops.list_grades()

    if grades:
        print(json.dumps(grades, indent=2, default=str))
    else:
        print("No grades found.")

    db_manager.close()


def add_unite(args):
    """Add a unit to the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    unite_id = db_ops.create_unite(
        args.numero,
        args.description,
        raccourci=args.raccourci
    )

    if unite_id:
        print(f"Unit created with ID: {unite_id}")
    else:
        print("Failed to create unit.")

    db_manager.close()


def get_unite(args):
    """Get a unit from the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if args.numero:
        unite = db_ops.get_unite_by_numero(args.numero)
    elif args.raccourci:
        unite = db_ops.get_unite_by_raccourci(args.raccourci)
    else:
        unite = db_ops.get_unite(args.id)

    if unite:
        print(json.dumps(unite, indent=2, default=str))
    else:
        print("Unit not found.")

    db_manager.close()


def list_unites(args):
    """List all units in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    unites = db_ops.list_unites()
    if unites:
        print(json.dumps(unites, indent=2, default=str))
    else:
        print("No units found.")

    db_manager.close()


def add_bungalow(args):
    """Add a bungalow to the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    capacite = int(args.capacite) if args.capacite else None

    bungalow_id = db_ops.create_bungalow(
        args.numero,
        args.endroit,
        capacite=capacite,
        caracteristiques=args.caracteristiques
    )

    if bungalow_id:
        print(f"Bungalow created with ID: {bungalow_id}")
    else:
        print("Failed to create bungalow.")

    db_manager.close()


def get_bungalow(args):
    """Get a bungalow from the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if args.numero:
        bungalow = db_ops.get_bungalow_by_numero(args.numero)
    else:
        bungalow = db_ops.get_bungalow(args.id)

    if bungalow:
        print(json.dumps(bungalow, indent=2, default=str))
    else:
        print("Bungalow not found.")

    db_manager.close()


def list_bungalows(args):
    """List all bungalows in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Check if we should show occupancy status
    show_status = args.show_status if hasattr(args, 'show_status') else False
    date = args.date if hasattr(args, 'date') else None

    # Check if we should filter by session
    session_id = args.session_id if hasattr(args, 'session_id') else None

    # Check if we should show available bungalows
    show_available = args.show_available if hasattr(args, 'show_available') else False
    start_date = args.start_date if hasattr(args, 'start_date') else None
    end_date = args.end_date if hasattr(args, 'end_date') else None

    if session_id:
        # Convert session_id to integer
        session_id = int(session_id)
        bungalows = db_ops.get_bungalows_by_session(session_id)
    elif show_status:
        bungalows = db_ops.get_bungalows_with_occupancy_status(date)
    elif show_available and start_date and end_date:
        bungalows = db_ops.get_available_bungalows(start_date, end_date)
    else:
        bungalows = db_ops.list_bungalows()

    if bungalows:
        print(json.dumps(bungalows, indent=2, default=str))
    else:
        print("No bungalows found.")

    db_manager.close()


def add_session(args):
    """Add a session to the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    session_id = db_ops.create_session(
        args.numero,
        args.date_debut,
        description=args.description,
        date_fin=args.date_fin,
        etat_session=args.etat_session
    )

    if session_id:
        print(f"Session created with ID: {session_id}")
    else:
        print("Failed to create session.")

    db_manager.close()


def get_session(args):
    """Get a session from the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if args.numero:
        session = db_ops.get_session_by_numero(args.numero)
    else:
        session = db_ops.get_session(args.id)

    if session:
        print(json.dumps(session, indent=2, default=str))
    else:
        print("Session not found.")

    db_manager.close()


def list_sessions(args):
    """List all sessions in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    sessions = db_ops.list_sessions()
    if sessions:
        print(json.dumps(sessions, indent=2, default=str))
    else:
        print("No sessions found.")

    db_manager.close()


def add_distribution_bungalow(args):
    """Add a bungalow distribution to the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Convert IDs to integers if provided
    bungalow_id = int(args.bungalow_id) if args.bungalow_id else None
    personnel_id = int(args.personnel_id) if args.personnel_id else None
    session_id = int(args.session_id) if args.session_id else None

    distribution_id = db_ops.create_distribution_bungalow(
        args.numero,
        bungalow_id=bungalow_id,
        personnel_id=personnel_id,
        session_id=session_id,
        date_debut=args.date_debut,
        date_fin=args.date_fin,
        notes=args.notes
    )

    if distribution_id:
        print(f"Distribution created with ID: {distribution_id}")
    else:
        print("Failed to create distribution.")

    db_manager.close()


def get_distribution_bungalow(args):
    """Get a bungalow distribution from the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Determine if we should include details
    include_details = args.include_details if hasattr(args, 'include_details') else False

    if args.numero:
        distribution = db_ops.get_distribution_bungalow_by_numero(args.numero)
        if distribution and include_details:
            distribution = db_ops.get_distribution_with_details(distribution['id'])
    else:
        if include_details:
            distribution = db_ops.get_distribution_with_details(args.id)
        else:
            distribution = db_ops.get_distribution_bungalow(args.id)

    if distribution:
        print(json.dumps(distribution, indent=2, default=str))
    else:
        print("Distribution not found.")

    db_manager.close()


def list_distribution_bungalows(args):
    """List all bungalow distributions in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    # Determine if we should include details
    include_details = args.include_details if hasattr(args, 'include_details') else False

    # Check if we should filter by bungalow, personnel, or session
    bungalow_id = args.bungalow_id if hasattr(args, 'bungalow_id') else None
    personnel_id = args.personnel_id if hasattr(args, 'personnel_id') else None
    session_id = args.session_id if hasattr(args, 'session_id') else None

    if bungalow_id:
        # Convert bungalow_id to integer
        bungalow_id = int(bungalow_id)
        distributions = db_ops.get_distribution_bungalows_by_bungalow(bungalow_id)
    elif personnel_id:
        # Convert personnel_id to integer
        personnel_id = int(personnel_id)
        distributions = db_ops.get_distribution_bungalows_by_personnel(personnel_id)
    elif session_id:
        # Convert session_id to integer
        session_id = int(session_id)
        if include_details:
            distributions = db_ops.get_distribution_bungalows_by_session_with_details(session_id)
        else:
            distributions = db_ops.get_distribution_bungalows_by_session(session_id)
    elif include_details:
        distributions = db_ops.list_distributions_with_details()
    else:
        distributions = db_ops.list_distribution_bungalows()

    if distributions:
        print(json.dumps(distributions, indent=2, default=str))
    else:
        print("No distributions found.")

    db_manager.close()


def main():
    """Main entry point for the CLI"""
    parser = argparse.ArgumentParser(description="BANGHALAU Database CLI")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # Init command
    init_parser = subparsers.add_parser("init", help="Initialize the database")
    init_parser.set_defaults(func=init_db)

    # User commands
    user_parser = subparsers.add_parser("user", help="User operations")
    user_subparsers = user_parser.add_subparsers(dest="user_command", help="User command to run")

    # Add user
    add_user_parser = user_subparsers.add_parser("add", help="Add a user")
    add_user_parser.add_argument("username", help="Username")
    add_user_parser.add_argument("email", help="Email address")
    add_user_parser.set_defaults(func=add_user)

    # Get user
    get_user_parser = user_subparsers.add_parser("get", help="Get a user")
    get_user_group = get_user_parser.add_mutually_exclusive_group(required=True)
    get_user_group.add_argument("--id", type=int, help="User ID")
    get_user_group.add_argument("--username", help="Username")
    get_user_parser.set_defaults(func=get_user)

    # List users
    list_users_parser = user_subparsers.add_parser("list", help="List all users")
    list_users_parser.set_defaults(func=list_users)

    # Item commands
    item_parser = subparsers.add_parser("item", help="Item operations")
    item_subparsers = item_parser.add_subparsers(dest="item_command", help="Item command to run")

    # Add item
    add_item_parser = item_subparsers.add_parser("add", help="Add an item")
    add_item_parser.add_argument("name", help="Item name")
    add_item_parser.add_argument("--description", help="Item description")
    add_item_parser.add_argument("--price", type=float, help="Item price")
    add_item_parser.add_argument("--user-id", type=int, help="User ID")
    add_item_parser.set_defaults(func=add_item)

    # Get item
    get_item_parser = item_subparsers.add_parser("get", help="Get an item")
    get_item_parser.add_argument("id", type=int, help="Item ID")
    get_item_parser.set_defaults(func=get_item)

    # List items
    list_items_parser = item_subparsers.add_parser("list", help="List all items")
    list_items_parser.add_argument("--user-id", type=int, help="Filter by user ID")
    list_items_parser.set_defaults(func=list_items)

    # Personnel commands
    personnel_parser = subparsers.add_parser("personnel", help="Military Personnel operations")
    personnel_subparsers = personnel_parser.add_subparsers(dest="personnel_command", help="Personnel command to run")

    # Add personnel
    add_personnel_parser = personnel_subparsers.add_parser("add", help="Add a military personnel")
    add_personnel_parser.add_argument("matricule", help="Matricule (unique identifier)")
    add_personnel_parser.add_argument("nom", help="Last name")
    add_personnel_parser.add_argument("prenom", help="First name")
    add_personnel_parser.add_argument("--grade-id", help="Military rank ID")
    add_personnel_parser.add_argument("--unite", help="Unit")
    add_personnel_parser.add_argument("--numero", help="Number")
    add_personnel_parser.set_defaults(func=add_personnel)

    # Get personnel
    get_personnel_parser = personnel_subparsers.add_parser("get", help="Get a military personnel")
    get_personnel_group = get_personnel_parser.add_mutually_exclusive_group(required=True)
    get_personnel_group.add_argument("--id", type=int, help="Personnel ID")
    get_personnel_group.add_argument("--matricule", help="Matricule")
    get_personnel_parser.add_argument("--include-grade", action="store_true", help="Include grade information")
    get_personnel_parser.set_defaults(func=get_personnel)

    # List personnel
    list_personnel_parser = personnel_subparsers.add_parser("list", help="List all military personnel")
    list_personnel_parser.add_argument("--include-grade", action="store_true", help="Include grade information")
    list_personnel_parser.add_argument("--grade-id", help="Filter by grade ID")
    list_personnel_parser.set_defaults(func=list_personnel)

    # Grade commands
    grade_parser = subparsers.add_parser("grade", help="Grade operations")
    grade_subparsers = grade_parser.add_subparsers(dest="grade_command", help="Grade command to run")

    # Add grade
    add_grade_parser = grade_subparsers.add_parser("add", help="Add a grade")
    add_grade_parser.add_argument("numero", help="Grade number")
    add_grade_parser.add_argument("grade", help="Grade name")
    add_grade_parser.add_argument("--description", help="Grade description")
    add_grade_parser.set_defaults(func=add_grade)

    # Get grade
    get_grade_parser = grade_subparsers.add_parser("get", help="Get a grade")
    get_grade_group = get_grade_parser.add_mutually_exclusive_group(required=True)
    get_grade_group.add_argument("--id", type=int, help="Grade ID")
    get_grade_group.add_argument("--numero", help="Grade number")
    get_grade_group.add_argument("--grade-name", help="Grade name")
    get_grade_parser.set_defaults(func=get_grade)

    # List grades
    list_grades_parser = grade_subparsers.add_parser("list", help="List all grades")
    list_grades_parser.add_argument("--include-count", action="store_true", help="Include personnel count for each grade")
    list_grades_parser.set_defaults(func=list_grades)

    # Unite commands
    unite_parser = subparsers.add_parser("unite", help="Unit/Position operations")
    unite_subparsers = unite_parser.add_subparsers(dest="unite_command", help="Unit command to run")

    # Add unite
    add_unite_parser = unite_subparsers.add_parser("add", help="Add a unit")
    add_unite_parser.add_argument("numero", help="Unit number")
    add_unite_parser.add_argument("description", help="Unit description")
    add_unite_parser.add_argument("--raccourci", help="Unit shortcut/abbreviation")
    add_unite_parser.set_defaults(func=add_unite)

    # Get unite
    get_unite_parser = unite_subparsers.add_parser("get", help="Get a unit")
    get_unite_group = get_unite_parser.add_mutually_exclusive_group(required=True)
    get_unite_group.add_argument("--id", type=int, help="Unit ID")
    get_unite_group.add_argument("--numero", help="Unit number")
    get_unite_group.add_argument("--raccourci", help="Unit shortcut")
    get_unite_parser.set_defaults(func=get_unite)

    # List unites
    list_unites_parser = unite_subparsers.add_parser("list", help="List all units")
    list_unites_parser.set_defaults(func=list_unites)

    # Bungalow commands
    bungalow_parser = subparsers.add_parser("bungalow", help="Bungalow operations")
    bungalow_subparsers = bungalow_parser.add_subparsers(dest="bungalow_command", help="Bungalow command to run")

    # Add bungalow
    add_bungalow_parser = bungalow_subparsers.add_parser("add", help="Add a bungalow")
    add_bungalow_parser.add_argument("numero", help="Bungalow number")
    add_bungalow_parser.add_argument("endroit", help="Bungalow location")
    add_bungalow_parser.add_argument("--capacite", help="Bungalow capacity")
    add_bungalow_parser.add_argument("--caracteristiques", help="Bungalow characteristics")
    add_bungalow_parser.set_defaults(func=add_bungalow)

    # Get bungalow
    get_bungalow_parser = bungalow_subparsers.add_parser("get", help="Get a bungalow")
    get_bungalow_group = get_bungalow_parser.add_mutually_exclusive_group(required=True)
    get_bungalow_group.add_argument("--id", type=int, help="Bungalow ID")
    get_bungalow_group.add_argument("--numero", help="Bungalow number")
    get_bungalow_parser.set_defaults(func=get_bungalow)

    # List bungalows
    list_bungalows_parser = bungalow_subparsers.add_parser("list", help="List all bungalows")
    list_bungalows_parser.add_argument("--show-status", action="store_true", help="Show occupancy status")
    list_bungalows_parser.add_argument("--date", help="Date to check occupancy status (YYYY-MM-DD)")
    list_bungalows_parser.add_argument("--session-id", help="Filter by session ID")
    list_bungalows_parser.add_argument("--show-available", action="store_true", help="Show only available bungalows")
    list_bungalows_parser.add_argument("--start-date", help="Start date for availability check (YYYY-MM-DD)")
    list_bungalows_parser.add_argument("--end-date", help="End date for availability check (YYYY-MM-DD)")
    list_bungalows_parser.set_defaults(func=list_bungalows)

    # Session commands
    session_parser = subparsers.add_parser("session", help="Session operations")
    session_subparsers = session_parser.add_subparsers(dest="session_command", help="Session command to run")

    # Add session
    add_session_parser = session_subparsers.add_parser("add", help="Add a session")
    add_session_parser.add_argument("numero", help="Session number")
    add_session_parser.add_argument("date_debut", help="Session start date (YYYY-MM-DD)")
    add_session_parser.add_argument("--description", help="Session description")
    add_session_parser.add_argument("--date-fin", help="Session end date (YYYY-MM-DD)")
    add_session_parser.add_argument("--etat-session", help="Session state")
    add_session_parser.set_defaults(func=add_session)

    # Get session
    get_session_parser = session_subparsers.add_parser("get", help="Get a session")
    get_session_group = get_session_parser.add_mutually_exclusive_group(required=True)
    get_session_group.add_argument("--id", type=int, help="Session ID")
    get_session_group.add_argument("--numero", help="Session number")
    get_session_parser.set_defaults(func=get_session)

    # List sessions
    list_sessions_parser = session_subparsers.add_parser("list", help="List all sessions")
    list_sessions_parser.set_defaults(func=list_sessions)

    # Distribution Bungalows commands
    distribution_parser = subparsers.add_parser("distribution", help="Bungalow Distribution operations")
    distribution_subparsers = distribution_parser.add_subparsers(dest="distribution_command", help="Distribution command to run")

    # Add distribution
    add_distribution_parser = distribution_subparsers.add_parser("add", help="Add a bungalow distribution")
    add_distribution_parser.add_argument("numero", help="Distribution number")
    add_distribution_parser.add_argument("--bungalow-id", help="Bungalow ID")
    add_distribution_parser.add_argument("--personnel-id", help="Personnel ID")
    add_distribution_parser.add_argument("--session-id", help="Session ID")
    add_distribution_parser.add_argument("--date-debut", help="Start date (YYYY-MM-DD)")
    add_distribution_parser.add_argument("--date-fin", help="End date (YYYY-MM-DD)")
    add_distribution_parser.add_argument("--notes", help="Notes")
    add_distribution_parser.set_defaults(func=add_distribution_bungalow)

    # Get distribution
    get_distribution_parser = distribution_subparsers.add_parser("get", help="Get a bungalow distribution")
    get_distribution_group = get_distribution_parser.add_mutually_exclusive_group(required=True)
    get_distribution_group.add_argument("--id", type=int, help="Distribution ID")
    get_distribution_group.add_argument("--numero", help="Distribution number")
    get_distribution_parser.add_argument("--include-details", action="store_true", help="Include bungalow and personnel details")
    get_distribution_parser.set_defaults(func=get_distribution_bungalow)

    # List distributions
    list_distributions_parser = distribution_subparsers.add_parser("list", help="List all bungalow distributions")
    list_distributions_parser.add_argument("--include-details", action="store_true", help="Include bungalow, personnel, and session details")
    list_distributions_parser.add_argument("--bungalow-id", help="Filter by bungalow ID")
    list_distributions_parser.add_argument("--personnel-id", help="Filter by personnel ID")
    list_distributions_parser.add_argument("--session-id", help="Filter by session ID")
    list_distributions_parser.set_defaults(func=list_distribution_bungalows)

    args = parser.parse_args()

    if not hasattr(args, "func"):
        parser.print_help()
        sys.exit(1)

    args.func(args)


if __name__ == "__main__":
    main()
