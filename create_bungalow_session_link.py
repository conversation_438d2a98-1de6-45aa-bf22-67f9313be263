#!/usr/bin/env python3
"""
Script pour créer le lien entre Bungalows et Sessions
"""

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def create_bungalow_session_link():
    """Créer le lien entre Bungalows et Sessions"""
    
    print("🔗 Création du lien entre Bungalows et Sessions...")
    
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # Option 1: Créer une table de liaison directe bungalow_sessions
    print("\n📋 Option 1: Table de liaison directe bungalow_sessions")
    try:
        db_manager.execute('''
        CREATE TABLE IF NOT EXISTS bungalow_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bungalow_id INTEGER NOT NULL,
            session_id INTEGER NOT NULL,
            date_assignation DATE DEFAULT CURRENT_DATE,
            statut TEXT DEFAULT 'active',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            <PERSON>OREI<PERSON><PERSON> KEY (bungalow_id) REFERENCES bungalows (id) ON DELETE CASCADE,
            FOREIGN KEY (session_id) REFERENCES sessions (id) ON DELETE CASCADE,
            UNIQUE(bungalow_id, session_id)
        )
        ''')
        print("✅ Table bungalow_sessions créée avec succès")
    except Exception as e:
        print(f"❌ Erreur lors de la création de bungalow_sessions: {e}")
    
    # Option 2: Ajouter session_id dans la table bungalows
    print("\n📋 Option 2: Ajout de session_id dans bungalows")
    try:
        db_manager.execute("ALTER TABLE bungalows ADD COLUMN session_id INTEGER")
        db_manager.execute("ALTER TABLE bungalows ADD COLUMN session_assignation_date DATE")
        print("✅ Colonnes session_id et session_assignation_date ajoutées à bungalows")
    except Exception as e:
        print(f"ℹ️  Colonnes déjà existantes ou erreur: {e}")
    
    # Option 3: Créer des vues pour faciliter les requêtes
    print("\n📋 Option 3: Création de vues pour les liaisons")
    try:
        # Vue pour les bungalows avec leurs sessions via distributions
        db_manager.execute('''
        CREATE VIEW IF NOT EXISTS bungalows_with_sessions AS
        SELECT DISTINCT
            b.id as bungalow_id,
            b.numero as bungalow_numero,
            b.endroit as bungalow_endroit,
            b.capacite as bungalow_capacite,
            s.id as session_id,
            s.numero as session_numero,
            s.description as session_description,
            s.date_debut as session_date_debut,
            s.date_fin as session_date_fin,
            d.date_debut as distribution_date_debut,
            d.date_fin as distribution_date_fin,
            d.notes as distribution_notes
        FROM bungalows b
        LEFT JOIN distribution_bungalows d ON b.id = d.bungalow_id
        LEFT JOIN sessions s ON d.session_id = s.id
        ''')
        print("✅ Vue bungalows_with_sessions créée")
        
        # Vue pour les sessions avec leurs bungalows
        db_manager.execute('''
        CREATE VIEW IF NOT EXISTS sessions_with_bungalows AS
        SELECT DISTINCT
            s.id as session_id,
            s.numero as session_numero,
            s.description as session_description,
            s.date_debut as session_date_debut,
            s.date_fin as session_date_fin,
            s.etat_session,
            b.id as bungalow_id,
            b.numero as bungalow_numero,
            b.endroit as bungalow_endroit,
            b.capacite as bungalow_capacite,
            COUNT(d.id) as distributions_count
        FROM sessions s
        LEFT JOIN distribution_bungalows d ON s.id = d.session_id
        LEFT JOIN bungalows b ON d.bungalow_id = b.id
        GROUP BY s.id, b.id
        ''')
        print("✅ Vue sessions_with_bungalows créée")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des vues: {e}")
    
    db_manager.commit()
    db_manager.close()
    
    print("\n🎉 Liaison entre Bungalows et Sessions créée avec succès!")
    print("\n📊 Types de liaisons disponibles:")
    print("1. 🔗 Table directe: bungalow_sessions")
    print("2. 🏠 Champ session_id dans bungalows")
    print("3. 📋 Via distributions (existant)")
    print("4. 👁️  Vues SQL pour requêtes facilitées")

def add_sample_bungalow_session_links():
    """Ajouter des exemples de liaisons bungalow-session"""
    
    print("\n📝 Ajout d'exemples de liaisons...")
    
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)
    
    # Récupérer les bungalows et sessions existants
    bungalows = db_ops.list_bungalows()
    sessions = db_ops.list_sessions()
    
    if not bungalows or not sessions:
        print("❌ Pas de bungalows ou sessions disponibles")
        db_manager.close()
        return
    
    # Exemples de liaisons directes
    sample_links = [
        {
            'bungalow_id': bungalows[0]['id'] if len(bungalows) > 0 else None,
            'session_id': sessions[0]['id'] if len(sessions) > 0 else None,
            'statut': 'active',
            'notes': 'Assignation automatique pour session principale'
        },
        {
            'bungalow_id': bungalows[1]['id'] if len(bungalows) > 1 else bungalows[0]['id'],
            'session_id': sessions[0]['id'] if len(sessions) > 0 else None,
            'statut': 'active',
            'notes': 'Bungalow secondaire pour la même session'
        }
    ]
    
    success_count = 0
    for link in sample_links:
        if link['bungalow_id'] and link['session_id']:
            try:
                db_manager.execute('''
                INSERT OR IGNORE INTO bungalow_sessions 
                (bungalow_id, session_id, statut, notes)
                VALUES (?, ?, ?, ?)
                ''', (link['bungalow_id'], link['session_id'], link['statut'], link['notes']))
                success_count += 1
            except Exception as e:
                print(f"❌ Erreur lors de l'ajout de liaison: {e}")
    
    # Mettre à jour quelques bungalows avec session_id
    if len(bungalows) > 0 and len(sessions) > 0:
        try:
            db_manager.execute('''
            UPDATE bungalows 
            SET session_id = ?, session_assignation_date = CURRENT_DATE
            WHERE id = ?
            ''', (sessions[0]['id'], bungalows[0]['id']))
            print(f"✅ Bungalow {bungalows[0]['numero']} lié à la session {sessions[0]['numero']}")
        except Exception as e:
            print(f"❌ Erreur lors de la mise à jour: {e}")
    
    db_manager.commit()
    db_manager.close()
    
    print(f"✅ {success_count} liaisons directes ajoutées")

def show_bungalow_session_relationships():
    """Afficher les relations entre bungalows et sessions"""
    
    print("\n📊 Relations Bungalows-Sessions:")
    
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # Afficher les liaisons directes
    print("\n🔗 Liaisons directes (bungalow_sessions):")
    try:
        result = db_manager.execute('''
        SELECT 
            b.numero as bungalow,
            b.endroit,
            s.numero as session,
            s.description,
            bs.statut,
            bs.date_assignation
        FROM bungalow_sessions bs
        JOIN bungalows b ON bs.bungalow_id = b.id
        JOIN sessions s ON bs.session_id = s.id
        ORDER BY bs.date_assignation DESC
        ''')
        
        rows = result.fetchall()
        if rows:
            for row in rows:
                print(f"  🏠 {row['bungalow']} ({row['endroit']}) ↔️ 📅 {row['session']} ({row['statut']})")
        else:
            print("  Aucune liaison directe trouvée")
    except Exception as e:
        print(f"  ❌ Erreur: {e}")
    
    # Afficher les liaisons via distributions
    print("\n📋 Liaisons via distributions:")
    try:
        result = db_manager.execute('''
        SELECT DISTINCT
            b.numero as bungalow,
            b.endroit,
            s.numero as session,
            s.description,
            COUNT(d.id) as distributions_count
        FROM distribution_bungalows d
        JOIN bungalows b ON d.bungalow_id = b.id
        JOIN sessions s ON d.session_id = s.id
        GROUP BY b.id, s.id
        ORDER BY distributions_count DESC
        ''')
        
        rows = result.fetchall()
        if rows:
            for row in rows:
                print(f"  🏠 {row['bungalow']} ({row['endroit']}) ↔️ 📅 {row['session']} ({row['distributions_count']} distributions)")
        else:
            print("  Aucune liaison via distributions trouvée")
    except Exception as e:
        print(f"  ❌ Erreur: {e}")
    
    db_manager.close()

if __name__ == "__main__":
    create_bungalow_session_link()
    add_sample_bungalow_session_links()
    show_bungalow_session_relationships()
