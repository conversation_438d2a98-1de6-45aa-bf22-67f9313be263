#!/usr/bin/env python3
"""
Create sample bungalow-session links for testing the filtering functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def create_sample_links():
    """Create sample bungalow-session links"""
    
    print("🔗 Creating Sample Bungalow-Session Links")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)
    
    # Get existing bungalows and sessions
    bungalows = db_ops.list_bungalows()
    sessions = db_ops.list_sessions()
    
    print(f"📊 Found {len(bungalows)} bungalows and {len(sessions)} sessions")
    
    if not bungalows or not sessions:
        print("❌ No bungalows or sessions available")
        db_manager.close()
        return
    
    # Create bungalow_sessions table if it doesn't exist
    try:
        db_manager.execute('''
        CREATE TABLE IF NOT EXISTS bungalow_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bungalow_id INTEGER NOT NULL,
            session_id INTEGER NOT NULL,
            date_assignation DATE DEFAULT CURRENT_DATE,
            statut TEXT DEFAULT 'active',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (bungalow_id) REFERENCES bungalows (id) ON DELETE CASCADE,
            FOREIGN KEY (session_id) REFERENCES sessions (id) ON DELETE CASCADE,
            UNIQUE(bungalow_id, session_id)
        )
        ''')
        print("✅ Table bungalow_sessions created/verified")
    except Exception as e:
        print(f"ℹ️  Table already exists or error: {e}")
    
    # Add session_id column to bungalows table if it doesn't exist
    try:
        db_manager.execute("ALTER TABLE bungalows ADD COLUMN session_id INTEGER")
        db_manager.execute("ALTER TABLE bungalows ADD COLUMN session_assignation_date DATE")
        print("✅ Added session_id column to bungalows table")
    except Exception as e:
        print(f"ℹ️  Columns already exist or error: {e}")
    
    # Create sample links
    sample_links = []
    
    # Link first 3 bungalows to first session
    if len(sessions) > 0:
        session1 = sessions[0]
        for i in range(min(3, len(bungalows))):
            sample_links.append({
                'bungalow_id': bungalows[i]['id'],
                'session_id': session1['id'],
                'notes': f'Bungalow {bungalows[i]["numero"]} assigné à {session1["numero"]}'
            })
    
    # Link next 2 bungalows to second session
    if len(sessions) > 1 and len(bungalows) > 3:
        session2 = sessions[1]
        for i in range(3, min(5, len(bungalows))):
            sample_links.append({
                'bungalow_id': bungalows[i]['id'],
                'session_id': session2['id'],
                'notes': f'Bungalow {bungalows[i]["numero"]} assigné à {session2["numero"]}'
            })
    
    # Create the links
    success_count = 0
    for link in sample_links:
        try:
            # Method 1: Direct link in bungalow_sessions table
            db_manager.execute('''
            INSERT OR IGNORE INTO bungalow_sessions 
            (bungalow_id, session_id, statut, notes)
            VALUES (?, ?, 'active', ?)
            ''', (link['bungalow_id'], link['session_id'], link['notes']))
            
            # Method 2: Update bungalow table with session_id
            db_manager.execute('''
            UPDATE bungalows 
            SET session_id = ?, session_assignation_date = CURRENT_DATE
            WHERE id = ?
            ''', (link['session_id'], link['bungalow_id']))
            
            success_count += 1
            print(f"✅ Linked bungalow {link['bungalow_id']} to session {link['session_id']}")
            
        except Exception as e:
            print(f"❌ Error creating link: {e}")
    
    db_manager.commit()
    
    # Show created links
    print(f"\n📋 Created {success_count} bungalow-session links")
    
    # Display the links
    print("\n🔍 Current Bungalow-Session Links:")
    try:
        db_manager.execute('''
        SELECT 
            b.numero as bungalow_numero,
            b.endroit as bungalow_endroit,
            s.numero as session_numero,
            s.description as session_description,
            bs.statut,
            bs.notes
        FROM bungalow_sessions bs
        JOIN bungalows b ON bs.bungalow_id = b.id
        JOIN sessions s ON bs.session_id = s.id
        ORDER BY b.numero
        ''')
        
        links = db_manager.cursor.fetchall()
        for link in links:
            print(f"  🏠 {link['bungalow_numero']} ({link['bungalow_endroit']}) ↔️ 📅 {link['session_numero']} ({link['session_description']})")
            
    except Exception as e:
        print(f"❌ Error displaying links: {e}")
    
    db_manager.close()
    print("\n✅ Sample links created successfully!")

if __name__ == "__main__":
    create_sample_links()
