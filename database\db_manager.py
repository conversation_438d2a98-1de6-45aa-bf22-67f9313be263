"""
Database connection manager for BANGHALAU
"""

import os
import sqlite3
from pathlib import Path


class DatabaseManager:
    """
    Manages database connections and initialization
    """

    def __init__(self, db_name="banghalau.db"):
        """
        Initialize the database manager

        Args:
            db_name (str): Name of the database file
        """
        self.db_name = db_name
        self.connection = None
        self.cursor = None

    def connect(self):
        """
        Connect to the database

        Returns:
            sqlite3.Connection: Database connection
        """
        try:
            self.connection = sqlite3.connect(self.db_name)
            self.connection.row_factory = sqlite3.Row
            self.cursor = self.connection.cursor()
            return self.connection
        except sqlite3.Error as e:
            print(f"Error connecting to database: {e}")
            raise

    def close(self):
        """
        Close the database connection
        """
        if self.connection:
            self.connection.close()
            self.connection = None
            self.cursor = None

    def commit(self):
        """
        Commit changes to the database
        """
        if self.connection:
            self.connection.commit()

    def execute(self, query, params=None):
        """
        Execute a query

        Args:
            query (str): SQL query
            params (tuple, optional): Query parameters

        Returns:
            sqlite3.Cursor: Query cursor
        """
        try:
            if params:
                return self.cursor.execute(query, params)
            return self.cursor.execute(query)
        except sqlite3.Error as e:
            print(f"Error executing query: {e}")
            print(f"Query: {query}")
            print(f"Params: {params}")
            raise

    def executemany(self, query, params_list):
        """
        Execute a query with multiple parameter sets

        Args:
            query (str): SQL query
            params_list (list): List of parameter tuples

        Returns:
            sqlite3.Cursor: Query cursor
        """
        try:
            return self.cursor.executemany(query, params_list)
        except sqlite3.Error as e:
            print(f"Error executing query: {e}")
            raise

    def initialize_database(self):
        """
        Initialize the database with tables
        """
        # Create users table
        self.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            email TEXT,
            password_hash TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Add password_hash column if it doesn't exist (for existing databases)
        try:
            self.execute("ALTER TABLE users ADD COLUMN password_hash TEXT")
            self.commit()
        except sqlite3.OperationalError:
            # Column already exists
            pass

        # Add additional user fields for user management
        user_columns = [
            ("role", "TEXT DEFAULT 'user'"),
            ("is_active", "BOOLEAN DEFAULT 1"),
            ("full_name", "TEXT"),
            ("last_login", "TIMESTAMP")
        ]

        for column_name, column_def in user_columns:
            try:
                self.execute(f"ALTER TABLE users ADD COLUMN {column_name} {column_def}")
                self.commit()
            except sqlite3.OperationalError:
                # Column already exists
                pass

        # Create items table
        self.execute('''
        CREATE TABLE IF NOT EXISTS items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            price REAL,
            user_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')

        # Create categories table
        self.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT
        )
        ''')

        # Create item_categories table (many-to-many relationship)
        self.execute('''
        CREATE TABLE IF NOT EXISTS item_categories (
            item_id INTEGER,
            category_id INTEGER,
            PRIMARY KEY (item_id, category_id),
            FOREIGN KEY (item_id) REFERENCES items (id),
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
        ''')

        # Create Grades table
        self.execute('''
        CREATE TABLE IF NOT EXISTS grades (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT NOT NULL UNIQUE,
            grade TEXT NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create Personnel Militaire Et Similaires table
        self.execute('''
        CREATE TABLE IF NOT EXISTS personnel_militaire (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT,
            matricule TEXT NOT NULL UNIQUE,
            nom TEXT NOT NULL,
            prenom TEXT NOT NULL,
            grade_id INTEGER,
            unite_id INTEGER,
            region TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (grade_id) REFERENCES grades (id),
            FOREIGN KEY (unite_id) REFERENCES unites (id)
        )
        ''')

        # Create Unités/Positions table
        self.execute('''
        CREATE TABLE IF NOT EXISTS unites (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT NOT NULL UNIQUE,
            description TEXT NOT NULL,
            raccourci TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create Bungalows table
        self.execute('''
        CREATE TABLE IF NOT EXISTS bungalows (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT NOT NULL UNIQUE,
            endroit TEXT NOT NULL,
            capacite INTEGER,
            caracteristiques TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create Sessions table
        self.execute('''
        CREATE TABLE IF NOT EXISTS sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT NOT NULL UNIQUE,
            description TEXT,
            date_debut DATE NOT NULL,
            date_fin DATE,
            etat_session TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create Distribution Bungalows table
        self.execute('''
        CREATE TABLE IF NOT EXISTS distribution_bungalows (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT NOT NULL UNIQUE,
            bungalow_id INTEGER,
            personnel_id INTEGER,
            session_id INTEGER,
            date_debut DATE,
            date_fin DATE,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (bungalow_id) REFERENCES bungalows (id),
            FOREIGN KEY (personnel_id) REFERENCES personnel_militaire (id),
            FOREIGN KEY (session_id) REFERENCES sessions (id)
        )
        ''')

        # Create indexes for better performance (Performance Optimization)
        self.create_performance_indexes()

        self.commit()
        print("Database initialized successfully.")

    def create_performance_indexes(self):
        """
        Create database indexes for better performance (Performance Optimization)
        """
        indexes = [
            # Bungalows search indexes
            "CREATE INDEX IF NOT EXISTS idx_bungalows_numero ON bungalows(numero)",
            "CREATE INDEX IF NOT EXISTS idx_bungalows_endroit ON bungalows(endroit)",
            "CREATE INDEX IF NOT EXISTS idx_bungalows_statut ON bungalows(statut)",

            # Personnel search indexes
            "CREATE INDEX IF NOT EXISTS idx_personnel_matricule ON personnel_militaire(matricule)",
            "CREATE INDEX IF NOT EXISTS idx_personnel_nom ON personnel_militaire(nom)",
            "CREATE INDEX IF NOT EXISTS idx_personnel_grade ON personnel_militaire(grade_id)",
            "CREATE INDEX IF NOT EXISTS idx_personnel_unite ON personnel_militaire(unite_id)",

            # Distribution indexes
            "CREATE INDEX IF NOT EXISTS idx_distribution_bungalow ON distribution_bungalows(bungalow_id)",
            "CREATE INDEX IF NOT EXISTS idx_distribution_personnel ON distribution_bungalows(personnel_id)",
            "CREATE INDEX IF NOT EXISTS idx_distribution_session ON distribution_bungalows(session_id)",
            "CREATE INDEX IF NOT EXISTS idx_distribution_dates ON distribution_bungalows(date_debut, date_fin)",

            # Session indexes
            "CREATE INDEX IF NOT EXISTS idx_sessions_numero ON sessions(numero)",
            "CREATE INDEX IF NOT EXISTS idx_sessions_dates ON sessions(date_debut, date_fin)",

            # User indexes
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)",
        ]

        for index_sql in indexes:
            try:
                self.execute(index_sql)
            except sqlite3.OperationalError as e:
                # Index might already exist
                if "already exists" not in str(e):
                    print(f"Warning creating index: {e}")
