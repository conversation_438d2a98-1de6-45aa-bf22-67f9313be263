"""
Database operations for BANGHALAU
"""

import sqlite3
from datetime import datetime
from .db_manager import DatabaseManager


class DatabaseOperations:
    """
    Provides CRUD operations for the database
    """

    def __init__(self, db_manager=None):
        """
        Initialize database operations

        Args:
            db_manager (DatabaseManager, optional): Database manager instance
        """
        self.db_manager = db_manager or DatabaseManager()
        if not self.db_manager.connection:
            self.db_manager.connect()

    def create_user(self, username, password_hash, email=None):
        """
        Create a new user

        Args:
            username (str): Username
            password_hash (str): Hashed password
            email (str, optional): Email address

        Returns:
            int: ID of the created user
        """
        try:
            self.db_manager.execute(
                "INSERT INTO users (username, password_hash, email) VALUES (?, ?, ?)",
                (username, password_hash, email)
            )
            self.db_manager.commit()
            return self.db_manager.cursor.lastrowid
        except sqlite3.IntegrityError:
            print(f"User with username '{username}' already exists")
            return None

    def get_user(self, user_id):
        """
        Get a user by ID

        Args:
            user_id (int): User ID

        Returns:
            dict: User data
        """
        self.db_manager.execute("SELECT * FROM users WHERE id = ?", (user_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_user_by_username(self, username):
        """
        Get a user by username

        Args:
            username (str): Username

        Returns:
            dict: User data
        """
        self.db_manager.execute("SELECT * FROM users WHERE username = ?", (username,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def list_users(self):
        """
        List all users

        Returns:
            list: List of users
        """
        self.db_manager.execute("SELECT * FROM users")
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def count_users(self):
        """
        Count total number of users (optimized)

        Returns:
            int: Number of users
        """
        self.db_manager.execute("SELECT COUNT(*) as count FROM users")
        result = self.db_manager.cursor.fetchone()
        return result['count'] if result else 0

    def update_user(self, user_id, username=None, email=None):
        """
        Update a user

        Args:
            user_id (int): User ID
            username (str, optional): New username
            email (str, optional): New email

        Returns:
            bool: True if successful
        """
        updates = []
        params = []

        if username:
            updates.append("username = ?")
            params.append(username)

        if email:
            updates.append("email = ?")
            params.append(email)

        if not updates:
            return False

        params.append(user_id)
        query = f"UPDATE users SET {', '.join(updates)} WHERE id = ?"

        self.db_manager.execute(query, tuple(params))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def delete_user(self, user_id):
        """
        Delete a user

        Args:
            user_id (int): User ID

        Returns:
            bool: True if successful
        """
        self.db_manager.execute("DELETE FROM users WHERE id = ?", (user_id,))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def create_item(self, name, description=None, price=None, user_id=None):
        """
        Create a new item

        Args:
            name (str): Item name
            description (str, optional): Item description
            price (float, optional): Item price
            user_id (int, optional): User ID

        Returns:
            int: ID of the created item
        """
        self.db_manager.execute(
            "INSERT INTO items (name, description, price, user_id) VALUES (?, ?, ?, ?)",
            (name, description, price, user_id)
        )
        self.db_manager.commit()
        return self.db_manager.cursor.lastrowid

    def get_item(self, item_id):
        """
        Get an item by ID

        Args:
            item_id (int): Item ID

        Returns:
            dict: Item data
        """
        self.db_manager.execute("SELECT * FROM items WHERE id = ?", (item_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def list_items(self, user_id=None):
        """
        List all items, optionally filtered by user

        Args:
            user_id (int, optional): User ID

        Returns:
            list: List of items
        """
        if user_id:
            self.db_manager.execute("SELECT * FROM items WHERE user_id = ?", (user_id,))
        else:
            self.db_manager.execute("SELECT * FROM items")
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def create_personnel_militaire(self, matricule, nom, prenom, grade_id=None, unite_id=None, numero=None, region=None):
        """
        Create a new military personnel

        Args:
            matricule (str): Matricule (unique identifier)
            nom (str): Last name
            prenom (str): First name
            grade_id (int, optional): ID of the military rank
            unite_id (int, optional): ID of the unit
            numero (str, optional): Number
            region (str, optional): Military region

        Returns:
            int: ID of the created personnel
        """
        try:
            print(f"🔥 DEBUG: Creating personnel with data:")
            print(f"   - matricule: {matricule}")
            print(f"   - nom: {nom}")
            print(f"   - prenom: {prenom}")
            print(f"   - grade_id: {grade_id}")
            print(f"   - unite_id: {unite_id}")
            print(f"   - numero: {numero}")
            print(f"   - region: {region}")

            self.db_manager.execute(
                "INSERT INTO personnel_militaire (matricule, nom, prenom, grade_id, unite_id, numero, region) VALUES (?, ?, ?, ?, ?, ?, ?)",
                (matricule, nom, prenom, grade_id, unite_id, numero, region)
            )
            self.db_manager.commit()
            personnel_id = self.db_manager.cursor.lastrowid
            print(f"🔥 DEBUG: Personnel created with ID: {personnel_id}")
            return personnel_id
        except sqlite3.IntegrityError as e:
            print(f"🔥 DEBUG: IntegrityError - Personnel with matricule '{matricule}' already exists: {e}")
            return None
        except Exception as e:
            print(f"🔥 DEBUG: Unexpected error creating personnel: {e}")
            return None

    def get_personnel_militaire(self, personnel_id):
        """
        Get a military personnel by ID

        Args:
            personnel_id (int): Personnel ID

        Returns:
            dict: Personnel data
        """
        self.db_manager.execute("SELECT * FROM personnel_militaire WHERE id = ?", (personnel_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_personnel_with_grade(self, personnel_id):
        """
        Get a military personnel by ID with grade information

        Args:
            personnel_id (int): Personnel ID

        Returns:
            dict: Personnel data with grade information
        """
        self.db_manager.execute("""
            SELECT p.*, g.grade as grade_name, g.numero as grade_numero, g.description as grade_description
            FROM personnel_militaire p
            LEFT JOIN grades g ON p.grade_id = g.id
            WHERE p.id = ?
        """, (personnel_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_personnel_with_unite(self, personnel_id):
        """
        Get a military personnel by ID with unit information

        Args:
            personnel_id (int): Personnel ID

        Returns:
            dict: Personnel data with unit information
        """
        self.db_manager.execute("""
            SELECT p.*, u.numero as unite_numero, u.description as unite_description, u.raccourci as unite_raccourci
            FROM personnel_militaire p
            LEFT JOIN unites u ON p.unite_id = u.id
            WHERE p.id = ?
        """, (personnel_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_personnel_with_grade_and_unite(self, personnel_id):
        """
        Get a military personnel by ID with grade and unit information

        Args:
            personnel_id (int): Personnel ID

        Returns:
            dict: Personnel data with grade and unit information
        """
        self.db_manager.execute("""
            SELECT p.*,
                   g.grade as grade_name, g.numero as grade_numero, g.description as grade_description,
                   u.numero as unite_numero, u.description as unite_description, u.raccourci as unite_raccourci
            FROM personnel_militaire p
            LEFT JOIN grades g ON p.grade_id = g.id
            LEFT JOIN unites u ON p.unite_id = u.id
            WHERE p.id = ?
        """, (personnel_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_personnel_by_matricule(self, matricule):
        """
        Get a military personnel by matricule

        Args:
            matricule (str): Matricule

        Returns:
            dict: Personnel data
        """
        self.db_manager.execute("SELECT * FROM personnel_militaire WHERE matricule = ?", (matricule,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_personnel_with_grade_by_matricule(self, matricule):
        """
        Get a military personnel by matricule with grade information

        Args:
            matricule (str): Matricule

        Returns:
            dict: Personnel data with grade information
        """
        self.db_manager.execute("""
            SELECT p.*, g.grade as grade_name, g.numero as grade_numero, g.description as grade_description
            FROM personnel_militaire p
            LEFT JOIN grades g ON p.grade_id = g.id
            WHERE p.matricule = ?
        """, (matricule,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_personnel_with_unite_by_matricule(self, matricule):
        """
        Get a military personnel by matricule with unit information

        Args:
            matricule (str): Matricule

        Returns:
            dict: Personnel data with unit information
        """
        self.db_manager.execute("""
            SELECT p.*, u.numero as unite_numero, u.description as unite_description, u.raccourci as unite_raccourci
            FROM personnel_militaire p
            LEFT JOIN unites u ON p.unite_id = u.id
            WHERE p.matricule = ?
        """, (matricule,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_personnel_with_grade_and_unite_by_matricule(self, matricule):
        """
        Get a military personnel by matricule with grade and unit information

        Args:
            matricule (str): Matricule

        Returns:
            dict: Personnel data with grade and unit information
        """
        self.db_manager.execute("""
            SELECT p.*,
                   g.grade as grade_name, g.numero as grade_numero, g.description as grade_description,
                   u.numero as unite_numero, u.description as unite_description, u.raccourci as unite_raccourci
            FROM personnel_militaire p
            LEFT JOIN grades g ON p.grade_id = g.id
            LEFT JOIN unites u ON p.unite_id = u.id
            WHERE p.matricule = ?
        """, (matricule,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def list_personnel_militaire(self):
        """
        List all military personnel

        Returns:
            list: List of personnel
        """
        self.db_manager.execute("SELECT * FROM personnel_militaire")
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def count_personnel_militaire(self):
        """
        Count total number of military personnel (optimized)

        Returns:
            int: Number of personnel
        """
        self.db_manager.execute("SELECT COUNT(*) as count FROM personnel_militaire")
        result = self.db_manager.cursor.fetchone()
        return result['count'] if result else 0

    def list_personnel_with_grades(self):
        """
        List all military personnel with grade information

        Returns:
            list: List of personnel with grade information
        """
        self.db_manager.execute("""
            SELECT p.*, g.grade as grade_name, g.numero as grade_numero, g.description as grade_description
            FROM personnel_militaire p
            LEFT JOIN grades g ON p.grade_id = g.id
        """)
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def list_personnel_with_unites(self):
        """
        List all military personnel with unit information

        Returns:
            list: List of personnel with unit information
        """
        self.db_manager.execute("""
            SELECT p.*, u.numero as unite_numero, u.description as unite_description, u.raccourci as unite_raccourci
            FROM personnel_militaire p
            LEFT JOIN unites u ON p.unite_id = u.id
        """)
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def list_personnel_with_grades_and_unites(self):
        """
        List all military personnel with grade and unit information

        Returns:
            list: List of personnel with grade and unit information
        """
        self.db_manager.execute("""
            SELECT p.*,
                   g.grade as grade_name, g.numero as grade_numero, g.description as grade_description,
                   u.numero as unite_numero, u.description as unite_description, u.raccourci as unite_raccourci
            FROM personnel_militaire p
            LEFT JOIN grades g ON p.grade_id = g.id
            LEFT JOIN unites u ON p.unite_id = u.id
        """)
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_personnel_by_id(self, personnel_id):
        """
        Get a military personnel by ID

        Args:
            personnel_id (int): Personnel ID

        Returns:
            dict: Personnel data
        """
        self.db_manager.execute("SELECT * FROM personnel_militaire WHERE id = ?", (personnel_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def update_personnel_militaire(self, personnel_id, numero=None, nom=None, prenom=None, grade_id=None, unite_id=None, telephone=None, email=None, matricule=None, region=None):
        """
        Update a military personnel

        Args:
            personnel_id (int): Personnel ID
            numero (str, optional): New personnel number
            nom (str, optional): New last name
            prenom (str, optional): New first name
            grade_id (int, optional): New grade ID
            unite_id (int, optional): New unit ID
            telephone (str, optional): New telephone
            email (str, optional): New email
            matricule (str, optional): New matricule
            region (str, optional): New region

        Returns:
            bool: True if successful
        """
        try:
            print(f"🔥 DEBUG: Updating personnel {personnel_id} with data:")
            print(f"   - numero: {numero}")
            print(f"   - nom: {nom}")
            print(f"   - prenom: {prenom}")
            print(f"   - grade_id: {grade_id}")
            print(f"   - unite_id: {unite_id}")
            print(f"   - matricule: {matricule}")
            print(f"   - region: {region}")

            updates = []
            params = []

            if numero:
                updates.append("numero = ?")
                params.append(numero)

            if nom:
                updates.append("nom = ?")
                params.append(nom)

            if prenom:
                updates.append("prenom = ?")
                params.append(prenom)

            if grade_id is not None:
                updates.append("grade_id = ?")
                params.append(grade_id)

            if unite_id is not None:
                updates.append("unite_id = ?")
                params.append(unite_id)

            if telephone is not None:
                updates.append("telephone = ?")
                params.append(telephone)

            if email is not None:
                updates.append("email = ?")
                params.append(email)

            if matricule is not None:
                updates.append("matricule = ?")
                params.append(matricule)

            if region is not None:
                updates.append("region = ?")
                params.append(region)

            if not updates:
                print(f"🔥 DEBUG: No updates to apply for personnel {personnel_id}")
                return False

            params.append(personnel_id)
            query = f"UPDATE personnel_militaire SET {', '.join(updates)} WHERE id = ?"
            print(f"🔥 DEBUG: Executing query: {query}")
            print(f"🔥 DEBUG: With params: {params}")

            self.db_manager.execute(query, tuple(params))
            self.db_manager.commit()
            rowcount = self.db_manager.cursor.rowcount
            print(f"🔥 DEBUG: Personnel update affected {rowcount} rows")
            return rowcount > 0
        except Exception as e:
            print(f"🔥 DEBUG: Error updating personnel: {e}")
            return False

    def delete_personnel_militaire(self, personnel_id):
        """
        Delete a military personnel

        Args:
            personnel_id (int): Personnel ID

        Returns:
            bool: True if successful
        """
        try:
            print(f"🔥 DEBUG: Deleting personnel with ID: {personnel_id}")
            self.db_manager.execute("DELETE FROM personnel_militaire WHERE id = ?", (personnel_id,))
            self.db_manager.commit()
            rowcount = self.db_manager.cursor.rowcount
            print(f"🔥 DEBUG: Personnel deletion affected {rowcount} rows")
            return rowcount > 0
        except Exception as e:
            print(f"🔥 DEBUG: Error deleting personnel: {e}")
            return False

    def get_personnel_by_grade(self, grade_id):
        """
        Get all military personnel with a specific grade

        Args:
            grade_id (int): Grade ID

        Returns:
            list: List of personnel with the specified grade
        """
        self.db_manager.execute("SELECT * FROM personnel_militaire WHERE grade_id = ?", (grade_id,))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_personnel_by_unite(self, unite_id):
        """
        Get all military personnel with a specific unit

        Args:
            unite_id (int): Unit ID

        Returns:
            list: List of personnel with the specified unit
        """
        self.db_manager.execute("SELECT * FROM personnel_militaire WHERE unite_id = ?", (unite_id,))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_personnel_by_grade_and_unite(self, grade_id, unite_id):
        """
        Get all military personnel with a specific grade and unit

        Args:
            grade_id (int): Grade ID
            unite_id (int): Unit ID

        Returns:
            list: List of personnel with the specified grade and unit
        """
        self.db_manager.execute("SELECT * FROM personnel_militaire WHERE grade_id = ? AND unite_id = ?", (grade_id, unite_id))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def update_personnel_militaire(self, personnel_id, matricule=None, nom=None, prenom=None,
                                  grade_id=None, unite_id=None, numero=None, region=None):
        """
        Update a military personnel

        Args:
            personnel_id (int): Personnel ID
            matricule (str, optional): New matricule
            nom (str, optional): New last name
            prenom (str, optional): New first name
            grade_id (int, optional): New military rank ID
            unite_id (int, optional): New unit ID
            numero (str, optional): New number
            region (str, optional): New military region

        Returns:
            bool: True if successful
        """
        updates = []
        params = []

        if matricule:
            updates.append("matricule = ?")
            params.append(matricule)

        if nom:
            updates.append("nom = ?")
            params.append(nom)

        if prenom:
            updates.append("prenom = ?")
            params.append(prenom)

        if grade_id is not None:  # Allow setting grade_id to NULL (None)
            updates.append("grade_id = ?")
            params.append(grade_id)

        if unite_id is not None:  # Allow setting unite_id to NULL (None)
            updates.append("unite_id = ?")
            params.append(unite_id)

        if numero:
            updates.append("numero = ?")
            params.append(numero)

        if region is not None:  # Allow setting region to NULL (None)
            updates.append("region = ?")
            params.append(region)

        if not updates:
            return False

        params.append(personnel_id)
        query = f"UPDATE personnel_militaire SET {', '.join(updates)} WHERE id = ?"

        self.db_manager.execute(query, tuple(params))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def delete_personnel_militaire(self, personnel_id):
        """
        Delete a military personnel

        Args:
            personnel_id (int): Personnel ID

        Returns:
            bool: True if successful
        """
        self.db_manager.execute("DELETE FROM personnel_militaire WHERE id = ?", (personnel_id,))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def create_grade(self, numero, grade, description=None):
        """
        Create a new grade

        Args:
            numero (str): Grade number
            grade (str): Grade name
            description (str, optional): Grade description

        Returns:
            int: ID of the created grade
        """
        try:
            self.db_manager.execute(
                "INSERT INTO grades (numero, grade, description) VALUES (?, ?, ?)",
                (numero, grade, description)
            )
            self.db_manager.commit()
            return self.db_manager.cursor.lastrowid
        except sqlite3.IntegrityError:
            print(f"Grade with numero '{numero}' or name '{grade}' already exists")
            return None

    def get_grade(self, grade_id):
        """
        Get a grade by ID

        Args:
            grade_id (int): Grade ID

        Returns:
            dict: Grade data
        """
        self.db_manager.execute("SELECT * FROM grades WHERE id = ?", (grade_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_grade_by_numero(self, numero):
        """
        Get a grade by numero

        Args:
            numero (str): Grade number

        Returns:
            dict: Grade data
        """
        self.db_manager.execute("SELECT * FROM grades WHERE numero = ?", (numero,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_grade_by_name(self, grade):
        """
        Get a grade by name

        Args:
            grade (str): Grade name

        Returns:
            dict: Grade data
        """
        self.db_manager.execute("SELECT * FROM grades WHERE grade = ?", (grade,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def list_grades(self):
        """
        List all grades

        Returns:
            list: List of grades
        """
        self.db_manager.execute("SELECT * FROM grades")
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def count_grades(self):
        """
        Count total number of grades (optimized)

        Returns:
            int: Number of grades
        """
        self.db_manager.execute("SELECT COUNT(*) as count FROM grades")
        result = self.db_manager.cursor.fetchone()
        return result['count'] if result else 0

    def list_grades_with_personnel_count(self):
        """
        List all grades with the count of personnel in each grade

        Returns:
            list: List of grades with personnel count
        """
        self.db_manager.execute("""
            SELECT g.*, COUNT(p.id) as personnel_count
            FROM grades g
            LEFT JOIN personnel_militaire p ON g.id = p.grade_id
            GROUP BY g.id
        """)
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def update_grade(self, grade_id, numero=None, grade=None, description=None):
        """
        Update a grade

        Args:
            grade_id (int): Grade ID
            numero (str, optional): New grade number
            grade (str, optional): New grade name
            description (str, optional): New grade description

        Returns:
            bool: True if successful
        """
        updates = []
        params = []

        if numero:
            updates.append("numero = ?")
            params.append(numero)

        if grade:
            updates.append("grade = ?")
            params.append(grade)

        if description:
            updates.append("description = ?")
            params.append(description)

        if not updates:
            return False

        params.append(grade_id)
        query = f"UPDATE grades SET {', '.join(updates)} WHERE id = ?"

        self.db_manager.execute(query, tuple(params))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def delete_grade(self, grade_id):
        """
        Delete a grade

        Args:
            grade_id (int): Grade ID

        Returns:
            bool: True if successful
        """
        self.db_manager.execute("DELETE FROM grades WHERE id = ?", (grade_id,))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def create_unite(self, numero, description, raccourci=None):
        """
        Create a new unit/position

        Args:
            numero (str): Unit number
            description (str): Unit description
            raccourci (str, optional): Unit shortcut/abbreviation

        Returns:
            int: ID of the created unit
        """
        try:
            self.db_manager.execute(
                "INSERT INTO unites (numero, description, raccourci) VALUES (?, ?, ?)",
                (numero, description, raccourci)
            )
            self.db_manager.commit()
            return self.db_manager.cursor.lastrowid
        except sqlite3.IntegrityError:
            print(f"Unit with numero '{numero}' already exists")
            return None

    def get_unite(self, unite_id):
        """
        Get a unit by ID

        Args:
            unite_id (int): Unit ID

        Returns:
            dict: Unit data
        """
        self.db_manager.execute("SELECT * FROM unites WHERE id = ?", (unite_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_unite_by_numero(self, numero):
        """
        Get a unit by numero

        Args:
            numero (str): Unit number

        Returns:
            dict: Unit data
        """
        self.db_manager.execute("SELECT * FROM unites WHERE numero = ?", (numero,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_unite_by_raccourci(self, raccourci):
        """
        Get a unit by shortcut

        Args:
            raccourci (str): Unit shortcut

        Returns:
            dict: Unit data
        """
        self.db_manager.execute("SELECT * FROM unites WHERE raccourci = ?", (raccourci,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def list_unites(self):
        """
        List all units

        Returns:
            list: List of units
        """
        self.db_manager.execute("SELECT * FROM unites")
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def count_unites(self):
        """
        Count total number of units (optimized)

        Returns:
            int: Number of units
        """
        self.db_manager.execute("SELECT COUNT(*) as count FROM unites")
        result = self.db_manager.cursor.fetchone()
        return result['count'] if result else 0

    def update_unite(self, unite_id, numero=None, description=None, raccourci=None):
        """
        Update a unit

        Args:
            unite_id (int): Unit ID
            numero (str, optional): New unit number
            description (str, optional): New unit description
            raccourci (str, optional): New unit shortcut

        Returns:
            bool: True if successful
        """
        updates = []
        params = []

        if numero:
            updates.append("numero = ?")
            params.append(numero)

        if description:
            updates.append("description = ?")
            params.append(description)

        if raccourci is not None:  # Allow empty string to clear the shortcut
            updates.append("raccourci = ?")
            params.append(raccourci)

        if not updates:
            return False

        params.append(unite_id)
        query = f"UPDATE unites SET {', '.join(updates)} WHERE id = ?"

        self.db_manager.execute(query, tuple(params))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def delete_unite(self, unite_id):
        """
        Delete a unit

        Args:
            unite_id (int): Unit ID

        Returns:
            bool: True if successful
        """
        self.db_manager.execute("DELETE FROM unites WHERE id = ?", (unite_id,))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def create_bungalow(self, numero, endroit, capacite=None, caracteristiques=None, statut='Disponible', notes_statut=None):
        """
        Create a new bungalow

        Args:
            numero (str): Bungalow number
            endroit (str): Bungalow location
            capacite (int, optional): Bungalow capacity
            caracteristiques (str, optional): Bungalow characteristics
            statut (str, optional): Bungalow status (default: 'Disponible')
            notes_statut (str, optional): Status notes

        Returns:
            int: ID of the created bungalow
        """
        try:
            print(f"🔥 DEBUG: Creating bungalow with data:")
            print(f"   - numero: {numero}")
            print(f"   - endroit: {endroit}")
            print(f"   - capacite: {capacite}")
            print(f"   - caracteristiques: {caracteristiques}")
            print(f"   - statut: {statut}")
            print(f"   - notes_statut: {notes_statut}")

            self.db_manager.execute(
                "INSERT INTO bungalows (numero, endroit, capacite, caracteristiques, statut, notes_statut) VALUES (?, ?, ?, ?, ?, ?)",
                (numero, endroit, capacite, caracteristiques, statut, notes_statut)
            )
            self.db_manager.commit()
            bungalow_id = self.db_manager.cursor.lastrowid
            print(f"🔥 DEBUG: Bungalow created with ID: {bungalow_id}")
            return bungalow_id
        except sqlite3.IntegrityError as e:
            print(f"🔥 DEBUG: IntegrityError - Bungalow with numero '{numero}' already exists: {e}")
            return None
        except Exception as e:
            print(f"🔥 DEBUG: Unexpected error creating bungalow: {e}")
            return None

    def get_bungalow(self, bungalow_id):
        """
        Get a bungalow by ID

        Args:
            bungalow_id (int): Bungalow ID

        Returns:
            dict: Bungalow data
        """
        self.db_manager.execute("SELECT * FROM bungalows WHERE id = ?", (bungalow_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_bungalow_by_numero(self, numero):
        """
        Get a bungalow by numero

        Args:
            numero (str): Bungalow number

        Returns:
            dict: Bungalow data
        """
        self.db_manager.execute("SELECT * FROM bungalows WHERE numero = ?", (numero,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_bungalow_by_id(self, bungalow_id):
        """
        Get a bungalow by ID (alias for get_bungalow)

        Args:
            bungalow_id (int): Bungalow ID

        Returns:
            dict: Bungalow data
        """
        return self.get_bungalow(bungalow_id)

    def list_bungalows(self):
        """
        List all bungalows

        Returns:
            list: List of bungalows
        """
        self.db_manager.execute("SELECT * FROM bungalows")
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def count_bungalows(self):
        """
        Count total number of bungalows (optimized)

        Returns:
            int: Number of bungalows
        """
        self.db_manager.execute("SELECT COUNT(*) as count FROM bungalows")
        result = self.db_manager.cursor.fetchone()
        return result['count'] if result else 0

    def list_bungalows_with_sessions(self):
        """
        List all bungalows with their associated session information

        Returns:
            list: List of bungalows with session details
        """
        self.db_manager.execute('''
        SELECT b.*,
               s.numero as session_numero,
               s.description as session_description,
               s.date_debut as session_date_debut,
               s.date_fin as session_date_fin,
               s.etat_session as session_etat,
               bs.statut as session_statut,
               bs.date_assignation as session_date_assignation
        FROM bungalows b
        LEFT JOIN bungalow_sessions bs ON b.id = bs.bungalow_id AND bs.statut = 'active'
        LEFT JOIN sessions s ON bs.session_id = s.id
        ORDER BY b.numero
        ''')
        rows = self.db_manager.cursor.fetchall()
        return [dict(row) for row in rows]

    def get_available_bungalows(self, start_date, end_date):
        """
        Get all bungalows that are available during a specific period

        Args:
            start_date (str): Start date (YYYY-MM-DD format)
            end_date (str): End date (YYYY-MM-DD format)

        Returns:
            list: List of available bungalows
        """
        self.db_manager.execute("""
            SELECT b.* FROM bungalows b
            WHERE b.id NOT IN (
                SELECT d.bungalow_id FROM distribution_bungalows d
                WHERE d.bungalow_id IS NOT NULL
                AND (
                    (d.date_debut <= ? AND d.date_fin >= ?) OR
                    (d.date_debut <= ? AND d.date_fin >= ?) OR
                    (d.date_debut >= ? AND d.date_fin <= ?)
                )
            )
        """, (end_date, start_date, start_date, start_date, start_date, end_date))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_occupied_bungalows(self, date=None):
        """
        Get all bungalows that are occupied on a specific date

        Args:
            date (str, optional): Date to check (YYYY-MM-DD format). If None, uses current date.

        Returns:
            list: List of occupied bungalows with distribution details
        """
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")

        self.db_manager.execute("""
            SELECT b.*, d.id as distribution_id, d.numero as distribution_numero,
                   d.personnel_id, d.session_id, d.date_debut, d.date_fin, d.notes
            FROM bungalows b
            JOIN distribution_bungalows d ON b.id = d.bungalow_id
            WHERE (d.date_debut <= ? OR d.date_debut IS NULL)
            AND (d.date_fin >= ? OR d.date_fin IS NULL)
        """, (date, date))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]





    def update_bungalow(self, bungalow_id, numero=None, endroit=None, capacite=None, caracteristiques=None, statut=None, notes_statut=None):
        """
        Update a bungalow

        Args:
            bungalow_id (int): Bungalow ID
            numero (str, optional): New bungalow number
            endroit (str, optional): New bungalow location
            capacite (int, optional): New bungalow capacity
            caracteristiques (str, optional): New bungalow characteristics

        Returns:
            bool: True if successful
        """
        updates = []
        params = []

        if numero:
            updates.append("numero = ?")
            params.append(numero)

        if endroit:
            updates.append("endroit = ?")
            params.append(endroit)

        if capacite is not None:  # Allow 0 capacity
            updates.append("capacite = ?")
            params.append(capacite)

        if caracteristiques is not None:  # Allow empty string to clear the characteristics
            updates.append("caracteristiques = ?")
            params.append(caracteristiques)

        if statut is not None:  # Allow empty string
            updates.append("statut = ?")
            params.append(statut)

        if notes_statut is not None:  # Allow empty string
            updates.append("notes_statut = ?")
            params.append(notes_statut)

        if not updates:
            return False

        params.append(bungalow_id)
        query = f"UPDATE bungalows SET {', '.join(updates)} WHERE id = ?"

        try:
            print(f"🔥 DEBUG: Executing bungalow update query: {query}")
            print(f"🔥 DEBUG: With params: {params}")
            self.db_manager.execute(query, tuple(params))
            self.db_manager.commit()
            rowcount = self.db_manager.cursor.rowcount
            print(f"🔥 DEBUG: Bungalow update affected {rowcount} rows")
            return rowcount > 0
        except Exception as e:
            print(f"🔥 DEBUG: Error updating bungalow: {e}")
            return False

    def delete_bungalow(self, bungalow_id):
        """
        Delete a bungalow

        Args:
            bungalow_id (int): Bungalow ID

        Returns:
            bool: True if successful
        """
        self.db_manager.execute("DELETE FROM bungalows WHERE id = ?", (bungalow_id,))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def create_session(self, numero, date_debut, description=None, date_fin=None, etat_session=None):
        """
        Create a new session

        Args:
            numero (str): Session number
            date_debut (str): Session start date (YYYY-MM-DD format)
            description (str, optional): Session description
            date_fin (str, optional): Session end date (YYYY-MM-DD format)
            etat_session (str, optional): Session state

        Returns:
            int: ID of the created session
        """
        try:
            self.db_manager.execute(
                "INSERT INTO sessions (numero, description, date_debut, date_fin, etat_session) VALUES (?, ?, ?, ?, ?)",
                (numero, description, date_debut, date_fin, etat_session)
            )
            self.db_manager.commit()
            return self.db_manager.cursor.lastrowid
        except sqlite3.IntegrityError:
            print(f"Session with numero '{numero}' already exists")
            return None

    def get_session(self, session_id):
        """
        Get a session by ID

        Args:
            session_id (int): Session ID

        Returns:
            dict: Session data
        """
        self.db_manager.execute("SELECT * FROM sessions WHERE id = ?", (session_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_session_by_numero(self, numero):
        """
        Get a session by numero

        Args:
            numero (str): Session number

        Returns:
            dict: Session data
        """
        self.db_manager.execute("SELECT * FROM sessions WHERE numero = ?", (numero,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def list_sessions(self):
        """
        List all sessions

        Returns:
            list: List of sessions
        """
        self.db_manager.execute("SELECT * FROM sessions")
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def count_sessions(self):
        """
        Count total number of sessions (optimized)

        Returns:
            int: Number of sessions
        """
        self.db_manager.execute("SELECT COUNT(*) as count FROM sessions")
        result = self.db_manager.cursor.fetchone()
        return result['count'] if result else 0

    def update_session(self, session_id, numero=None, description=None, date_debut=None, date_fin=None, etat_session=None):
        """
        Update a session

        Args:
            session_id (int): Session ID
            numero (str, optional): New session number
            description (str, optional): New session description
            date_debut (str, optional): New session start date (YYYY-MM-DD format)
            date_fin (str, optional): New session end date (YYYY-MM-DD format)
            etat_session (str, optional): New session state

        Returns:
            bool: True if successful
        """
        updates = []
        params = []

        if numero:
            updates.append("numero = ?")
            params.append(numero)

        if description is not None:  # Allow empty string to clear the description
            updates.append("description = ?")
            params.append(description)

        if date_debut:
            updates.append("date_debut = ?")
            params.append(date_debut)

        if date_fin is not None:  # Allow empty string to clear the end date
            updates.append("date_fin = ?")
            params.append(date_fin)

        if etat_session is not None:  # Allow empty string to clear the state
            updates.append("etat_session = ?")
            params.append(etat_session)

        if not updates:
            return False

        params.append(session_id)
        query = f"UPDATE sessions SET {', '.join(updates)} WHERE id = ?"

        self.db_manager.execute(query, tuple(params))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def delete_session(self, session_id):
        """
        Delete a session

        Args:
            session_id (int): Session ID

        Returns:
            bool: True if successful
        """
        self.db_manager.execute("DELETE FROM sessions WHERE id = ?", (session_id,))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def create_distribution_bungalow(self, numero, bungalow_id=None, personnel_id=None, session_id=None, date_debut=None, date_fin=None, notes=None):
        """
        Create a new bungalow distribution and decrease bungalow capacity

        Args:
            numero (str): Distribution number
            bungalow_id (int, optional): Bungalow ID
            personnel_id (int, optional): Personnel ID
            session_id (int, optional): Session ID
            date_debut (str, optional): Start date (YYYY-MM-DD format)
            date_fin (str, optional): End date (YYYY-MM-DD format)
            notes (str, optional): Notes

        Returns:
            int: ID of the created distribution
        """
        try:
            # Check bungalow capacity before creating distribution
            if bungalow_id:
                bungalow = self.get_bungalow_by_id(bungalow_id)
                if bungalow and bungalow.get('capacite', 0) <= 0:
                    print(f"Bungalow {bungalow_id} has no available capacity")
                    return None

            # Create the distribution
            self.db_manager.execute(
                "INSERT INTO distribution_bungalows (numero, bungalow_id, personnel_id, session_id, date_debut, date_fin, notes) VALUES (?, ?, ?, ?, ?, ?, ?)",
                (numero, bungalow_id, personnel_id, session_id, date_debut, date_fin, notes)
            )

            # Decrease bungalow capacity by 1
            if bungalow_id:
                self.db_manager.execute(
                    "UPDATE bungalows SET capacite = capacite - 1 WHERE id = ? AND capacite > 0",
                    (bungalow_id,)
                )
                print(f"✅ Decreased capacity for bungalow {bungalow_id}")

            self.db_manager.commit()
            return self.db_manager.cursor.lastrowid
        except sqlite3.IntegrityError:
            print(f"Distribution with numero '{numero}' already exists")
            return None

    def get_bungalow_capacity_stats(self, bungalow_id):
        """
        Get capacity statistics for a bungalow

        Args:
            bungalow_id (int): Bungalow ID

        Returns:
            dict: Capacity statistics with original_capacity, current_capacity, used, remaining
        """
        try:
            # Get current bungalow info
            bungalow = self.get_bungalow_by_id(bungalow_id)
            if not bungalow:
                return None

            current_capacity = bungalow.get('capacite', 0)

            # Count active distributions for this bungalow
            self.db_manager.execute("""
                SELECT COUNT(*) as used_count
                FROM distribution_bungalows
                WHERE bungalow_id = ?
                AND (date_fin IS NULL OR date_fin >= date('now'))
            """, (bungalow_id,))

            result = self.db_manager.cursor.fetchone()
            used_count = result[0] if result else 0

            # Calculate original capacity (current + used)
            original_capacity = current_capacity + used_count

            return {
                'bungalow_id': bungalow_id,
                'original_capacity': original_capacity,
                'current_capacity': current_capacity,
                'used': used_count,
                'remaining': current_capacity
            }

        except Exception as e:
            print(f"Error getting capacity stats for bungalow {bungalow_id}: {e}")
            return None

    def get_distribution_bungalow(self, distribution_id):
        """
        Get a bungalow distribution by ID

        Args:
            distribution_id (int): Distribution ID

        Returns:
            dict: Distribution data
        """
        self.db_manager.execute("SELECT * FROM distribution_bungalows WHERE id = ?", (distribution_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_distribution_bungalow_by_numero(self, numero):
        """
        Get a bungalow distribution by numero

        Args:
            numero (str): Distribution number

        Returns:
            dict: Distribution data
        """
        self.db_manager.execute("SELECT * FROM distribution_bungalows WHERE numero = ?", (numero,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_distribution_by_id(self, distribution_id):
        """
        Get a distribution by ID (alias for get_distribution_bungalow)

        Args:
            distribution_id (int): Distribution ID

        Returns:
            dict: Distribution data
        """
        return self.get_distribution_bungalow(distribution_id)

    def list_distribution_bungalows(self):
        """
        List all bungalow distributions

        Returns:
            list: List of distributions
        """
        self.db_manager.execute("SELECT * FROM distribution_bungalows")
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def count_distribution_bungalows(self):
        """
        Count total number of distributions (optimized)

        Returns:
            int: Number of distributions
        """
        self.db_manager.execute("SELECT COUNT(*) as count FROM distribution_bungalows")
        result = self.db_manager.cursor.fetchone()
        return result['count'] if result else 0

    def get_distribution_bungalows_by_bungalow(self, bungalow_id):
        """
        Get all distributions for a specific bungalow

        Args:
            bungalow_id (int): Bungalow ID

        Returns:
            list: List of distributions
        """
        self.db_manager.execute("SELECT * FROM distribution_bungalows WHERE bungalow_id = ?", (bungalow_id,))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_distribution_bungalows_by_bungalow_with_details(self, bungalow_id):
        """
        Get all distributions for a specific bungalow with details

        Args:
            bungalow_id (int): Bungalow ID

        Returns:
            list: List of distributions with details
        """
        self.db_manager.execute("""
            SELECT d.*,
                   b.numero as bungalow_numero, b.endroit as bungalow_endroit, b.capacite as bungalow_capacite,
                   p.matricule as personnel_matricule, p.nom as personnel_nom, p.prenom as personnel_prenom,
                   p.numero as personnel_numero,
                   g.grade as personnel_grade,
                   u.description as unite_description,
                   s.numero as session_numero, s.description as session_description, s.date_debut as session_date_debut, s.date_fin as session_date_fin
            FROM distribution_bungalows d
            LEFT JOIN bungalows b ON d.bungalow_id = b.id
            LEFT JOIN personnel_militaire p ON d.personnel_id = p.id
            LEFT JOIN grades g ON p.grade_id = g.id
            LEFT JOIN unites u ON p.unite_id = u.id
            LEFT JOIN sessions s ON d.session_id = s.id
            WHERE d.bungalow_id = ?
        """, (bungalow_id,))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_bungalow_current_distribution(self, bungalow_id, date=None):
        """
        Get the current distribution for a specific bungalow

        Args:
            bungalow_id (int): Bungalow ID
            date (str, optional): Date to check (YYYY-MM-DD format). If None, uses current date.

        Returns:
            dict: Current distribution or None if not assigned
        """
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")

        self.db_manager.execute("""
            SELECT * FROM distribution_bungalows
            WHERE bungalow_id = ?
            AND (date_debut <= ? OR date_debut IS NULL)
            AND (date_fin >= ? OR date_fin IS NULL)
        """, (bungalow_id, date, date))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_bungalows_by_session(self, session_id):
        """
        Get all bungalows assigned to a specific session

        Args:
            session_id (int): Session ID

        Returns:
            list: List of bungalows with distribution details
        """
        self.db_manager.execute("""
            SELECT b.*, d.id as distribution_id, d.numero as distribution_numero,
                   d.personnel_id, d.date_debut, d.date_fin, d.notes
            FROM bungalows b
            JOIN distribution_bungalows d ON b.id = d.bungalow_id
            WHERE d.session_id = ?
        """, (session_id,))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_bungalows_with_occupancy_status(self, date=None):
        """
        Get all bungalows with their occupancy status for a specific date

        Args:
            date (str, optional): Date to check (YYYY-MM-DD format). If None, uses current date.

        Returns:
            list: List of bungalows with occupancy status
        """
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")

        self.db_manager.execute("""
            SELECT b.*,
                   CASE WHEN d.id IS NULL THEN 'Available' ELSE 'Occupied' END as status,
                   d.id as distribution_id, d.numero as distribution_numero,
                   d.personnel_id, d.session_id, d.date_debut, d.date_fin, d.notes
            FROM bungalows b
            LEFT JOIN distribution_bungalows d ON b.id = d.bungalow_id
                AND (d.date_debut <= ? OR d.date_debut IS NULL)
                AND (d.date_fin >= ? OR d.date_fin IS NULL)
        """, (date, date))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_distribution_bungalows_by_personnel(self, personnel_id):
        """
        Get all distributions for a specific personnel

        Args:
            personnel_id (int): Personnel ID

        Returns:
            list: List of distributions
        """
        self.db_manager.execute("SELECT * FROM distribution_bungalows WHERE personnel_id = ?", (personnel_id,))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_distribution_bungalows_by_session(self, session_id):
        """
        Get all distributions for a specific session

        Args:
            session_id (int): Session ID

        Returns:
            list: List of distributions
        """
        self.db_manager.execute("SELECT * FROM distribution_bungalows WHERE session_id = ?", (session_id,))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_distribution_bungalows_by_session_with_details(self, session_id):
        """
        Get all distributions for a specific session with details

        Args:
            session_id (int): Session ID

        Returns:
            list: List of distributions with details
        """
        self.db_manager.execute("""
            SELECT d.*,
                   b.numero as bungalow_numero, b.endroit as bungalow_endroit, b.capacite as bungalow_capacite,
                   p.matricule as personnel_matricule, p.nom as personnel_nom, p.prenom as personnel_prenom,
                   p.numero as personnel_numero,
                   g.grade as personnel_grade,
                   u.description as unite_description,
                   s.numero as session_numero, s.description as session_description, s.date_debut as session_date_debut, s.date_fin as session_date_fin
            FROM distribution_bungalows d
            LEFT JOIN bungalows b ON d.bungalow_id = b.id
            LEFT JOIN personnel_militaire p ON d.personnel_id = p.id
            LEFT JOIN grades g ON p.grade_id = g.id
            LEFT JOIN unites u ON p.unite_id = u.id
            LEFT JOIN sessions s ON d.session_id = s.id
            WHERE d.session_id = ?
        """, (session_id,))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_distribution_with_details(self, distribution_id):
        """
        Get a bungalow distribution by ID with bungalow, personnel, and session details

        Args:
            distribution_id (int): Distribution ID

        Returns:
            dict: Distribution data with bungalow, personnel, and session details
        """
        self.db_manager.execute("""
            SELECT d.*,
                   b.numero as bungalow_numero, b.endroit as bungalow_endroit, b.capacite as bungalow_capacite,
                   p.matricule as personnel_matricule, p.nom as personnel_nom, p.prenom as personnel_prenom,
                   p.numero as personnel_numero, p.unite_id,
                   g.grade as personnel_grade,
                   u.description as unite_description,
                   s.numero as session_numero, s.description as session_description, s.date_debut as session_date_debut, s.date_fin as session_date_fin
            FROM distribution_bungalows d
            LEFT JOIN bungalows b ON d.bungalow_id = b.id
            LEFT JOIN personnel_militaire p ON d.personnel_id = p.id
            LEFT JOIN grades g ON p.grade_id = g.id
            LEFT JOIN unites u ON p.unite_id = u.id
            LEFT JOIN sessions s ON d.session_id = s.id
            WHERE d.id = ?
        """, (distribution_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def list_distributions_with_details(self):
        """
        List all bungalow distributions with bungalow, personnel, and session details

        Returns:
            list: List of distributions with details
        """
        self.db_manager.execute("""
            SELECT d.*,
                   b.numero as bungalow_numero, b.endroit as bungalow_endroit, b.capacite as bungalow_capacite,
                   p.matricule as personnel_matricule, p.nom as personnel_nom, p.prenom as personnel_prenom,
                   p.numero as personnel_numero, p.unite_id, p.region as personnel_region,
                   g.grade as personnel_grade,
                   u.description as unite_description,
                   s.numero as session_numero, s.description as session_description, s.date_debut as session_date_debut, s.date_fin as session_date_fin
            FROM distribution_bungalows d
            LEFT JOIN bungalows b ON d.bungalow_id = b.id
            LEFT JOIN personnel_militaire p ON d.personnel_id = p.id
            LEFT JOIN grades g ON p.grade_id = g.id
            LEFT JOIN unites u ON p.unite_id = u.id
            LEFT JOIN sessions s ON d.session_id = s.id
        """)
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def update_distribution_bungalow(self, distribution_id, numero=None, bungalow_id=None, personnel_id=None,
                                    session_id=None, date_debut=None, date_fin=None, notes=None):
        """
        Update a bungalow distribution

        Args:
            distribution_id (int): Distribution ID
            numero (str, optional): New distribution number
            bungalow_id (int, optional): New bungalow ID
            personnel_id (int, optional): New personnel ID
            session_id (int, optional): New session ID
            date_debut (str, optional): New start date (YYYY-MM-DD format)
            date_fin (str, optional): New end date (YYYY-MM-DD format)
            notes (str, optional): New notes

        Returns:
            bool: True if successful
        """
        updates = []
        params = []

        if numero:
            updates.append("numero = ?")
            params.append(numero)

        if bungalow_id is not None:  # Allow setting bungalow_id to NULL (None)
            updates.append("bungalow_id = ?")
            params.append(bungalow_id)

        if personnel_id is not None:  # Allow setting personnel_id to NULL (None)
            updates.append("personnel_id = ?")
            params.append(personnel_id)

        if session_id is not None:  # Allow setting session_id to NULL (None)
            updates.append("session_id = ?")
            params.append(session_id)

        if date_debut is not None:  # Allow setting date_debut to NULL (None)
            updates.append("date_debut = ?")
            params.append(date_debut)

        if date_fin is not None:  # Allow setting date_fin to NULL (None)
            updates.append("date_fin = ?")
            params.append(date_fin)

        if notes is not None:  # Allow setting notes to NULL (None)
            updates.append("notes = ?")
            params.append(notes)

        if not updates:
            return False

        params.append(distribution_id)
        query = f"UPDATE distribution_bungalows SET {', '.join(updates)} WHERE id = ?"

        self.db_manager.execute(query, tuple(params))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def delete_distribution_bungalow(self, distribution_id):
        """
        Delete a bungalow distribution

        Args:
            distribution_id (int): Distribution ID

        Returns:
            bool: True if successful
        """
        self.db_manager.execute("DELETE FROM distribution_bungalows WHERE id = ?", (distribution_id,))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def check_personnel_has_active_distribution(self, personnel_id):
        """
        Check if personnel already has an active distribution

        Args:
            personnel_id (int): Personnel ID

        Returns:
            dict: Active distribution info if exists, None otherwise
        """
        if not personnel_id:
            return None

        self.db_manager.execute("""
            SELECT d.*, b.numero as bungalow_numero, s.numero as session_numero
            FROM distribution_bungalows d
            LEFT JOIN bungalows b ON d.bungalow_id = b.id
            LEFT JOIN sessions s ON d.session_id = s.id
            WHERE d.personnel_id = ?
            AND (d.date_fin IS NULL OR d.date_fin >= date('now'))
            ORDER BY d.date_debut DESC
            LIMIT 1
        """, (personnel_id,))

        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def list_distributions_with_details(self):
        """
        List all distributions with detailed information

        Returns:
            list: List of distributions with details
        """
        self.db_manager.execute("""
            SELECT d.*,
                   b.numero as bungalow_numero, b.endroit as bungalow_endroit, b.capacite as bungalow_capacite,
                   p.matricule as personnel_matricule, p.numero as personnel_numero, p.nom as personnel_nom, p.prenom as personnel_prenom, p.unite_id,
                   g.grade as personnel_grade,
                   u.description as unite_description,
                   s.numero as session_numero, s.description as session_description,
                   datetime('now') as created_at
            FROM distribution_bungalows d
            LEFT JOIN bungalows b ON d.bungalow_id = b.id
            LEFT JOIN personnel_militaire p ON d.personnel_id = p.id
            LEFT JOIN grades g ON p.grade_id = g.id
            LEFT JOIN unites u ON p.unite_id = u.id
            LEFT JOIN sessions s ON d.session_id = s.id
            ORDER BY d.id DESC
        """)
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    # Unites operations
    def create_unite(self, numero, description, raccourci=None):
        """
        Create a new unite

        Args:
            numero (str): Unite number
            description (str): Unite description
            raccourci (str, optional): Unite abbreviation

        Returns:
            int: ID of the created unite
        """
        try:
            self.db_manager.execute(
                "INSERT INTO unites (numero, description, raccourci) VALUES (?, ?, ?)",
                (numero, description, raccourci)
            )
            self.db_manager.commit()
            return self.db_manager.cursor.lastrowid
        except sqlite3.IntegrityError:
            print(f"Unite with numero '{numero}' already exists")
            return None

    def get_unite_by_id(self, unite_id):
        """
        Get a unite by ID

        Args:
            unite_id (int): Unite ID

        Returns:
            dict: Unite data
        """
        self.db_manager.execute("SELECT * FROM unites WHERE id = ?", (unite_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_unite_by_numero(self, numero):
        """
        Get a unite by numero

        Args:
            numero (str): Unite number

        Returns:
            dict: Unite data
        """
        self.db_manager.execute("SELECT * FROM unites WHERE numero = ?", (numero,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def update_unite(self, unite_id, numero=None, description=None, raccourci=None):
        """
        Update a unite

        Args:
            unite_id (int): Unite ID
            numero (str, optional): New unite number
            description (str, optional): New unite description
            raccourci (str, optional): New unite abbreviation

        Returns:
            bool: True if successful
        """
        updates = []
        params = []

        if numero:
            updates.append("numero = ?")
            params.append(numero)

        if description:
            updates.append("description = ?")
            params.append(description)

        if raccourci is not None:
            updates.append("raccourci = ?")
            params.append(raccourci)

        if not updates:
            return False

        params.append(unite_id)
        query = f"UPDATE unites SET {', '.join(updates)} WHERE id = ?"

        self.db_manager.execute(query, tuple(params))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def delete_unite(self, unite_id):
        """
        Delete a unite

        Args:
            unite_id (int): Unite ID

        Returns:
            bool: True if successful
        """
        # Check if unite is used by personnel
        self.db_manager.execute("SELECT COUNT(*) as count FROM personnel_militaire WHERE unite_id = ?", (unite_id,))
        result = self.db_manager.cursor.fetchone()

        if result['count'] > 0:
            return False, f"Cannot delete unite: {result['count']} personnel are assigned to this unite"

        self.db_manager.execute("DELETE FROM unites WHERE id = ?", (unite_id,))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0, "Unite deleted successfully"

    def list_unites_with_personnel_count(self):
        """
        List all unites with personnel count

        Returns:
            list: List of unites with personnel count
        """
        self.db_manager.execute("""
            SELECT u.*,
                   COUNT(p.id) as personnel_count
            FROM unites u
            LEFT JOIN personnel_militaire p ON u.id = p.unite_id
            GROUP BY u.id, u.numero, u.description, u.raccourci
            ORDER BY u.numero
        """)
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def import_unites_from_data(self, unites_data):
        """
        Import unites from data list

        Args:
            unites_data (list): List of unite dictionaries

        Returns:
            dict: Import results
        """
        results = {
            'success': 0,
            'errors': 0,
            'duplicates': 0,
            'messages': []
        }

        for unite_data in unites_data:
            try:
                # Handle NaN values properly
                numero_raw = unite_data.get('numero', '')
                description_raw = unite_data.get('description', '')
                raccourci_raw = unite_data.get('raccourci', '')

                # Convert to string and handle NaN
                numero = str(numero_raw).strip() if str(numero_raw) != 'nan' else ''
                description = str(description_raw).strip() if str(description_raw) != 'nan' else ''
                raccourci = str(raccourci_raw).strip() if str(raccourci_raw) != 'nan' and raccourci_raw else None

                if not numero or not description or numero == 'nan' or description == 'nan':
                    results['errors'] += 1
                    results['messages'].append(f"Ligne ignorée: numéro ou description manquant")
                    continue

                # Check if unite already exists
                existing = self.get_unite_by_numero(numero)
                if existing:
                    results['duplicates'] += 1
                    results['messages'].append(f"Unité {numero} existe déjà")
                    continue

                # Create new unite
                unite_id = self.create_unite(numero, description, raccourci)
                if unite_id:
                    results['success'] += 1
                    results['messages'].append(f"Unité {numero} créée avec succès")
                else:
                    results['errors'] += 1
                    results['messages'].append(f"Erreur lors de la création de l'unité {numero}")

            except Exception as e:
                results['errors'] += 1
                results['messages'].append(f"Erreur: {str(e)}")

        return results

    # Grades operations
    def create_grade(self, grade, description=None, niveau=None):
        """
        Create a new grade

        Args:
            grade (str): Grade name
            description (str, optional): Grade description
            niveau (int, optional): Grade level/hierarchy

        Returns:
            int: ID of the created grade
        """
        try:
            # Generate numero automatically
            numero = f"G{str(len(self.list_grades()) + 1).zfill(3)}"
            self.db_manager.execute(
                "INSERT INTO grades (numero, grade, description) VALUES (?, ?, ?)",
                (numero, grade, description)
            )
            self.db_manager.commit()
            return self.db_manager.cursor.lastrowid
        except sqlite3.IntegrityError:
            print(f"Grade '{grade}' already exists")
            return None

    def get_grade_by_id(self, grade_id):
        """
        Get a grade by ID

        Args:
            grade_id (int): Grade ID

        Returns:
            dict: Grade data
        """
        self.db_manager.execute("SELECT * FROM grades WHERE id = ?", (grade_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_grade_by_name(self, grade_name):
        """
        Get a grade by name

        Args:
            grade_name (str): Grade name

        Returns:
            dict: Grade data
        """
        self.db_manager.execute("SELECT * FROM grades WHERE grade = ?", (grade_name,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def update_grade(self, grade_id, grade=None, description=None, niveau=None):
        """
        Update a grade

        Args:
            grade_id (int): Grade ID
            grade (str, optional): New grade name
            description (str, optional): New grade description
            niveau (int, optional): New grade level (not used in current schema)

        Returns:
            bool: True if successful
        """
        updates = []
        params = []

        if grade:
            updates.append("grade = ?")
            params.append(grade)

        if description is not None:
            updates.append("description = ?")
            params.append(description)

        if not updates:
            return False

        params.append(grade_id)
        query = f"UPDATE grades SET {', '.join(updates)} WHERE id = ?"

        self.db_manager.execute(query, tuple(params))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def delete_grade(self, grade_id):
        """
        Delete a grade

        Args:
            grade_id (int): Grade ID

        Returns:
            tuple: (success, message)
        """
        # Check if grade is used by personnel
        self.db_manager.execute("SELECT COUNT(*) as count FROM personnel_militaire WHERE grade_id = ?", (grade_id,))
        result = self.db_manager.cursor.fetchone()

        if result['count'] > 0:
            return False, f"Cannot delete grade: {result['count']} personnel have this grade"

        self.db_manager.execute("DELETE FROM grades WHERE id = ?", (grade_id,))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0, "Grade deleted successfully"

    def list_grades_with_personnel_count(self):
        """
        List all grades with personnel count

        Returns:
            list: List of grades with personnel count
        """
        self.db_manager.execute("""
            SELECT g.*,
                   COUNT(p.id) as personnel_count
            FROM grades g
            LEFT JOIN personnel_militaire p ON g.id = p.grade_id
            GROUP BY g.id, g.numero, g.grade, g.description
            ORDER BY g.grade ASC
        """)
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def search_grades(self, search_term):
        """
        Search grades by name or description

        Args:
            search_term (str): Search term

        Returns:
            list: List of matching grades
        """
        search_pattern = f"%{search_term}%"
        self.db_manager.execute("""
            SELECT g.*,
                   COUNT(p.id) as personnel_count
            FROM grades g
            LEFT JOIN personnel_militaire p ON g.id = p.grade_id
            WHERE g.grade LIKE ? OR g.description LIKE ?
            GROUP BY g.id, g.numero, g.grade, g.description
            ORDER BY g.grade ASC
        """, (search_pattern, search_pattern))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]



    def import_grades_from_data(self, grades_data):
        """
        Import grades from data list

        Args:
            grades_data (list): List of grade dictionaries

        Returns:
            dict: Import results
        """
        results = {
            'success': 0,
            'errors': 0,
            'duplicates': 0,
            'messages': []
        }

        for grade_data in grades_data:
            try:
                grade = str(grade_data.get('grade', '')).strip()
                description = str(grade_data.get('description', '')).strip() if grade_data.get('description') else None

                if not grade:
                    results['errors'] += 1
                    results['messages'].append(f"Ligne ignorée: nom de grade manquant")
                    continue

                # Check if grade already exists
                existing = self.get_grade_by_name(grade)
                if existing:
                    results['duplicates'] += 1
                    results['messages'].append(f"Grade {grade} existe déjà")
                    continue

                # Create new grade
                grade_id = self.create_grade(grade, description)
                if grade_id:
                    results['success'] += 1
                    results['messages'].append(f"Grade {grade} créé avec succès")
                else:
                    results['errors'] += 1
                    results['messages'].append(f"Erreur lors de la création du grade {grade}")

            except Exception as e:
                results['errors'] += 1
                results['messages'].append(f"Erreur: {str(e)}")

        return results

    # Sessions operations
    def create_session(self, numero, description, date_debut, date_fin=None, etat_session=None):
        """Create a new session"""
        try:
            self.db_manager.execute(
                "INSERT INTO sessions (numero, description, date_debut, date_fin, etat_session) VALUES (?, ?, ?, ?, ?)",
                (numero, description, date_debut, date_fin, etat_session)
            )
            self.db_manager.commit()
            return self.db_manager.cursor.lastrowid
        except sqlite3.IntegrityError:
            print(f"Session '{numero}' already exists")
            return None

    def get_session_by_id(self, session_id):
        """Get a session by ID"""
        self.db_manager.execute("SELECT * FROM sessions WHERE id = ?", (session_id,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def get_session_by_numero(self, numero):
        """Get a session by numero"""
        self.db_manager.execute("SELECT * FROM sessions WHERE numero = ?", (numero,))
        row = self.db_manager.cursor.fetchone()
        return dict(row) if row else None

    def update_session(self, session_id, numero=None, description=None, date_debut=None, date_fin=None, etat_session=None):
        """Update a session"""
        updates = []
        params = []

        if numero:
            updates.append("numero = ?")
            params.append(numero)

        if description is not None:
            updates.append("description = ?")
            params.append(description)

        if date_debut:
            updates.append("date_debut = ?")
            params.append(date_debut)

        if date_fin is not None:
            updates.append("date_fin = ?")
            params.append(date_fin)

        if etat_session is not None:
            updates.append("etat_session = ?")
            params.append(etat_session)

        if not updates:
            return False

        params.append(session_id)
        query = f"UPDATE sessions SET {', '.join(updates)} WHERE id = ?"

        self.db_manager.execute(query, tuple(params))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0

    def delete_session(self, session_id):
        """Delete a session"""
        # Check if session is used in distributions
        self.db_manager.execute("SELECT COUNT(*) as count FROM distribution_bungalows WHERE session_id = ?", (session_id,))
        result = self.db_manager.cursor.fetchone()

        if result['count'] > 0:
            return False, f"Cannot delete session: {result['count']} distribution(s) use this session"

        self.db_manager.execute("DELETE FROM sessions WHERE id = ?", (session_id,))
        self.db_manager.commit()
        return self.db_manager.cursor.rowcount > 0, "Session deleted successfully"

    def list_sessions_with_distribution_count(self):
        """List all sessions with distribution count"""
        self.db_manager.execute("""
            SELECT s.*,
                   COUNT(d.id) as distribution_count
            FROM sessions s
            LEFT JOIN distribution_bungalows d ON s.id = d.session_id
            GROUP BY s.id, s.numero, s.description, s.date_debut, s.date_fin, s.etat_session
            ORDER BY s.date_debut DESC, s.numero ASC
        """)
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def search_sessions(self, search_term):
        """Search sessions by numero, description, or status"""
        search_pattern = f"%{search_term}%"
        self.db_manager.execute("""
            SELECT s.*,
                   COUNT(d.id) as distribution_count
            FROM sessions s
            LEFT JOIN distribution_bungalows d ON s.id = d.session_id
            WHERE s.numero LIKE ? OR s.description LIKE ? OR s.etat_session LIKE ?
            GROUP BY s.id, s.numero, s.description, s.date_debut, s.date_fin, s.etat_session
            ORDER BY s.date_debut DESC, s.numero ASC
        """, (search_pattern, search_pattern, search_pattern))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    def get_sessions_by_status(self, status):
        """Get sessions by status"""
        self.db_manager.execute("""
            SELECT s.*,
                   COUNT(d.id) as distribution_count
            FROM sessions s
            LEFT JOIN distribution_bungalows d ON s.id = d.session_id
            WHERE s.etat_session = ?
            GROUP BY s.id, s.numero, s.description, s.date_debut, s.date_fin, s.etat_session
            ORDER BY s.date_debut DESC, s.numero ASC
        """, (status,))
        return [dict(row) for row in self.db_manager.cursor.fetchall()]

    # Bungalow-Session relationship methods
    def create_bungalow_session_link(self, bungalow_id, session_id, statut='active', notes=None):
        """Create a direct link between bungalow and session"""
        try:
            self.db_manager.execute('''
            INSERT INTO bungalow_sessions (bungalow_id, session_id, statut, notes)
            VALUES (?, ?, ?, ?)
            ''', (bungalow_id, session_id, statut, notes))
            self.db_manager.commit()
            return self.db_manager.cursor.lastrowid
        except Exception as e:
            print(f"Error creating bungalow-session link: {e}")
            return None

    def list_bungalow_session_links(self):
        """List all bungalow-session links"""
        try:
            result = self.db_manager.execute('''
            SELECT
                bs.id,
                bs.bungalow_id,
                bs.session_id,
                bs.statut,
                bs.notes,
                bs.date_assignation,
                b.numero as bungalow_numero,
                b.endroit as bungalow_endroit,
                b.capacite as bungalow_capacite,
                s.numero as session_numero,
                s.description as session_description,
                s.date_debut as session_date_debut,
                s.date_fin as session_date_fin
            FROM bungalow_sessions bs
            JOIN bungalows b ON bs.bungalow_id = b.id
            JOIN sessions s ON bs.session_id = s.id
            ORDER BY bs.date_assignation DESC
            ''')
            return [dict(row) for row in result.fetchall()]
        except Exception as e:
            print(f"Error listing bungalow-session links: {e}")
            return []

    def get_bungalows_by_session_direct(self, session_id):
        """Get all bungalows linked to a specific session (direct link)"""
        try:
            result = self.db_manager.execute('''
            SELECT DISTINCT
                b.id,
                b.numero,
                b.endroit,
                b.capacite,
                b.caracteristiques,
                bs.statut as link_status,
                bs.notes as link_notes,
                bs.date_assignation
            FROM bungalows b
            LEFT JOIN bungalow_sessions bs ON b.id = bs.bungalow_id AND bs.session_id = ?
            LEFT JOIN distribution_bungalows d ON b.id = d.bungalow_id AND d.session_id = ?
            WHERE bs.bungalow_id IS NOT NULL OR d.bungalow_id IS NOT NULL
            ORDER BY b.numero
            ''', (session_id, session_id))
            return [dict(row) for row in result.fetchall()]
        except Exception as e:
            print(f"Error getting bungalows by session: {e}")
            return []

    def get_sessions_by_bungalow_direct(self, bungalow_id):
        """Get all sessions linked to a specific bungalow (direct link)"""
        try:
            result = self.db_manager.execute('''
            SELECT DISTINCT
                s.id,
                s.numero,
                s.description,
                s.date_debut,
                s.date_fin,
                s.etat_session,
                bs.statut as link_status,
                bs.notes as link_notes,
                bs.date_assignation
            FROM sessions s
            LEFT JOIN bungalow_sessions bs ON s.id = bs.session_id AND bs.bungalow_id = ?
            LEFT JOIN distribution_bungalows d ON s.id = d.session_id AND d.bungalow_id = ?
            WHERE bs.session_id IS NOT NULL OR d.session_id IS NOT NULL
            ORDER BY s.date_debut DESC
            ''', (bungalow_id, bungalow_id))
            return [dict(row) for row in result.fetchall()]
        except Exception as e:
            print(f"Error getting sessions by bungalow: {e}")
            return []

    def update_bungalow_session_link(self, link_id, statut=None, notes=None):
        """Update a bungalow-session link"""
        try:
            updates = []
            params = []

            if statut is not None:
                updates.append("statut = ?")
                params.append(statut)

            if notes is not None:
                updates.append("notes = ?")
                params.append(notes)

            if not updates:
                return False

            params.append(link_id)
            query = f"UPDATE bungalow_sessions SET {', '.join(updates)} WHERE id = ?"

            self.db_manager.execute(query, params)
            self.db_manager.commit()
            return True
        except Exception as e:
            print(f"Error updating bungalow-session link: {e}")
            return False

    def delete_bungalow_session_link(self, link_id):
        """Delete a bungalow-session link"""
        try:
            self.db_manager.execute("DELETE FROM bungalow_sessions WHERE id = ?", (link_id,))
            self.db_manager.commit()
            return True
        except Exception as e:
            print(f"Error deleting bungalow-session link: {e}")
            return False

    def assign_bungalow_to_session(self, bungalow_id, session_id):
        """Assign a bungalow to a session (update bungalow table)"""
        try:
            self.db_manager.execute('''
            UPDATE bungalows
            SET session_id = ?, session_assignation_date = CURRENT_DATE
            WHERE id = ?
            ''', (session_id, bungalow_id))
            self.db_manager.commit()
            return True
        except Exception as e:
            print(f"Error assigning bungalow to session: {e}")
            return False

    def get_bungalow_current_session(self, bungalow_id):
        """Get current session associated with a bungalow"""
        try:
            # Check bungalow_sessions table first
            self.db_manager.execute('''
            SELECT s.id, s.numero, s.description, s.date_debut, s.date_fin, s.etat_session,
                   bs.statut, bs.date_assignation, bs.notes
            FROM bungalow_sessions bs
            JOIN sessions s ON bs.session_id = s.id
            WHERE bs.bungalow_id = ? AND bs.statut = 'active'
            ORDER BY bs.date_assignation DESC
            LIMIT 1
            ''', (bungalow_id,))
            row = self.db_manager.cursor.fetchone()
            if row:
                return dict(row)

            # Fallback: check bungalows table for session_id
            self.db_manager.execute('''
            SELECT s.id, s.numero, s.description, s.date_debut, s.date_fin, s.etat_session
            FROM bungalows b
            JOIN sessions s ON b.session_id = s.id
            WHERE b.id = ?
            ''', (bungalow_id,))
            row = self.db_manager.cursor.fetchone()
            return dict(row) if row else None
        except Exception as e:
            print(f"Error getting bungalow current session: {e}")
            return None

    def remove_bungalow_session_link(self, bungalow_id, session_id):
        """Remove a specific bungalow-session link"""
        try:
            # Remove from bungalow_sessions table
            self.db_manager.execute('''
            DELETE FROM bungalow_sessions
            WHERE bungalow_id = ? AND session_id = ?
            ''', (bungalow_id, session_id))

            # Also clear session_id from bungalows table if it matches
            self.db_manager.execute('''
            UPDATE bungalows
            SET session_id = NULL, session_assignation_date = NULL
            WHERE id = ? AND session_id = ?
            ''', (bungalow_id, session_id))

            self.db_manager.commit()
            return True
        except Exception as e:
            print(f"Error removing bungalow-session link: {e}")
            return False

    def unassign_bungalow_from_session(self, bungalow_id):
        """Remove session assignment from bungalow"""
        try:
            self.db_manager.execute('''
            UPDATE bungalows
            SET session_id = NULL, session_assignation_date = NULL
            WHERE id = ?
            ''', (bungalow_id,))
            self.db_manager.commit()
            return True
        except Exception as e:
            print(f"Error unassigning bungalow from session: {e}")
            return False

    def get_bungalows_with_session_info(self):
        """Get all bungalows with their session information"""
        try:
            result = self.db_manager.execute('''
            SELECT
                b.*,
                s.id as assigned_session_id,
                s.numero as assigned_session_numero,
                s.description as assigned_session_description,
                s.date_debut as assigned_session_date_debut,
                s.date_fin as assigned_session_date_fin,
                b.session_assignation_date
            FROM bungalows b
            LEFT JOIN sessions s ON b.session_id = s.id
            ORDER BY b.numero
            ''')
            return [dict(row) for row in result.fetchall()]
        except Exception as e:
            print(f"Error getting bungalows with session info: {e}")
            return []

    def get_sessions_with_bungalow_count(self):
        """Get all sessions with count of assigned bungalows"""
        try:
            result = self.db_manager.execute('''
            SELECT
                s.*,
                COUNT(DISTINCT b.id) as direct_bungalows_count,
                COUNT(DISTINCT d.bungalow_id) as distribution_bungalows_count,
                COUNT(DISTINCT COALESCE(b.id, d.bungalow_id)) as total_bungalows_count
            FROM sessions s
            LEFT JOIN bungalows b ON s.id = b.session_id
            LEFT JOIN distribution_bungalows d ON s.id = d.session_id
            GROUP BY s.id, s.numero, s.description, s.date_debut, s.date_fin, s.etat_session
            ORDER BY s.date_debut DESC
            ''')
            return [dict(row) for row in result.fetchall()]
        except Exception as e:
            print(f"Error getting sessions with bungalow count: {e}")
            return []

    # User Management Methods
    def get_all_users(self):
        """
        Get all users from the database

        Returns:
            list: List of all users
        """
        try:
            self.db_manager.execute("""
                SELECT id, username, email, role, is_active, created_at, last_login, full_name
                FROM users
                ORDER BY created_at DESC
            """)
            return [dict(row) for row in self.db_manager.cursor.fetchall()]
        except Exception as e:
            print(f"Error getting all users: {e}")
            return []

    def get_user_by_id(self, user_id):
        """
        Get a user by ID

        Args:
            user_id (int): User ID

        Returns:
            dict: User data or None
        """
        try:
            self.db_manager.execute("""
                SELECT id, username, email, password_hash, role, is_active, created_at, last_login, full_name
                FROM users
                WHERE id = ?
            """, (user_id,))
            row = self.db_manager.cursor.fetchone()
            return dict(row) if row else None
        except Exception as e:
            print(f"Error getting user by ID: {e}")
            return None

    def get_user_by_username(self, username):
        """
        Get a user by username

        Args:
            username (str): Username

        Returns:
            dict: User data or None
        """
        try:
            self.db_manager.execute("""
                SELECT id, username, email, password_hash, role, is_active, created_at, last_login, full_name
                FROM users
                WHERE username = ?
            """, (username,))
            row = self.db_manager.cursor.fetchone()
            return dict(row) if row else None
        except Exception as e:
            print(f"Error getting user by username: {e}")
            return None

    def create_user(self, user_data):
        """
        Create a new user

        Args:
            user_data (dict): User data including username, password, email, etc.

        Returns:
            int: User ID if successful, None otherwise
        """
        try:
            self.db_manager.execute("""
                INSERT INTO users (username, password_hash, email, role, is_active, created_at, full_name)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                user_data['username'],
                user_data['password'],
                user_data.get('email', ''),
                user_data.get('role', 'user'),
                user_data.get('is_active', True),
                user_data.get('created_at', datetime.now()),
                user_data.get('full_name', '')
            ))
            self.db_manager.commit()
            return self.db_manager.cursor.lastrowid
        except Exception as e:
            print(f"Error creating user: {e}")
            return None

    def update_user(self, user_id, update_data):
        """
        Update a user

        Args:
            user_id (int): User ID
            update_data (dict): Data to update

        Returns:
            bool: True if successful
        """
        try:
            # Build dynamic update query
            updates = []
            params = []

            for field, value in update_data.items():
                if field in ['username', 'email', 'role', 'is_active', 'full_name', 'password']:
                    if field == 'password':
                        updates.append("password_hash = ?")
                    else:
                        updates.append(f"{field} = ?")
                    params.append(value)

            if not updates:
                return False

            params.append(user_id)
            query = f"UPDATE users SET {', '.join(updates)} WHERE id = ?"

            self.db_manager.execute(query, tuple(params))
            self.db_manager.commit()
            return self.db_manager.cursor.rowcount > 0
        except Exception as e:
            print(f"Error updating user: {e}")
            return False

    def delete_user(self, user_id):
        """
        Delete a user

        Args:
            user_id (int): User ID

        Returns:
            bool: True if successful
        """
        try:
            self.db_manager.execute("DELETE FROM users WHERE id = ?", (user_id,))
            self.db_manager.commit()
            return self.db_manager.cursor.rowcount > 0
        except Exception as e:
            print(f"Error deleting user: {e}")
            return False

    def update_user_last_login(self, user_id):
        """
        Update user's last login timestamp

        Args:
            user_id (int): User ID

        Returns:
            bool: True if successful
        """
        try:
            self.db_manager.execute(
                "UPDATE users SET last_login = ? WHERE id = ?",
                (datetime.now(), user_id)
            )
            self.db_manager.commit()
            return True
        except Exception as e:
            print(f"Error updating last login: {e}")
            return False

    def get_dashboard_statistics(self):
        """
        Get all dashboard statistics in optimized queries (Performance Optimization)

        Returns:
            dict: Dictionary containing all dashboard statistics
        """
        try:
            # Get basic counts in a single query
            self.db_manager.execute("""
                SELECT
                    (SELECT COUNT(*) FROM bungalows) as bungalow_count,
                    (SELECT COUNT(*) FROM personnel_militaire) as personnel_count,
                    (SELECT COUNT(*) FROM distribution_bungalows) as distribution_count,
                    (SELECT COUNT(*) FROM grades) as grades_count,
                    (SELECT COUNT(*) FROM unites) as unites_count,
                    (SELECT COUNT(*) FROM sessions) as sessions_count,
                    (SELECT COUNT(*) FROM users) as users_count
            """)

            result = self.db_manager.cursor.fetchone()
            stats = dict(result) if result else {}

            # Get occupancy statistics
            self.db_manager.execute("""
                SELECT
                    COUNT(*) as total_bungalows,
                    SUM(CASE
                        WHEN EXISTS (
                            SELECT 1 FROM distribution_bungalows d
                            WHERE d.bungalow_id = b.id
                            AND d.date_debut <= date('now')
                            AND (d.date_fin IS NULL OR d.date_fin >= date('now'))
                        ) THEN 1 ELSE 0
                    END) as occupied_count
                FROM bungalows b
            """)

            occupancy_result = self.db_manager.cursor.fetchone()
            if occupancy_result:
                occupancy_data = dict(occupancy_result)
                total = occupancy_data.get('total_bungalows', 0)
                occupied = occupancy_data.get('occupied_count', 0)
                stats['occupancy_rate'] = (occupied / total * 100) if total > 0 else 0
            else:
                stats['occupancy_rate'] = 0

            return stats

        except Exception as e:
            print(f"Error getting dashboard statistics: {e}")
            return {
                'bungalow_count': 0,
                'personnel_count': 0,
                'distribution_count': 0,
                'grades_count': 0,
                'unites_count': 0,
                'sessions_count': 0,
                'users_count': 0,
                'occupancy_rate': 0
            }

    def get_recent_distributions_optimized(self, limit=10):
        """
        Get recent distributions with details in a single optimized query

        Args:
            limit (int): Number of recent distributions to return

        Returns:
            list: List of recent distributions with details
        """
        try:
            self.db_manager.execute("""
                SELECT d.*,
                       b.numero as bungalow_numero, b.endroit as bungalow_endroit, b.capacite as bungalow_capacite,
                       p.matricule as personnel_matricule, p.nom as personnel_nom, p.prenom as personnel_prenom,
                       p.numero as personnel_numero,
                       g.grade as personnel_grade,
                       u.description as unite_description,
                       s.numero as session_numero, s.description as session_description,
                       s.date_debut as session_date_debut, s.date_fin as session_date_fin
                FROM distribution_bungalows d
                LEFT JOIN bungalows b ON d.bungalow_id = b.id
                LEFT JOIN personnel_militaire p ON d.personnel_id = p.id
                LEFT JOIN grades g ON p.grade_id = g.id
                LEFT JOIN unites u ON p.unite_id = u.id
                LEFT JOIN sessions s ON d.session_id = s.id
                ORDER BY d.id DESC
                LIMIT ?
            """, (limit,))

            return [dict(row) for row in self.db_manager.cursor.fetchall()]

        except Exception as e:
            print(f"Error getting recent distributions: {e}")
            return []

    def search_bungalows_optimized(self, search_query):
        """
        Search bungalows with optimized SQL query (Performance Optimization)

        Args:
            search_query (str): Search term

        Returns:
            list: List of matching bungalows with session information
        """
        try:
            if not search_query or not search_query.strip():
                return self.list_bungalows_with_sessions()

            search_term = f"%{search_query.strip()}%"

            self.db_manager.execute('''
                SELECT b.*,
                       s.numero as session_numero,
                       s.description as session_description,
                       s.date_debut as session_date_debut,
                       s.date_fin as session_date_fin,
                       s.etat_session as session_etat,
                       bs.statut as session_statut,
                       bs.date_assignation as session_date_assignation
                FROM bungalows b
                LEFT JOIN bungalow_sessions bs ON b.id = bs.bungalow_id AND bs.statut = 'active'
                LEFT JOIN sessions s ON bs.session_id = s.id
                WHERE LOWER(b.numero) LIKE LOWER(?)
                   OR LOWER(b.endroit) LIKE LOWER(?)
                   OR LOWER(b.caracteristiques) LIKE LOWER(?)
                   OR LOWER(b.numero) LIKE LOWER(?)
                ORDER BY b.numero
            ''', (search_term, search_term, search_term, f"{search_query.strip()}%"))

            return [dict(row) for row in self.db_manager.cursor.fetchall()]

        except Exception as e:
            print(f"Error searching bungalows: {e}")
            return []

    def search_personnel_optimized(self, search_query):
        """
        Search personnel with optimized SQL query (Performance Optimization)

        Args:
            search_query (str): Search term

        Returns:
            list: List of matching personnel with grade and unit information
        """
        try:
            if not search_query or not search_query.strip():
                return self.list_personnel_with_grades_and_unites()

            search_term = f"%{search_query.strip()}%"

            self.db_manager.execute('''
                SELECT p.*,
                       g.grade as grade_name,
                       u.description as unite_description,
                       u.raccourci as unite_raccourci
                FROM personnel_militaire p
                LEFT JOIN grades g ON p.grade_id = g.id
                LEFT JOIN unites u ON p.unite_id = u.id
                WHERE LOWER(p.matricule) LIKE LOWER(?)
                   OR LOWER(p.nom) LIKE LOWER(?)
                   OR LOWER(p.prenom) LIKE LOWER(?)
                   OR LOWER(p.numero) LIKE LOWER(?)
                   OR LOWER(g.grade) LIKE LOWER(?)
                   OR LOWER(u.description) LIKE LOWER(?)
                ORDER BY p.nom, p.prenom
            ''', (search_term, search_term, search_term, search_term, search_term, search_term))

            return [dict(row) for row in self.db_manager.cursor.fetchall()]

        except Exception as e:
            print(f"Error searching personnel: {e}")
            return []