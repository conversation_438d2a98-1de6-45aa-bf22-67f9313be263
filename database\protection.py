#!/usr/bin/env python3
"""
Advanced Database Protection System for BANGHALAU
Provides real-time monitoring, access control, and threat detection
"""

import os
import sqlite3
import threading
import time
import json
import hashlib
from datetime import datetime, timedelta
from collections import defaultdict, deque
from pathlib import Path
import logging

class DatabaseProtection:
    """
    Advanced database protection with real-time monitoring
    """
    
    def __init__(self, db_path="banghalau.db"):
        self.db_path = db_path
        self.protection_log = "protection.log"
        self.access_log = "access.log"
        self.threat_log = "threats.log"
        
        # Security monitoring
        self.failed_attempts = defaultdict(int)
        self.access_patterns = defaultdict(deque)
        self.suspicious_queries = []
        self.blocked_ips = set()
        
        # Protection settings
        self.max_failed_attempts = 5
        self.lockout_duration = 300  # 5 minutes
        self.max_queries_per_minute = 100
        self.suspicious_keywords = [
            'DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'INSERT',
            'UPDATE', 'UNION', 'SELECT *', '--', ';--', '/*', '*/'
        ]
        
        self.setup_logging()
        self.start_monitoring()
    
    def setup_logging(self):
        """Setup protection logging"""
        # Protection logger
        self.protection_logger = logging.getLogger('DatabaseProtection')
        protection_handler = logging.FileHandler(self.protection_log)
        protection_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        )
        self.protection_logger.addHandler(protection_handler)
        self.protection_logger.setLevel(logging.INFO)
        
        # Access logger
        self.access_logger = logging.getLogger('DatabaseAccess')
        access_handler = logging.FileHandler(self.access_log)
        access_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(message)s')
        )
        self.access_logger.addHandler(access_handler)
        self.access_logger.setLevel(logging.INFO)
        
        # Threat logger
        self.threat_logger = logging.getLogger('DatabaseThreats')
        threat_handler = logging.FileHandler(self.threat_log)
        threat_handler.setFormatter(
            logging.Formatter('%(asctime)s - THREAT - %(message)s')
        )
        self.threat_logger.addHandler(threat_handler)
        self.threat_logger.setLevel(logging.WARNING)
    
    def start_monitoring(self):
        """Start background monitoring thread"""
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.protection_logger.info("Database protection monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring"""
        self.monitoring_active = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=5)
        self.protection_logger.info("Database protection monitoring stopped")
    
    def _monitor_loop(self):
        """Background monitoring loop"""
        while self.monitoring_active:
            try:
                self._check_database_health()
                self._cleanup_old_logs()
                self._analyze_access_patterns()
                time.sleep(60)  # Check every minute
            except Exception as e:
                self.protection_logger.error(f"Monitoring error: {e}")
    
    def _check_database_health(self):
        """Check database health and integrity"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=5)
            cursor = conn.cursor()
            
            # Quick integrity check
            cursor.execute("PRAGMA quick_check")
            result = cursor.fetchone()[0]
            
            if result != "ok":
                self.threat_logger.warning(f"Database integrity issue detected: {result}")
            
            # Check database size
            db_size = os.path.getsize(self.db_path)
            if db_size > 100 * 1024 * 1024:  # 100MB
                self.protection_logger.warning(f"Database size is large: {db_size / 1024 / 1024:.2f}MB")
            
            conn.close()
            
        except Exception as e:
            self.threat_logger.error(f"Database health check failed: {e}")
    
    def _cleanup_old_logs(self):
        """Clean up old log entries"""
        cutoff_time = datetime.now() - timedelta(days=30)
        
        # Clean up failed attempts older than lockout duration
        current_time = time.time()
        for ip in list(self.failed_attempts.keys()):
            if current_time - self.failed_attempts[ip] > self.lockout_duration:
                del self.failed_attempts[ip]
        
        # Clean up access patterns older than 1 hour
        for ip in self.access_patterns:
            while (self.access_patterns[ip] and 
                   current_time - self.access_patterns[ip][0] > 3600):
                self.access_patterns[ip].popleft()
    
    def _analyze_access_patterns(self):
        """Analyze access patterns for suspicious activity"""
        current_time = time.time()
        
        for ip, accesses in self.access_patterns.items():
            # Check for too many queries in short time
            recent_accesses = [t for t in accesses if current_time - t < 60]
            if len(recent_accesses) > self.max_queries_per_minute:
                self.threat_logger.warning(
                    f"Suspicious activity: {len(recent_accesses)} queries in 1 minute from {ip}"
                )
                self.blocked_ips.add(ip)
    
    def log_access(self, user_id=None, query=None, ip_address="localhost", success=True):
        """Log database access"""
        access_info = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "ip_address": ip_address,
            "success": success,
            "query_type": self._get_query_type(query) if query else "unknown"
        }
        
        self.access_logger.info(json.dumps(access_info))
        
        # Track access patterns
        current_time = time.time()
        self.access_patterns[ip_address].append(current_time)
        
        # Check for suspicious queries
        if query and self._is_suspicious_query(query):
            self.threat_logger.warning(
                f"Suspicious query from {ip_address}: {query[:100]}..."
            )
    
    def log_failed_attempt(self, user_id=None, ip_address="localhost", reason=""):
        """Log failed access attempt"""
        self.failed_attempts[ip_address] += 1
        
        threat_info = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "ip_address": ip_address,
            "failed_attempts": self.failed_attempts[ip_address],
            "reason": reason
        }
        
        self.threat_logger.warning(json.dumps(threat_info))
        
        # Block IP if too many failed attempts
        if self.failed_attempts[ip_address] >= self.max_failed_attempts:
            self.blocked_ips.add(ip_address)
            self.threat_logger.error(f"IP {ip_address} blocked due to {self.failed_attempts[ip_address]} failed attempts")
    
    def is_ip_blocked(self, ip_address):
        """Check if IP is blocked"""
        return ip_address in self.blocked_ips
    
    def unblock_ip(self, ip_address):
        """Unblock an IP address"""
        if ip_address in self.blocked_ips:
            self.blocked_ips.remove(ip_address)
            self.failed_attempts[ip_address] = 0
            self.protection_logger.info(f"IP {ip_address} unblocked")
            return True
        return False
    
    def _get_query_type(self, query):
        """Get query type from SQL query"""
        if not query:
            return "unknown"
        
        query_upper = query.strip().upper()
        if query_upper.startswith('SELECT'):
            return "SELECT"
        elif query_upper.startswith('INSERT'):
            return "INSERT"
        elif query_upper.startswith('UPDATE'):
            return "UPDATE"
        elif query_upper.startswith('DELETE'):
            return "DELETE"
        elif query_upper.startswith('CREATE'):
            return "CREATE"
        elif query_upper.startswith('DROP'):
            return "DROP"
        elif query_upper.startswith('ALTER'):
            return "ALTER"
        else:
            return "OTHER"
    
    def _is_suspicious_query(self, query):
        """Check if query contains suspicious patterns"""
        if not query:
            return False
        
        query_upper = query.upper()
        
        # Check for suspicious keywords
        for keyword in self.suspicious_keywords:
            if keyword in query_upper:
                return True
        
        # Check for SQL injection patterns
        injection_patterns = [
            "' OR '1'='1",
            "' OR 1=1",
            "'; DROP TABLE",
            "UNION SELECT",
            "' UNION SELECT",
            "/*",
            "*/",
            "--",
            "xp_",
            "sp_"
        ]
        
        for pattern in injection_patterns:
            if pattern.upper() in query_upper:
                return True
        
        return False
    
    def create_database_snapshot(self):
        """Create a snapshot of current database state"""
        try:
            snapshot_dir = "snapshots"
            os.makedirs(snapshot_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            snapshot_path = os.path.join(snapshot_dir, f"snapshot_{timestamp}.db")
            
            # Copy database file
            with open(self.db_path, 'rb') as src:
                with open(snapshot_path, 'wb') as dst:
                    dst.write(src.read())
            
            # Create metadata
            metadata = {
                "timestamp": datetime.now().isoformat(),
                "original_size": os.path.getsize(self.db_path),
                "snapshot_path": snapshot_path,
                "checksum": self._calculate_file_checksum(self.db_path)
            }
            
            metadata_path = os.path.join(snapshot_dir, f"snapshot_{timestamp}.json")
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=4)
            
            self.protection_logger.info(f"Database snapshot created: {snapshot_path}")
            return True, snapshot_path
            
        except Exception as e:
            self.protection_logger.error(f"Failed to create snapshot: {e}")
            return False, str(e)
    
    def _calculate_file_checksum(self, file_path):
        """Calculate SHA256 checksum of file"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def verify_database_integrity(self):
        """Verify database integrity with detailed checks"""
        issues = []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Full integrity check
            cursor.execute("PRAGMA integrity_check")
            results = cursor.fetchall()
            for result in results:
                if result[0] != "ok":
                    issues.append(f"Integrity issue: {result[0]}")
            
            # Foreign key check
            cursor.execute("PRAGMA foreign_key_check")
            fk_results = cursor.fetchall()
            if fk_results:
                issues.append(f"Foreign key violations: {len(fk_results)}")
            
            # Check for orphaned records
            tables = ['personnel_militaire', 'bungalows', 'sessions', 'distribution_bungalows']
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    if count == 0:
                        issues.append(f"Table {table} is empty")
                except sqlite3.OperationalError:
                    pass  # Table might not exist
            
            conn.close()
            
        except Exception as e:
            issues.append(f"Database verification error: {str(e)}")
        
        return issues
    
    def get_protection_status(self):
        """Get current protection status"""
        return {
            "monitoring_active": self.monitoring_active,
            "blocked_ips": list(self.blocked_ips),
            "failed_attempts": dict(self.failed_attempts),
            "database_size": os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
            "last_check": datetime.now().isoformat(),
            "integrity_issues": self.verify_database_integrity()
        }
    
    def generate_security_report(self):
        """Generate comprehensive security report"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "protection_status": self.get_protection_status(),
            "recent_threats": self._get_recent_threats(),
            "access_summary": self._get_access_summary(),
            "recommendations": self._get_security_recommendations()
        }
        
        return report
    
    def _get_recent_threats(self):
        """Get recent threat events"""
        threats = []
        try:
            if os.path.exists(self.threat_log):
                with open(self.threat_log, 'r') as f:
                    lines = f.readlines()
                    # Get last 10 threat entries
                    for line in lines[-10:]:
                        if 'THREAT' in line:
                            threats.append(line.strip())
        except Exception as e:
            self.protection_logger.error(f"Error reading threat log: {e}")
        
        return threats
    
    def _get_access_summary(self):
        """Get access summary statistics"""
        summary = {
            "total_accesses": 0,
            "unique_ips": len(self.access_patterns),
            "blocked_ips": len(self.blocked_ips),
            "failed_attempts": sum(self.failed_attempts.values())
        }
        
        return summary
    
    def _get_security_recommendations(self):
        """Get security recommendations"""
        recommendations = []
        
        if len(self.blocked_ips) > 0:
            recommendations.append("مراجعة عناوين IP المحظورة وإلغاء حظر العناوين الآمنة")
        
        if sum(self.failed_attempts.values()) > 10:
            recommendations.append("مراجعة محاولات تسجيل الدخول الفاشلة")
        
        if os.path.getsize(self.db_path) > 50 * 1024 * 1024:  # 50MB
            recommendations.append("النظر في ضغط أو أرشفة قاعدة البيانات")
        
        # Check if backups exist
        backup_files = [f for f in os.listdir('.') if f.startswith('banghalau') and f.endswith('.backup')]
        if len(backup_files) == 0:
            recommendations.append("إنشاء نسخة احتياطية من قاعدة البيانات")
        
        return recommendations
