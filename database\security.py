#!/usr/bin/env python3
"""
Database Security Module for BANGHALAU
Provides comprehensive database protection and security features
"""

import os
import hashlib
import secrets
import sqlite3
import json
import logging
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
from pathlib import Path

class DatabaseSecurity:
    """
    Comprehensive database security manager
    """
    
    def __init__(self, db_path="banghalau.db"):
        self.db_path = db_path
        self.security_config_path = "security_config.json"
        self.audit_log_path = "database_audit.log"
        self.backup_encryption_key = None
        self.setup_logging()
        self.load_or_create_security_config()
    
    def setup_logging(self):
        """Setup security audit logging"""
        logging.basicConfig(
            filename=self.audit_log_path,
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self.logger = logging.getLogger('DatabaseSecurity')
    
    def load_or_create_security_config(self):
        """Load or create security configuration"""
        if os.path.exists(self.security_config_path):
            with open(self.security_config_path, 'r') as f:
                self.config = json.load(f)
        else:
            self.config = self.create_default_security_config()
            self.save_security_config()
    
    def create_default_security_config(self):
        """Create default security configuration"""
        return {
            "encryption_enabled": True,
            "backup_encryption": True,
            "audit_logging": True,
            "max_login_attempts": 5,
            "session_timeout": 3600,  # 1 hour
            "password_policy": {
                "min_length": 12,
                "require_uppercase": True,
                "require_lowercase": True,
                "require_numbers": True,
                "require_special": True
            },
            "database_permissions": {
                "admin": ["read", "write", "delete", "backup", "restore"],
                "user": ["read", "write"],
                "viewer": ["read"]
            },
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat()
        }
    
    def save_security_config(self):
        """Save security configuration"""
        self.config["last_updated"] = datetime.now().isoformat()
        with open(self.security_config_path, 'w') as f:
            json.dump(self.config, f, indent=4)
    
    def generate_encryption_key(self, password: str, salt: bytes = None) -> bytes:
        """Generate encryption key from password"""
        if salt is None:
            salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key, salt
    
    def encrypt_data(self, data: str, key: bytes) -> str:
        """Encrypt sensitive data"""
        f = Fernet(key)
        encrypted_data = f.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_data(self, encrypted_data: str, key: bytes) -> str:
        """Decrypt sensitive data"""
        f = Fernet(key)
        decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = f.decrypt(decoded_data)
        return decrypted_data.decode()
    
    def validate_password_policy(self, password: str) -> tuple:
        """Validate password against security policy"""
        policy = self.config["password_policy"]
        errors = []
        
        if len(password) < policy["min_length"]:
            errors.append(f"كلمة المرور يجب أن تكون {policy['min_length']} أحرف على الأقل")
        
        if policy["require_uppercase"] and not any(c.isupper() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل")
        
        if policy["require_lowercase"] and not any(c.islower() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل")
        
        if policy["require_numbers"] and not any(c.isdigit() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل")
        
        if policy["require_special"] and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل")
        
        return len(errors) == 0, errors
    
    def generate_secure_password(self, length: int = 16) -> str:
        """Generate a secure password"""
        import string
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(characters) for _ in range(length))
        return password
    
    def hash_password_secure(self, password: str) -> str:
        """Create secure password hash with salt"""
        salt = secrets.token_hex(32)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}${password_hash.hex()}"
    
    def verify_password_secure(self, password: str, stored_hash: str) -> bool:
        """Verify password against secure hash"""
        try:
            salt, hash_hex = stored_hash.split('$')
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash.hex() == hash_hex
        except:
            return False
    
    def log_security_event(self, event_type: str, user_id: str = None, details: str = None):
        """Log security events for audit"""
        event = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "user_id": user_id,
            "details": details,
            "ip_address": "localhost"  # Can be enhanced to get real IP
        }
        self.logger.info(json.dumps(event))
    
    def create_secure_backup(self, backup_path: str, encryption_password: str = None):
        """Create encrypted database backup"""
        try:
            # Create backup directory if it doesn't exist
            backup_dir = os.path.dirname(backup_path)
            os.makedirs(backup_dir, exist_ok=True)
            
            # Read database file
            with open(self.db_path, 'rb') as db_file:
                db_data = db_file.read()
            
            if encryption_password and self.config["backup_encryption"]:
                # Encrypt backup
                key, salt = self.generate_encryption_key(encryption_password)
                encrypted_data = self.encrypt_data(base64.b64encode(db_data).decode(), key)
                
                backup_data = {
                    "encrypted": True,
                    "salt": base64.b64encode(salt).decode(),
                    "data": encrypted_data,
                    "created_at": datetime.now().isoformat(),
                    "database_size": len(db_data)
                }
                
                with open(backup_path, 'w') as backup_file:
                    json.dump(backup_data, backup_file)
            else:
                # Unencrypted backup
                with open(backup_path, 'wb') as backup_file:
                    backup_file.write(db_data)
            
            self.log_security_event("BACKUP_CREATED", details=f"Backup created: {backup_path}")
            return True, "تم إنشاء النسخة الاحتياطية بنجاح"
            
        except Exception as e:
            self.log_security_event("BACKUP_FAILED", details=f"Backup failed: {str(e)}")
            return False, f"فشل في إنشاء النسخة الاحتياطية: {str(e)}"
    
    def restore_secure_backup(self, backup_path: str, encryption_password: str = None):
        """Restore from encrypted database backup"""
        try:
            if not os.path.exists(backup_path):
                return False, "ملف النسخة الاحتياطية غير موجود"
            
            # Try to read as encrypted backup first
            try:
                with open(backup_path, 'r') as backup_file:
                    backup_data = json.load(backup_file)
                
                if backup_data.get("encrypted") and encryption_password:
                    # Decrypt backup
                    salt = base64.b64decode(backup_data["salt"])
                    key, _ = self.generate_encryption_key(encryption_password, salt)
                    decrypted_data = self.decrypt_data(backup_data["data"], key)
                    db_data = base64.b64decode(decrypted_data)
                else:
                    return False, "النسخة الاحتياطية مشفرة وتحتاج كلمة مرور"
                    
            except json.JSONDecodeError:
                # Try as unencrypted backup
                with open(backup_path, 'rb') as backup_file:
                    db_data = backup_file.read()
            
            # Create backup of current database
            current_backup = f"{self.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            with open(self.db_path, 'rb') as current_db:
                with open(current_backup, 'wb') as backup_current:
                    backup_current.write(current_db.read())
            
            # Restore database
            with open(self.db_path, 'wb') as db_file:
                db_file.write(db_data)
            
            self.log_security_event("RESTORE_COMPLETED", details=f"Database restored from: {backup_path}")
            return True, "تم استعادة قاعدة البيانات بنجاح"
            
        except Exception as e:
            self.log_security_event("RESTORE_FAILED", details=f"Restore failed: {str(e)}")
            return False, f"فشل في استعادة قاعدة البيانات: {str(e)}"
    
    def check_database_integrity(self):
        """Check database integrity and security"""
        issues = []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check database integrity
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()[0]
            if integrity_result != "ok":
                issues.append(f"Database integrity issue: {integrity_result}")
            
            # Check for weak passwords (if users table exists)
            try:
                cursor.execute("SELECT username, password_hash FROM users")
                users = cursor.fetchall()
                for username, password_hash in users:
                    if not password_hash or len(password_hash) < 50:
                        issues.append(f"Weak password hash for user: {username}")
            except sqlite3.OperationalError:
                pass  # Users table might not exist
            
            conn.close()
            
        except Exception as e:
            issues.append(f"Database connection error: {str(e)}")
        
        return issues
    
    def get_security_report(self):
        """Generate comprehensive security report"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "database_path": self.db_path,
            "security_config": self.config,
            "integrity_issues": self.check_database_integrity(),
            "backup_status": os.path.exists(f"{self.db_path}.backup"),
            "audit_log_size": os.path.getsize(self.audit_log_path) if os.path.exists(self.audit_log_path) else 0
        }
        
        return report
