#!/usr/bin/env python3
"""
Direct runner for BANGHALAU - Simplified version
"""

print("🚀 BANGHALAU - Starting Application...")

# Import Flask app directly
from app import app

if __name__ == '__main__':
    print("✅ App imported successfully")
    print("📍 Starting server on: http://localhost:5000")
    print("👤 Username: admin")
    print("🔑 Password: admin123")
    print("🌐 Opening browser...")
    
    # Start the Flask development server
    app.run(
        host='127.0.0.1',
        port=5000,
        debug=False,
        use_reloader=False,
        threaded=True
    )
