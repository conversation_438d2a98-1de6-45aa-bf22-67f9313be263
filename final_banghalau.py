#!/usr/bin/env python3
"""
BANGHALAU - Final Working Version
"""

import socket
import webbrowser
import threading
import time
from flask import Flask, render_template, request, redirect, url_for, flash, session
from werkzeug.security import check_password_hash

# Create Flask app
app = Flask(__name__)
app.secret_key = 'banghalau_secret_key_2024'

# Admin user credentials
ADMIN_USER = {
    'username': 'admin',
    'password': 'admin123'  # Using plain text for simplicity
}

def find_available_port():
    """Find an available port"""
    for port in [5000, 8000, 8080, 3000, 4000, 9000, 7000]:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            if result != 0:  # Port is available
                return port
        except:
            continue
    return 9999

@app.route('/')
def index():
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username == ADMIN_USER['username'] and password == ADMIN_USER['password']:
            session['logged_in'] = True
            session['username'] = username
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BANGHALAU - تسجيل الدخول</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            max-width: 300px;
            width: 100%;
            text-align: center;
        }
        .logo { font-size: 4em; margin-bottom: 20px; animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }
        h1 { color: #333; margin-bottom: 10px; font-size: 2.2em; }
        h2 { color: #666; margin-bottom: 30px; font-size: 1.3em; }
        .form-group { margin-bottom: 20px; text-align: right; }
        label { display: block; margin-bottom: 8px; color: #555; font-weight: bold; }
        input[type="text"], input[type="password"] {
            width: 100%; padding: 15px; border: 2px solid #ddd; border-radius: 10px;
            font-size: 16px; transition: border-color 0.3s;
        }
        input:focus { outline: none; border-color: #667eea; }
        .btn {
            background: #667eea; color: white; padding: 15px 30px; border: none;
            border-radius: 10px; font-size: 16px; cursor: pointer; width: 100%;
            transition: background 0.3s; margin-top: 10px;
        }
        .btn:hover { background: #5a6fd8; }
        .credentials {
            background: #e8f5e8; padding: 20px; border-radius: 10px; margin-bottom: 25px;
            border: 2px solid #4CAF50;
        }
        .credentials h3 { color: #2e7d32; margin-bottom: 15px; }
        .cred-item { background: white; padding: 12px; margin: 8px 0; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🏠</div>
        <h1>BANGHALAU</h1>
        <h2>نظام إدارة توزيع البنغالوهات</h2>
        
        <div class="credentials">
            <h3>🔐 بيانات تسجيل الدخول</h3>
            <div class="cred-item">👤 اسم المستخدم: <strong>admin</strong></div>
            <div class="cred-item">🔑 كلمة المرور: <strong>admin123</strong></div>
        </div>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            <button type="submit" class="btn">🚀 تسجيل الدخول</button>
        </form>
    </div>
</body>
</html>
    '''

@app.route('/dashboard')
def dashboard():
    if 'logged_in' not in session:
        return redirect(url_for('login'))
    
    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BANGHALAU - لوحة التحكم</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header {
            background: rgba(255, 255, 255, 0.95); padding: 30px; border-radius: 20px;
            margin-bottom: 30px; text-align: center; backdrop-filter: blur(10px);
        }
        .success-alert {
            background: #4CAF50; color: white; padding: 20px; border-radius: 15px;
            margin-bottom: 30px; text-align: center; font-size: 1.2em;
        }
        .dashboard-grid {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px; margin-bottom: 30px;
        }
        .card {
            background: rgba(255, 255, 255, 0.95); padding: 30px; border-radius: 20px;
            backdrop-filter: blur(10px); box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            text-align: center; transition: transform 0.3s;
        }
        .card:hover { transform: translateY(-8px); }
        .card-icon { font-size: 3.5em; margin-bottom: 20px; }
        .card-title { font-size: 1.6em; color: #333; margin-bottom: 15px; }
        .card-description { color: #666; margin-bottom: 25px; line-height: 1.6; }
        .btn {
            background: #667eea; color: white; padding: 12px 25px; border: none;
            border-radius: 10px; text-decoration: none; display: inline-block;
            transition: background 0.3s; font-size: 1em;
        }
        .btn:hover { background: #5a6fd8; }
        .logout-btn { background: #f44336; margin-top: 20px; }
        .logout-btn:hover { background: #d32f2f; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 BANGHALAU</h1>
            <h2>نظام إدارة توزيع البنغالوهات</h2>
            <p>مرحباً بك، ''' + session.get('username', 'المستخدم') + '''</p>
        </div>
        
        <div class="success-alert">
            ✅ تم تسجيل الدخول بنجاح! النظام يعمل بشكل مثالي
        </div>
        
        <div class="dashboard-grid">
            <div class="card">
                <div class="card-icon">🏠</div>
                <div class="card-title">إدارة البنغالوهات</div>
                <div class="card-description">إضافة وتعديل وحذف البنغالوهات وإدارة خصائصها</div>
                <a href="#" class="btn">إدارة البنغالوهات</a>
            </div>
            
            <div class="card">
                <div class="card-icon">👥</div>
                <div class="card-title">إدارة الأفراد العسكريين</div>
                <div class="card-description">تسجيل وإدارة بيانات الأفراد العسكريين</div>
                <a href="#" class="btn">إدارة الأفراد</a>
            </div>
            
            <div class="card">
                <div class="card-icon">🎖️</div>
                <div class="card-title">إدارة الرتب العسكرية</div>
                <div class="card-description">تنظيم وإدارة الرتب العسكرية المختلفة</div>
                <a href="#" class="btn">إدارة الرتب</a>
            </div>
            
            <div class="card">
                <div class="card-icon">🏢</div>
                <div class="card-title">إدارة الوحدات</div>
                <div class="card-description">تنظيم الوحدات والأقسام العسكرية</div>
                <a href="#" class="btn">إدارة الوحدات</a>
            </div>
            
            <div class="card">
                <div class="card-icon">📋</div>
                <div class="card-title">توزيع البنغالوهات</div>
                <div class="card-description">ربط الأفراد بالبنغالوهات وإدارة التوزيع</div>
                <a href="#" class="btn">إدارة التوزيع</a>
            </div>
            
            <div class="card">
                <div class="card-icon">📊</div>
                <div class="card-title">التقارير والإحصائيات</div>
                <div class="card-description">عرض التقارير وإحصائيات الإشغال</div>
                <a href="#" class="btn">عرض التقارير</a>
            </div>
        </div>
        
        <div style="text-align: center;">
            <a href="/logout" class="btn logout-btn">🚪 تسجيل الخروج</a>
        </div>
    </div>
</body>
</html>
    '''

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

def open_browser_delayed(port):
    time.sleep(2)
    webbrowser.open(f'http://localhost:{port}')

if __name__ == '__main__':
    port = find_available_port()
    
    print("🚀 BANGHALAU - نظام إدارة توزيع البنغالوهات")
    print("=" * 60)
    print(f"🌐 الخادم يعمل على: http://localhost:{port}")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("🔄 فتح المتصفح...")
    print("=" * 60)
    
    # Open browser in background
    browser_thread = threading.Thread(target=open_browser_delayed, args=(port,))
    browser_thread.daemon = True
    browser_thread.start()
    
    # Start Flask server
    app.run(host='0.0.0.0', port=port, debug=False)
