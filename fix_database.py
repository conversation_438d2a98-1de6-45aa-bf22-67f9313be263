#!/usr/bin/env python3
"""
Fix database lock issues
"""

import sqlite3
import os
import sys
import time

def fix_database_lock():
    """Fix database lock issues"""
    
    print("🔧 Fixing Database Lock Issues")
    print("=" * 50)
    
    # Database file path
    db_path = "banghalau.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} not found")
        return False
    
    try:
        # Remove WAL and SHM files if they exist
        wal_file = db_path + "-wal"
        shm_file = db_path + "-shm"
        
        if os.path.exists(wal_file):
            os.remove(wal_file)
            print(f"✅ Removed {wal_file}")
            
        if os.path.exists(shm_file):
            os.remove(shm_file)
            print(f"✅ Removed {shm_file}")
        
        # Test database connection
        print("🔍 Testing database connection...")
        
        conn = sqlite3.connect(db_path, timeout=30)
        conn.execute("PRAGMA journal_mode=DELETE;")
        conn.execute("PRAGMA synchronous=NORMAL;")
        conn.execute("PRAGMA temp_store=MEMORY;")
        conn.execute("PRAGMA mmap_size=268435456;")  # 256MB
        
        # Test a simple query
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"✅ Database connection successful")
        print(f"📊 Found {len(tables)} tables:")
        for table in tables:
            print(f"   - {table[0]}")
        
        # Vacuum the database to optimize it
        print("🧹 Optimizing database...")
        conn.execute("VACUUM;")
        
        conn.close()
        print("✅ Database fixed and optimized successfully!")
        return True
        
    except sqlite3.OperationalError as e:
        print(f"❌ Database error: {e}")
        
        # Try to force unlock
        try:
            print("🔓 Attempting to force unlock...")
            conn = sqlite3.connect(db_path, timeout=1)
            conn.execute("BEGIN IMMEDIATE;")
            conn.rollback()
            conn.close()
            print("✅ Force unlock successful")
            return True
        except Exception as e2:
            print(f"❌ Force unlock failed: {e2}")
            return False
            
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def restart_application():
    """Restart the Flask application"""
    print("\n🚀 Restarting Flask Application")
    print("=" * 50)
    
    try:
        # Kill any existing Python processes
        os.system("taskkill /f /im python.exe 2>nul")
        time.sleep(2)
        
        print("✅ Stopped existing processes")
        
        # Start the application
        print("🌟 Starting Flask application...")
        os.system("start cmd /k python app.py")
        
        print("✅ Application started successfully!")
        print("🌐 Access the application at: http://localhost:5000")
        
    except Exception as e:
        print(f"❌ Error restarting application: {e}")

if __name__ == "__main__":
    print("🔧 BANGHALAU Database Fix Tool")
    print("=" * 50)
    
    if fix_database_lock():
        restart_application()
    else:
        print("\n❌ Failed to fix database issues")
        print("💡 Try manually deleting .db-wal and .db-shm files")
