#!/usr/bin/env python3
"""
Security Installation Script for BANGHALAU
Installs required security packages and sets up protection
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a Python package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Failed to install {package}")
        return False

def main():
    """Main installation function"""
    print("🔒 BANGHALAU Security Installation")
    print("=" * 50)
    
    # Required security packages
    security_packages = [
        "cryptography",  # For encryption
        "bcrypt",        # For password hashing
        "passlib",       # For password utilities
    ]
    
    print("📦 Installing security packages...")
    
    failed_packages = []
    for package in security_packages:
        print(f"Installing {package}...")
        if not install_package(package):
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n❌ Failed to install: {', '.join(failed_packages)}")
        print("Please install them manually:")
        for package in failed_packages:
            print(f"  pip install {package}")
        return False
    
    print("\n✅ All security packages installed successfully!")
    
    # Create necessary directories
    directories = [
        "backups",
        "snapshots",
        "logs"
    ]
    
    print("\n📁 Creating security directories...")
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created directory: {directory}")
        except Exception as e:
            print(f"❌ Failed to create directory {directory}: {e}")
    
    print("\n🔐 Security system ready!")
    print("You can now run the security manager:")
    print("  python security_manager.py")
    
    return True

if __name__ == "__main__":
    if main():
        print("\n🎉 Installation completed successfully!")
    else:
        print("\n💥 Installation failed!")
        sys.exit(1)
