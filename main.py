#!/usr/bin/env python
"""
Main entry point for BANGHALAU database application
"""

import os
import sys
from database import DatabaseManager, DatabaseOperations
from models import User, Item, Category, PersonnelMilitaire


def display_menu():
    """Display the main menu"""
    print("\n===== Base de Données BANGHALAU =====")
    print("1. Initialiser la base de données")
    print("2. Opérations utilisateur")
    print("3. Opérations d'articles")
    print("4. Opérations de catégorie")
    print("5. Opérations Personnel Militaire")
    print("6. Opérations de grade")
    print("7. Opérations Unité/Position")
    print("8. Opérations Bungalow")
    print("9. Opérations de session")
    print("10. Opérations Distribution Bungalows")
    print("0. Quitter")
    print("==============================")
    return input("Entrez votre choix: ")


def user_menu():
    """Display the user menu"""
    print("\n===== User Operations =====")
    print("1. Add user")
    print("2. Get user")
    print("3. List users")
    print("4. Update user")
    print("5. Delete user")
    print("0. Back to main menu")
    print("==========================")
    return input("Enter your choice: ")


def item_menu():
    """Display the item menu"""
    print("\n===== Item Operations =====")
    print("1. Add item")
    print("2. Get item")
    print("3. List items")
    print("4. Update item")
    print("5. Delete item")
    print("0. Back to main menu")
    print("==========================")
    return input("Enter your choice: ")


def category_menu():
    """Display the category menu"""
    print("\n===== Category Operations =====")
    print("1. Add category")
    print("2. Get category")
    print("3. List categories")
    print("4. Update category")
    print("5. Delete category")
    print("0. Back to main menu")
    print("==============================")
    return input("Enter your choice: ")


def personnel_menu():
    """Display the personnel menu"""
    print("\n===== Personnel Militaire Operations =====")
    print("1. Add personnel")
    print("2. Get personnel")
    print("3. List personnel")
    print("4. Update personnel")
    print("5. Delete personnel")
    print("0. Back to main menu")
    print("========================================")
    return input("Enter your choice: ")


def grade_menu():
    """Display the grade menu"""
    print("\n===== Grade Operations =====")
    print("1. Add grade")
    print("2. Get grade")
    print("3. List grades")
    print("4. Update grade")
    print("5. Delete grade")
    print("0. Back to main menu")
    print("==========================")
    return input("Enter your choice: ")


def unite_menu():
    """Display the unite menu"""
    print("\n===== Unité/Position Operations =====")
    print("1. Add unit")
    print("2. Get unit")
    print("3. List units")
    print("4. Update unit")
    print("5. Delete unit")
    print("0. Back to main menu")
    print("==================================")
    return input("Enter your choice: ")


def bungalow_menu():
    """Display the bungalow menu"""
    print("\n===== Bungalow Operations =====")
    print("1. Add bungalow")
    print("2. Get bungalow")
    print("3. List bungalows")
    print("4. List bungalows with occupancy status")
    print("5. List bungalows by session")
    print("6. List available bungalows")
    print("7. Update bungalow")
    print("8. Delete bungalow")
    print("0. Back to main menu")
    print("============================")
    return input("Enter your choice: ")


def session_menu():
    """Display the session menu"""
    print("\n===== Session Operations =====")
    print("1. Add session")
    print("2. Get session")
    print("3. List sessions")
    print("4. Update session")
    print("5. Delete session")
    print("0. Back to main menu")
    print("============================")
    return input("Enter your choice: ")


def distribution_bungalow_menu():
    """Display the distribution bungalow menu"""
    print("\n===== Distribution Bungalows Operations =====")
    print("1. Add distribution")
    print("2. Get distribution")
    print("3. List distributions")
    print("4. Update distribution")
    print("5. Delete distribution")
    print("0. Back to main menu")
    print("==========================================")
    return input("Enter your choice: ")


def initialize_database():
    """Initialize the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_manager.initialize_database()
    db_manager.close()
    input("Press Enter to continue...")


def add_user():
    """Add a user to the database"""
    username = input("Enter username: ")
    email = input("Enter email: ")

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    user_id = db_ops.create_user(username, email)
    if user_id:
        print(f"User created with ID: {user_id}")
    else:
        print("Failed to create user.")

    db_manager.close()
    input("Press Enter to continue...")


def get_user():
    """Get a user from the database"""
    user_id = input("Enter user ID (or leave blank to search by username): ")

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if user_id:
        user = db_ops.get_user(int(user_id))
    else:
        username = input("Enter username: ")
        user = db_ops.get_user_by_username(username)

    if user:
        print("\nUser details:")
        for key, value in user.items():
            print(f"{key}: {value}")
    else:
        print("User not found.")

    db_manager.close()
    input("Press Enter to continue...")


def list_users():
    """List all users in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    users = db_ops.list_users()
    if users:
        print("\nUsers:")
        for user in users:
            print(f"ID: {user['id']}, Username: {user['username']}, Email: {user['email']}")
    else:
        print("No users found.")

    db_manager.close()
    input("Press Enter to continue...")


def add_item():
    """Add an item to the database"""
    name = input("Enter item name: ")
    description = input("Enter item description (optional): ") or None
    price_str = input("Enter item price (optional): ")
    price = float(price_str) if price_str else None
    user_id_str = input("Enter user ID (optional): ")
    user_id = int(user_id_str) if user_id_str else None

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    item_id = db_ops.create_item(name, description, price, user_id)
    if item_id:
        print(f"Item created with ID: {item_id}")
    else:
        print("Failed to create item.")

    db_manager.close()
    input("Press Enter to continue...")


def get_item():
    """Get an item from the database"""
    item_id = input("Enter item ID: ")

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    item = db_ops.get_item(int(item_id))
    if item:
        print("\nItem details:")
        for key, value in item.items():
            print(f"{key}: {value}")
    else:
        print("Item not found.")

    db_manager.close()
    input("Press Enter to continue...")


def list_items():
    """List all items in the database"""
    user_id_str = input("Enter user ID to filter (optional): ")
    user_id = int(user_id_str) if user_id_str else None

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    items = db_ops.list_items(user_id)
    if items:
        print("\nItems:")
        for item in items:
            print(f"ID: {item['id']}, Name: {item['name']}, Price: {item['price']}")
    else:
        print("No items found.")

    db_manager.close()
    input("Press Enter to continue...")


def add_personnel():
    """Add a military personnel to the database"""
    matricule = input("Enter matricule (unique identifier): ")
    nom = input("Enter last name: ")
    prenom = input("Enter first name: ")

    # Show available grades
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    grades = db_ops.list_grades()
    if grades:
        print("\nAvailable grades:")
        for grade in grades:
            print(f"ID: {grade['id']}, Number: {grade['numero']}, Grade: {grade['grade']}")

    grade_id_str = input("Enter grade ID (optional): ") or None
    grade_id = int(grade_id_str) if grade_id_str else None
    unite = input("Enter unit (optional): ") or None
    numero = input("Enter number (optional): ") or None

    personnel_id = db_ops.create_personnel_militaire(matricule, nom, prenom, grade_id, unite, numero)
    if personnel_id:
        print(f"Personnel created with ID: {personnel_id}")
    else:
        print("Failed to create personnel.")

    db_manager.close()
    input("Press Enter to continue...")


def get_personnel():
    """Get a military personnel from the database"""
    search_by = input("Search by ID or matricule? (id/matricule): ").lower()
    include_grade = input("Include grade information? (y/n): ").lower() == 'y'

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if search_by == "matricule":
        matricule = input("Enter matricule: ")
        if include_grade:
            personnel = db_ops.get_personnel_with_grade_by_matricule(matricule)
        else:
            personnel = db_ops.get_personnel_by_matricule(matricule)
    else:
        personnel_id = input("Enter personnel ID: ")
        if include_grade:
            personnel = db_ops.get_personnel_with_grade(int(personnel_id))
        else:
            personnel = db_ops.get_personnel_militaire(int(personnel_id))

    if personnel:
        print("\nPersonnel details:")
        for key, value in personnel.items():
            print(f"{key}: {value}")
    else:
        print("Personnel not found.")

    db_manager.close()
    input("Press Enter to continue...")


def list_personnel():
    """List all military personnel in the database"""
    include_grade = input("Include grade information? (y/n): ").lower() == 'y'
    filter_by_grade = input("Filter by grade ID? (y/n): ").lower() == 'y'

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    grade_id = None
    if filter_by_grade:
        # Show available grades
        grades = db_ops.list_grades()
        if grades:
            print("\nAvailable grades:")
            for grade in grades:
                print(f"ID: {grade['id']}, Number: {grade['numero']}, Grade: {grade['grade']}")

        grade_id_str = input("Enter grade ID: ")
        grade_id = int(grade_id_str) if grade_id_str else None

    if grade_id:
        personnel_list = db_ops.get_personnel_by_grade(grade_id)
    elif include_grade:
        personnel_list = db_ops.list_personnel_with_grades()
    else:
        personnel_list = db_ops.list_personnel_militaire()

    if personnel_list:
        print("\nMilitary Personnel:")
        for personnel in personnel_list:
            if include_grade:
                print(f"ID: {personnel['id']}, Matricule: {personnel['matricule']}, Nom: {personnel['nom']}, Prenom: {personnel['prenom']}, Grade: {personnel.get('grade_name', 'N/A')}")
            else:
                print(f"ID: {personnel['id']}, Matricule: {personnel['matricule']}, Nom: {personnel['nom']}, Prenom: {personnel['prenom']}, Grade ID: {personnel['grade_id']}")
    else:
        print("No personnel found.")

    db_manager.close()
    input("Press Enter to continue...")


def add_grade():
    """Add a grade to the database"""
    numero = input("Enter grade number: ")
    grade = input("Enter grade name: ")
    description = input("Enter grade description (optional): ") or None

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    grade_id = db_ops.create_grade(numero, grade, description)
    if grade_id:
        print(f"Grade created with ID: {grade_id}")
    else:
        print("Failed to create grade.")

    db_manager.close()
    input("Press Enter to continue...")


def get_grade():
    """Get a grade from the database"""
    search_by = input("Search by ID, number, or name? (id/number/name): ").lower()

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if search_by == "number":
        numero = input("Enter grade number: ")
        grade = db_ops.get_grade_by_numero(numero)
    elif search_by == "name":
        grade_name = input("Enter grade name: ")
        grade = db_ops.get_grade_by_name(grade_name)
    else:
        grade_id = input("Enter grade ID: ")
        grade = db_ops.get_grade(int(grade_id))

    if grade:
        print("\nGrade details:")
        for key, value in grade.items():
            print(f"{key}: {value}")
    else:
        print("Grade not found.")

    db_manager.close()
    input("Press Enter to continue...")


def list_grades():
    """List all grades in the database"""
    include_count = input("Include personnel count? (y/n): ").lower() == 'y'

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if include_count:
        grades = db_ops.list_grades_with_personnel_count()
    else:
        grades = db_ops.list_grades()

    if grades:
        print("\nGrades:")
        for grade in grades:
            if include_count:
                print(f"ID: {grade['id']}, Number: {grade['numero']}, Grade: {grade['grade']}, Description: {grade['description']}, Personnel Count: {grade['personnel_count']}")
            else:
                print(f"ID: {grade['id']}, Number: {grade['numero']}, Grade: {grade['grade']}, Description: {grade['description']}")
    else:
        print("No grades found.")

    db_manager.close()
    input("Press Enter to continue...")


def add_unite():
    """Add a unit to the database"""
    numero = input("Enter unit number: ")
    description = input("Enter unit description: ")
    raccourci = input("Enter unit shortcut (optional): ") or None

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    unite_id = db_ops.create_unite(numero, description, raccourci)
    if unite_id:
        print(f"Unit created with ID: {unite_id}")
    else:
        print("Failed to create unit.")

    db_manager.close()
    input("Press Enter to continue...")


def get_unite():
    """Get a unit from the database"""
    search_by = input("Search by ID, number, or shortcut? (id/number/shortcut): ").lower()

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if search_by == "number":
        numero = input("Enter unit number: ")
        unite = db_ops.get_unite_by_numero(numero)
    elif search_by == "shortcut":
        raccourci = input("Enter unit shortcut: ")
        unite = db_ops.get_unite_by_raccourci(raccourci)
    else:
        unite_id = input("Enter unit ID: ")
        unite = db_ops.get_unite(int(unite_id))

    if unite:
        print("\nUnit details:")
        for key, value in unite.items():
            print(f"{key}: {value}")
    else:
        print("Unit not found.")

    db_manager.close()
    input("Press Enter to continue...")


def list_unites():
    """List all units in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    unites = db_ops.list_unites()
    if unites:
        print("\nUnits:")
        for unite in unites:
            print(f"ID: {unite['id']}, Number: {unite['numero']}, Description: {unite['description']}, Shortcut: {unite['raccourci']}")
    else:
        print("No units found.")

    db_manager.close()
    input("Press Enter to continue...")


def add_bungalow():
    """Add a bungalow to the database"""
    numero = input("Enter bungalow number: ")
    endroit = input("Enter bungalow location: ")
    capacite_str = input("Enter bungalow capacity (optional): ")
    capacite = int(capacite_str) if capacite_str else None
    caracteristiques = input("Enter bungalow characteristics (optional): ") or None

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalow_id = db_ops.create_bungalow(numero, endroit, capacite, caracteristiques)
    if bungalow_id:
        print(f"Bungalow created with ID: {bungalow_id}")
    else:
        print("Failed to create bungalow.")

    db_manager.close()
    input("Press Enter to continue...")


def get_bungalow():
    """Get a bungalow from the database"""
    search_by = input("Search by ID or number? (id/number): ").lower()

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if search_by == "number":
        numero = input("Enter bungalow number: ")
        bungalow = db_ops.get_bungalow_by_numero(numero)
    else:
        bungalow_id = input("Enter bungalow ID: ")
        bungalow = db_ops.get_bungalow(int(bungalow_id))

    if bungalow:
        print("\nBungalow details:")
        for key, value in bungalow.items():
            print(f"{key}: {value}")
    else:
        print("Bungalow not found.")

    db_manager.close()
    input("Press Enter to continue...")


def list_bungalows():
    """List all bungalows in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalows = db_ops.list_bungalows()
    if bungalows:
        print("\nBungalows:")
        for bungalow in bungalows:
            print(f"ID: {bungalow['id']}, Number: {bungalow['numero']}, Location: {bungalow['endroit']}, Capacity: {bungalow['capacite']}")
    else:
        print("No bungalows found.")

    db_manager.close()
    input("Press Enter to continue...")


def list_bungalows_with_status():
    """List all bungalows with occupancy status"""
    date = input("Enter date to check (YYYY-MM-DD) or leave empty for today: ") or None

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalows = db_ops.get_bungalows_with_occupancy_status(date)
    if bungalows:
        print("\nBungalows with occupancy status:")
        for bungalow in bungalows:
            status = bungalow.get('status', 'Unknown')
            print(f"ID: {bungalow['id']}, Number: {bungalow['numero']}, Location: {bungalow['endroit']}, Capacity: {bungalow['capacite']}, Status: {status}")

            if status == 'Occupied':
                print(f"  Distribution: {bungalow.get('distribution_numero', 'N/A')}")
                if bungalow.get('personnel_id'):
                    personnel = db_ops.get_personnel_militaire(bungalow['personnel_id'])
                    if personnel:
                        print(f"  Assigned to: {personnel.get('nom', 'N/A')} {personnel.get('prenom', 'N/A')}")
                if bungalow.get('session_id'):
                    session = db_ops.get_session(bungalow['session_id'])
                    if session:
                        print(f"  Session: {session.get('numero', 'N/A')} - {session.get('description', 'N/A')}")
                print(f"  Period: {bungalow.get('date_debut', 'N/A')} to {bungalow.get('date_fin', 'N/A')}")
    else:
        print("No bungalows found.")

    db_manager.close()
    input("Press Enter to continue...")


def list_bungalows_by_session():
    """List all bungalows assigned to a session"""
    # Show available sessions
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    sessions = db_ops.list_sessions()
    if sessions:
        print("\nAvailable sessions:")
        for session in sessions:
            print(f"ID: {session['id']}, Number: {session['numero']}, Description: {session['description']}, Start Date: {session['date_debut']}, End Date: {session['date_fin']}")

    session_id_str = input("\nEnter session ID: ")
    session_id = int(session_id_str) if session_id_str else None

    if session_id:
        bungalows = db_ops.get_bungalows_by_session(session_id)
        if bungalows:
            print("\nBungalows assigned to session:")
            for bungalow in bungalows:
                print(f"ID: {bungalow['id']}, Number: {bungalow['numero']}, Location: {bungalow['endroit']}, Capacity: {bungalow['capacite']}")
                print(f"  Distribution: {bungalow.get('distribution_numero', 'N/A')}")
                if bungalow.get('personnel_id'):
                    personnel = db_ops.get_personnel_militaire(bungalow['personnel_id'])
                    if personnel:
                        print(f"  Assigned to: {personnel.get('nom', 'N/A')} {personnel.get('prenom', 'N/A')}")
                print(f"  Period: {bungalow.get('date_debut', 'N/A')} to {bungalow.get('date_fin', 'N/A')}")
        else:
            print("No bungalows found for this session.")
    else:
        print("Invalid session ID.")

    db_manager.close()
    input("Press Enter to continue...")


def list_available_bungalows():
    """List all available bungalows for a period"""
    start_date = input("Enter start date (YYYY-MM-DD): ")
    end_date = input("Enter end date (YYYY-MM-DD): ")

    if not start_date or not end_date:
        print("Both start date and end date are required.")
        input("Press Enter to continue...")
        return

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalows = db_ops.get_available_bungalows(start_date, end_date)
    if bungalows:
        print(f"\nBungalows available from {start_date} to {end_date}:")
        for bungalow in bungalows:
            print(f"ID: {bungalow['id']}, Number: {bungalow['numero']}, Location: {bungalow['endroit']}, Capacity: {bungalow['capacite']}")
    else:
        print("No available bungalows found for this period.")

    db_manager.close()
    input("Press Enter to continue...")


def list_available_bungalows():
    """List all available bungalows for a period"""
    start_date = input("Enter start date (YYYY-MM-DD): ")
    end_date = input("Enter end date (YYYY-MM-DD): ")

    if not start_date or not end_date:
        print("Both start date and end date are required.")
        input("Press Enter to continue...")
        return

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalows = db_ops.get_available_bungalows(start_date, end_date)
    if bungalows:
        print(f"\nBungalows available from {start_date} to {end_date}:")
        for bungalow in bungalows:
            print(f"ID: {bungalow['id']}, Number: {bungalow['numero']}, Location: {bungalow['endroit']}, Capacity: {bungalow['capacite']}")
    else:
        print("No available bungalows found for this period.")

    db_manager.close()
    input("Press Enter to continue...")


def delete_bungalow():
    """Delete a bungalow from the database"""
    search_by = input("Search by ID or number? (id/number): ").lower()

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if search_by == "number":
        numero = input("Enter bungalow number: ")
        bungalow = db_ops.get_bungalow_by_numero(numero)
        if bungalow:
            bungalow_id = bungalow['id']
        else:
            print("Bungalow not found.")
            db_manager.close()
            input("Press Enter to continue...")
            return
    else:
        bungalow_id_str = input("Enter bungalow ID: ")
        bungalow_id = int(bungalow_id_str) if bungalow_id_str else None
        if not bungalow_id:
            print("Invalid bungalow ID.")
            db_manager.close()
            input("Press Enter to continue...")
            return
        bungalow = db_ops.get_bungalow(bungalow_id)
        if not bungalow:
            print("Bungalow not found.")
            db_manager.close()
            input("Press Enter to continue...")
            return

    # Check if bungalow is currently assigned
    distributions = db_ops.get_distribution_bungalows_by_bungalow(bungalow_id)
    if distributions:
        print("Cannot delete bungalow because it is currently assigned to distributions:")
        for dist in distributions:
            print(f"Distribution ID: {dist['id']}, Number: {dist['numero']}")
        db_manager.close()
        input("Press Enter to continue...")
        return

    # Confirm deletion
    confirm = input(f"Are you sure you want to delete bungalow {bungalow['numero']}? (y/n): ").lower()
    if confirm != 'y':
        print("Deletion cancelled.")
        db_manager.close()
        input("Press Enter to continue...")
        return

    success = db_ops.delete_bungalow(bungalow_id)
    if success:
        print("Bungalow deleted successfully.")
    else:
        print("Failed to delete bungalow.")

    db_manager.close()
    input("Press Enter to continue...")


def update_bungalow():
    """Update a bungalow in the database"""
    search_by = input("Search by ID or number? (id/number): ").lower()

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if search_by == "number":
        numero = input("Enter bungalow number: ")
        bungalow = db_ops.get_bungalow_by_numero(numero)
    else:
        bungalow_id = input("Enter bungalow ID: ")
        bungalow = db_ops.get_bungalow(int(bungalow_id))

    if bungalow:
        print("\nCurrent bungalow details:")
        print(f"ID: {bungalow['id']}")
        print(f"Number: {bungalow['numero']}")
        print(f"Location: {bungalow['endroit']}")
        print(f"Capacity: {bungalow['capacite']}")
        print(f"Characteristics: {bungalow['caracteristiques']}")

        print("\nEnter new details (leave empty to keep current value):")
        new_numero = input(f"New number [{bungalow['numero']}]: ") or None
        new_endroit = input(f"New location [{bungalow['endroit']}]: ") or None
        new_capacite_str = input(f"New capacity [{bungalow['capacite']}]: ") or None
        new_capacite = int(new_capacite_str) if new_capacite_str else None
        new_caracteristiques = input(f"New characteristics [{bungalow['caracteristiques']}]: ") or None

        success = db_ops.update_bungalow(
            bungalow['id'],
            numero=new_numero,
            endroit=new_endroit,
            capacite=new_capacite,
            caracteristiques=new_caracteristiques
        )

        if success:
            print("Bungalow updated successfully.")
        else:
            print("Failed to update bungalow.")
    else:
        print("Bungalow not found.")

    db_manager.close()
    input("Press Enter to continue...")


def add_session():
    """Add a session to the database"""
    numero = input("Enter session number: ")
    description = input("Enter session description (optional): ") or None
    date_debut = input("Enter session start date (YYYY-MM-DD): ")
    date_fin = input("Enter session end date (YYYY-MM-DD) (optional): ") or None
    etat_session = input("Enter session state (optional): ") or None

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    session_id = db_ops.create_session(numero, date_debut, description, date_fin, etat_session)
    if session_id:
        print(f"Session created with ID: {session_id}")
    else:
        print("Failed to create session.")

    db_manager.close()
    input("Press Enter to continue...")


def get_session():
    """Get a session from the database"""
    search_by = input("Search by ID or number? (id/number): ").lower()

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if search_by == "number":
        numero = input("Enter session number: ")
        session = db_ops.get_session_by_numero(numero)
    else:
        session_id = input("Enter session ID: ")
        session = db_ops.get_session(int(session_id))

    if session:
        print("\nSession details:")
        for key, value in session.items():
            print(f"{key}: {value}")
    else:
        print("Session not found.")

    db_manager.close()
    input("Press Enter to continue...")


def list_sessions():
    """List all sessions in the database"""
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    sessions = db_ops.list_sessions()
    if sessions:
        print("\nSessions:")
        for session in sessions:
            print(f"ID: {session['id']}, Number: {session['numero']}, Start Date: {session['date_debut']}, End Date: {session['date_fin']}, State: {session['etat_session']}")
    else:
        print("No sessions found.")

    db_manager.close()
    input("Press Enter to continue...")


def add_distribution_bungalow():
    """Add a bungalow distribution to the database"""
    numero = input("Enter distribution number: ")

    # Show available bungalows
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalows = db_ops.list_bungalows()
    if bungalows:
        print("\nAvailable bungalows:")
        for bungalow in bungalows:
            print(f"ID: {bungalow['id']}, Number: {bungalow['numero']}, Location: {bungalow['endroit']}, Capacity: {bungalow['capacite']}")

    bungalow_id_str = input("Enter bungalow ID (optional): ") or None
    bungalow_id = int(bungalow_id_str) if bungalow_id_str else None

    # Show available personnel
    personnel_list = db_ops.list_personnel_militaire()
    if personnel_list:
        print("\nAvailable personnel:")
        for personnel in personnel_list:
            print(f"ID: {personnel['id']}, Matricule: {personnel['matricule']}, Nom: {personnel['nom']}, Prenom: {personnel['prenom']}")

    personnel_id_str = input("Enter personnel ID (optional): ") or None
    personnel_id = int(personnel_id_str) if personnel_id_str else None

    # Show available sessions
    sessions = db_ops.list_sessions()
    if sessions:
        print("\nAvailable sessions:")
        for session in sessions:
            print(f"ID: {session['id']}, Number: {session['numero']}, Description: {session['description']}, Start Date: {session['date_debut']}, End Date: {session['date_fin']}")

    session_id_str = input("Enter session ID (optional): ") or None
    session_id = int(session_id_str) if session_id_str else None

    date_debut = input("Enter start date (YYYY-MM-DD) (optional): ") or None
    date_fin = input("Enter end date (YYYY-MM-DD) (optional): ") or None
    notes = input("Enter notes (optional): ") or None

    distribution_id = db_ops.create_distribution_bungalow(numero, bungalow_id, personnel_id, session_id, date_debut, date_fin, notes)
    if distribution_id:
        print(f"Distribution created with ID: {distribution_id}")
    else:
        print("Failed to create distribution.")

    db_manager.close()
    input("Press Enter to continue...")


def get_distribution_bungalow():
    """Get a bungalow distribution from the database"""
    search_by = input("Search by ID or number? (id/number): ").lower()
    include_details = input("Include bungalow, personnel, and session details? (y/n): ").lower() == 'y'

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    if search_by == "number":
        numero = input("Enter distribution number: ")
        distribution = db_ops.get_distribution_bungalow_by_numero(numero)
        if distribution and include_details:
            distribution = db_ops.get_distribution_with_details(distribution['id'])
    else:
        distribution_id = input("Enter distribution ID: ")
        if include_details:
            distribution = db_ops.get_distribution_with_details(int(distribution_id))
        else:
            distribution = db_ops.get_distribution_bungalow(int(distribution_id))

    if distribution:
        print("\nDistribution details:")
        for key, value in distribution.items():
            print(f"{key}: {value}")
    else:
        print("Distribution not found.")

    db_manager.close()
    input("Press Enter to continue...")


def list_distribution_bungalows():
    """List all bungalow distributions in the database"""
    include_details = input("Include bungalow, personnel, and session details? (y/n): ").lower() == 'y'
    filter_option = input("Filter by bungalow, personnel, session, or none? (bungalow/personnel/session/none): ").lower()

    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    bungalow_id = None
    personnel_id = None
    session_id = None

    if filter_option == "bungalow":
        # Show available bungalows
        bungalows = db_ops.list_bungalows()
        if bungalows:
            print("\nAvailable bungalows:")
            for bungalow in bungalows:
                print(f"ID: {bungalow['id']}, Number: {bungalow['numero']}, Location: {bungalow['endroit']}")

        bungalow_id_str = input("Enter bungalow ID: ")
        bungalow_id = int(bungalow_id_str) if bungalow_id_str else None

    elif filter_option == "personnel":
        # Show available personnel
        personnel_list = db_ops.list_personnel_militaire()
        if personnel_list:
            print("\nAvailable personnel:")
            for personnel in personnel_list:
                print(f"ID: {personnel['id']}, Matricule: {personnel['matricule']}, Nom: {personnel['nom']}, Prenom: {personnel['prenom']}")

        personnel_id_str = input("Enter personnel ID: ")
        personnel_id = int(personnel_id_str) if personnel_id_str else None

    elif filter_option == "session":
        # Show available sessions
        sessions = db_ops.list_sessions()
        if sessions:
            print("\nAvailable sessions:")
            for session in sessions:
                print(f"ID: {session['id']}, Number: {session['numero']}, Description: {session['description']}, Start Date: {session['date_debut']}, End Date: {session['date_fin']}")

        session_id_str = input("Enter session ID: ")
        session_id = int(session_id_str) if session_id_str else None

    if bungalow_id:
        distributions = db_ops.get_distribution_bungalows_by_bungalow(bungalow_id)
    elif personnel_id:
        distributions = db_ops.get_distribution_bungalows_by_personnel(personnel_id)
    elif session_id:
        if include_details:
            distributions = db_ops.get_distribution_bungalows_by_session_with_details(session_id)
        else:
            distributions = db_ops.get_distribution_bungalows_by_session(session_id)
    elif include_details:
        distributions = db_ops.list_distributions_with_details()
    else:
        distributions = db_ops.list_distribution_bungalows()

    if distributions:
        print("\nDistributions:")
        for dist in distributions:
            if include_details:
                print(f"ID: {dist['id']}, Number: {dist['numero']}, Bungalow: {dist.get('bungalow_numero', 'N/A')}, Personnel: {dist.get('personnel_nom', 'N/A')} {dist.get('personnel_prenom', '')}, Session: {dist.get('session_numero', 'N/A')}")
            else:
                print(f"ID: {dist['id']}, Number: {dist['numero']}, Bungalow ID: {dist['bungalow_id']}, Personnel ID: {dist['personnel_id']}, Session ID: {dist['session_id']}, Start Date: {dist['date_debut']}, End Date: {dist['date_fin']}")
    else:
        print("No distributions found.")

    db_manager.close()
    input("Press Enter to continue...")


def main():
    """Main entry point for the application"""
    while True:
        choice = display_menu()

        if choice == "0":
            print("Goodbye!")
            sys.exit(0)

        elif choice == "1":
            initialize_database()

        elif choice == "2":
            while True:
                user_choice = user_menu()

                if user_choice == "0":
                    break
                elif user_choice == "1":
                    add_user()
                elif user_choice == "2":
                    get_user()
                elif user_choice == "3":
                    list_users()
                else:
                    print("Invalid choice. Please try again.")

        elif choice == "3":
            while True:
                item_choice = item_menu()

                if item_choice == "0":
                    break
                elif item_choice == "1":
                    add_item()
                elif item_choice == "2":
                    get_item()
                elif item_choice == "3":
                    list_items()
                else:
                    print("Invalid choice. Please try again.")

        elif choice == "5":
            while True:
                personnel_choice = personnel_menu()

                if personnel_choice == "0":
                    break
                elif personnel_choice == "1":
                    add_personnel()
                elif personnel_choice == "2":
                    get_personnel()
                elif personnel_choice == "3":
                    list_personnel()
                else:
                    print("Invalid choice. Please try again.")

        elif choice == "6":
            while True:
                grade_choice = grade_menu()

                if grade_choice == "0":
                    break
                elif grade_choice == "1":
                    add_grade()
                elif grade_choice == "2":
                    get_grade()
                elif grade_choice == "3":
                    list_grades()
                else:
                    print("Invalid choice. Please try again.")

        elif choice == "7":
            while True:
                unite_choice = unite_menu()

                if unite_choice == "0":
                    break
                elif unite_choice == "1":
                    add_unite()
                elif unite_choice == "2":
                    get_unite()
                elif unite_choice == "3":
                    list_unites()
                else:
                    print("Invalid choice. Please try again.")

        elif choice == "8":
            while True:
                bungalow_choice = bungalow_menu()

                if bungalow_choice == "0":
                    break
                elif bungalow_choice == "1":
                    add_bungalow()
                elif bungalow_choice == "2":
                    get_bungalow()
                elif bungalow_choice == "3":
                    list_bungalows()
                elif bungalow_choice == "4":
                    list_bungalows_with_status()
                elif bungalow_choice == "5":
                    list_bungalows_by_session()
                elif bungalow_choice == "6":
                    list_available_bungalows()
                elif bungalow_choice == "7":
                    update_bungalow()
                elif bungalow_choice == "8":
                    delete_bungalow()
                else:
                    print("Invalid choice. Please try again.")

        elif choice == "9":
            while True:
                session_choice = session_menu()

                if session_choice == "0":
                    break
                elif session_choice == "1":
                    add_session()
                elif session_choice == "2":
                    get_session()
                elif session_choice == "3":
                    list_sessions()
                else:
                    print("Invalid choice. Please try again.")

        elif choice == "10":
            while True:
                distribution_choice = distribution_bungalow_menu()

                if distribution_choice == "0":
                    break
                elif distribution_choice == "1":
                    add_distribution_bungalow()
                elif distribution_choice == "2":
                    get_distribution_bungalow()
                elif distribution_choice == "3":
                    list_distribution_bungalows()
                else:
                    print("Invalid choice. Please try again.")

        else:
            print("Invalid choice. Please try again.")


if __name__ == "__main__":
    main()
