#!/usr/bin/env python3
"""
Minimal Flask server for BANGHALAU
"""

from flask import Flask, render_template, redirect, url_for, request, flash, session
from werkzeug.security import check_password_hash
import os

# Create Flask app
app = Flask(__name__)
app.secret_key = 'banghalau_secret_key'

# Simple in-memory user for testing
ADMIN_USER = {
    'username': 'admin',
    'password_hash': 'scrypt:32768:8:1$VCDhsvS0VPzRCcJB$fe219a42bb4d73008addb340ffd4c4db0287a3884757d4104448e53a2b0c7649cc860b33ba8cea9e0ef13216b40b84632c1fde9762291b1031dfa11afe4c66132'
}

@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username == ADMIN_USER['username'] and check_password_hash(ADMIN_USER['password_hash'], password):
            session['user_id'] = 1
            session['username'] = username
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة')
    
    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BANGHALAU - تسجيل الدخول</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        .logo {
            font-size: 3em;
            margin-bottom: 10px;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 1.8em;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .alert {
            background: #f44336;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .credentials {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🏠</div>
        <h1>BANGHALAU</h1>
        <h2>نظام إدارة توزيع البنغالوهات</h2>
        
        <div class="credentials">
            <strong>بيانات تسجيل الدخول:</strong><br>
            👤 اسم المستخدم: admin<br>
            🔑 كلمة المرور: admin123
        </div>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn">🚀 تسجيل الدخول</button>
        </form>
    </div>
</body>
</html>
    '''

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BANGHALAU - لوحة التحكم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .card {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        .card-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 10px;
        }
        .card-description {
            color: #666;
            margin-bottom: 20px;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .success-message {
            background: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 BANGHALAU</h1>
            <h2>مرحباً بك في نظام إدارة توزيع البنغالوهات</h2>
            <p>المستخدم: ''' + session.get('username', 'غير معروف') + '''</p>
        </div>
        
        <div class="success-message">
            ✅ تم تسجيل الدخول بنجاح! النظام يعمل بشكل صحيح.
        </div>
        
        <div class="dashboard-grid">
            <div class="card">
                <div class="card-icon">🏠</div>
                <div class="card-title">إدارة البنغالوهات</div>
                <div class="card-description">إضافة وتعديل وحذف البنغالوهات</div>
                <a href="#" class="btn">إدارة البنغالوهات</a>
            </div>
            
            <div class="card">
                <div class="card-icon">👥</div>
                <div class="card-title">إدارة الأفراد</div>
                <div class="card-description">تسجيل بيانات الأفراد العسكريين</div>
                <a href="#" class="btn">إدارة الأفراد</a>
            </div>
            
            <div class="card">
                <div class="card-icon">🎖️</div>
                <div class="card-title">إدارة الرتب</div>
                <div class="card-description">تنظيم الرتب العسكرية</div>
                <a href="#" class="btn">إدارة الرتب</a>
            </div>
            
            <div class="card">
                <div class="card-icon">🏢</div>
                <div class="card-title">إدارة الوحدات</div>
                <div class="card-description">تنظيم الوحدات العسكرية</div>
                <a href="#" class="btn">إدارة الوحدات</a>
            </div>
            
            <div class="card">
                <div class="card-icon">📋</div>
                <div class="card-title">توزيع البنغالوهات</div>
                <div class="card-description">ربط الأفراد بالبنغالوهات</div>
                <a href="#" class="btn">إدارة التوزيع</a>
            </div>
            
            <div class="card">
                <div class="card-icon">📊</div>
                <div class="card-title">التقارير</div>
                <div class="card-description">عرض الإحصائيات والتقارير</div>
                <a href="#" class="btn">عرض التقارير</a>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/logout" class="btn" style="background: #f44336;">🚪 تسجيل الخروج</a>
        </div>
    </div>
</body>
</html>
    '''

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

if __name__ == '__main__':
    print("🚀 BANGHALAU - Minimal Server Starting...")
    print("📍 URL: http://localhost:5000")
    print("👤 Username: admin")
    print("🔑 Password: admin123")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
