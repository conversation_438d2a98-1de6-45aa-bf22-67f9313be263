"""
Bungalow model for BANGHALAU database
"""

from datetime import datetime


class Bungalow:
    """
    Represents a bungalow in the system
    """
    
    def __init__(self, numero, endroit, capacite=None, caracteristiques=None, bungalow_id=None, created_at=None):
        """
        Initialize a bungalow
        
        Args:
            numero (str): Bungalow number
            endroit (str): Bungalow location
            capacite (int, optional): Bungalow capacity
            caracteristiques (str, optional): Bungalow characteristics
            bungalow_id (int, optional): Bungalow ID
            created_at (datetime, optional): Creation timestamp
        """
        self.id = bungalow_id
        self.numero = numero
        self.endroit = endroit
        self.capacite = capacite
        self.caracteristiques = caracteristiques
        self.created_at = created_at or datetime.now()
    
    @classmethod
    def from_dict(cls, data):
        """
        Create a Bungalow instance from a dictionary
        
        Args:
            data (dict): Bungalow data
            
        Returns:
            Bungalow: Bungalow instance
        """
        return cls(
            numero=data['numero'],
            endroit=data['endroit'],
            capacite=data.get('capacite'),
            caracteristiques=data.get('caracteristiques'),
            bungalow_id=data.get('id'),
            created_at=data.get('created_at')
        )
    
    def to_dict(self):
        """
        Convert the bungalow to a dictionary
        
        Returns:
            dict: Bungalow data
        """
        return {
            'id': self.id,
            'numero': self.numero,
            'endroit': self.endroit,
            'capacite': self.capacite,
            'caracteristiques': self.caracteristiques,
            'created_at': self.created_at
        }
    
    def __str__(self):
        return f"Bungalow(id={self.id}, numero={self.numero}, endroit={self.endroit}, capacite={self.capacite})"
    
    def __repr__(self):
        return self.__str__()
