"""
Category model for BANGHALAU database
"""


class Category:
    """
    Represents a category in the system
    """
    
    def __init__(self, name, description=None, category_id=None):
        """
        Initialize a category
        
        Args:
            name (str): Category name
            description (str, optional): Category description
            category_id (int, optional): Category ID
        """
        self.id = category_id
        self.name = name
        self.description = description
        self.items = []
    
    @classmethod
    def from_dict(cls, data):
        """
        Create a Category instance from a dictionary
        
        Args:
            data (dict): Category data
            
        Returns:
            Category: Category instance
        """
        return cls(
            name=data['name'],
            description=data.get('description'),
            category_id=data.get('id')
        )
    
    def to_dict(self):
        """
        Convert the category to a dictionary
        
        Returns:
            dict: Category data
        """
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description
        }
    
    def add_item(self, item):
        """
        Add an item to the category
        
        Args:
            item (Item): Item to add
        """
        if item not in self.items:
            self.items.append(item)
            item.add_category(self)
    
    def remove_item(self, item):
        """
        Remove an item from the category
        
        Args:
            item (Item): Item to remove
        """
        if item in self.items:
            self.items.remove(item)
            item.remove_category(self)
    
    def __str__(self):
        return f"Category(id={self.id}, name={self.name})"
    
    def __repr__(self):
        return self.__str__()
