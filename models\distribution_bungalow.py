"""
Distribution Bungalow model for BANGHALAU database
"""

from datetime import datetime


class DistributionBungalow:
    """
    Represents a bungalow distribution in the system
    """

    def __init__(self, numero, bungalow_id=None, personnel_id=None, session_id=None, date_debut=None, date_fin=None,
                 notes=None, distribution_id=None, created_at=None):
        """
        Initialize a bungalow distribution

        Args:
            numero (str): Distribution number
            bungalow_id (int, optional): Bungalow ID
            personnel_id (int, optional): Personnel ID
            session_id (int, optional): Session ID
            date_debut (str or datetime, optional): Start date
            date_fin (str or datetime, optional): End date
            notes (str, optional): Notes
            distribution_id (int, optional): Distribution ID
            created_at (datetime, optional): Creation timestamp
        """
        self.id = distribution_id
        self.numero = numero
        self.bungalow_id = bungalow_id
        self.personnel_id = personnel_id
        self.session_id = session_id
        self.date_debut = date_debut
        self.date_fin = date_fin
        self.notes = notes
        self.created_at = created_at or datetime.now()

    @classmethod
    def from_dict(cls, data):
        """
        Create a DistributionBungalow instance from a dictionary

        Args:
            data (dict): Distribution data

        Returns:
            DistributionBungalow: DistributionBungalow instance
        """
        return cls(
            numero=data['numero'],
            bungalow_id=data.get('bungalow_id'),
            personnel_id=data.get('personnel_id'),
            session_id=data.get('session_id'),
            date_debut=data.get('date_debut'),
            date_fin=data.get('date_fin'),
            notes=data.get('notes'),
            distribution_id=data.get('id'),
            created_at=data.get('created_at')
        )

    def to_dict(self):
        """
        Convert the distribution to a dictionary

        Returns:
            dict: Distribution data
        """
        return {
            'id': self.id,
            'numero': self.numero,
            'bungalow_id': self.bungalow_id,
            'personnel_id': self.personnel_id,
            'session_id': self.session_id,
            'date_debut': self.date_debut,
            'date_fin': self.date_fin,
            'notes': self.notes,
            'created_at': self.created_at
        }

    def __str__(self):
        return f"DistributionBungalow(id={self.id}, numero={self.numero}, bungalow_id={self.bungalow_id}, personnel_id={self.personnel_id}, session_id={self.session_id})"

    def __repr__(self):
        return self.__str__()
