"""
Grade model for BANGHALAU database
"""

from datetime import datetime


class Grade:
    """
    Represents a military grade/rank in the system
    """
    
    def __init__(self, numero, grade, description=None, grade_id=None, created_at=None):
        """
        Initialize a grade
        
        Args:
            numero (str): Grade number
            grade (str): Grade name
            description (str, optional): Grade description
            grade_id (int, optional): Grade ID
            created_at (datetime, optional): Creation timestamp
        """
        self.id = grade_id
        self.numero = numero
        self.grade = grade
        self.description = description
        self.created_at = created_at or datetime.now()
    
    @classmethod
    def from_dict(cls, data):
        """
        Create a Grade instance from a dictionary
        
        Args:
            data (dict): Grade data
            
        Returns:
            Grade: Grade instance
        """
        return cls(
            numero=data['numero'],
            grade=data['grade'],
            description=data.get('description'),
            grade_id=data.get('id'),
            created_at=data.get('created_at')
        )
    
    def to_dict(self):
        """
        Convert the grade to a dictionary
        
        Returns:
            dict: Grade data
        """
        return {
            'id': self.id,
            'numero': self.numero,
            'grade': self.grade,
            'description': self.description,
            'created_at': self.created_at
        }
    
    def __str__(self):
        return f"Grade(id={self.id}, numero={self.numero}, grade={self.grade})"
    
    def __repr__(self):
        return self.__str__()
