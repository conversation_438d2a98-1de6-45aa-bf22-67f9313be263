"""
Item model for BANGHALAU database
"""

from datetime import datetime


class Item:
    """
    Represents an item in the system
    """
    
    def __init__(self, name, description=None, price=None, user_id=None, 
                 item_id=None, created_at=None):
        """
        Initialize an item
        
        Args:
            name (str): Item name
            description (str, optional): Item description
            price (float, optional): Item price
            user_id (int, optional): User ID
            item_id (int, optional): Item ID
            created_at (datetime, optional): Creation timestamp
        """
        self.id = item_id
        self.name = name
        self.description = description
        self.price = price
        self.user_id = user_id
        self.created_at = created_at or datetime.now()
        self.categories = []
    
    @classmethod
    def from_dict(cls, data):
        """
        Create an Item instance from a dictionary
        
        Args:
            data (dict): Item data
            
        Returns:
            Item: Item instance
        """
        return cls(
            name=data['name'],
            description=data.get('description'),
            price=data.get('price'),
            user_id=data.get('user_id'),
            item_id=data.get('id'),
            created_at=data.get('created_at')
        )
    
    def to_dict(self):
        """
        Convert the item to a dictionary
        
        Returns:
            dict: Item data
        """
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'price': self.price,
            'user_id': self.user_id,
            'created_at': self.created_at
        }
    
    def add_category(self, category):
        """
        Add a category to the item
        
        Args:
            category (Category): Category to add
        """
        if category not in self.categories:
            self.categories.append(category)
    
    def remove_category(self, category):
        """
        Remove a category from the item
        
        Args:
            category (Category): Category to remove
        """
        if category in self.categories:
            self.categories.remove(category)
    
    def __str__(self):
        return f"Item(id={self.id}, name={self.name}, price={self.price})"
    
    def __repr__(self):
        return self.__str__()
