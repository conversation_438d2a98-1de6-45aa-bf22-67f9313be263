"""
Personnel Militaire model for BANGHALAU database
"""

from datetime import datetime


class PersonnelMilitaire:
    """
    Represents a military personnel in the system
    """

    def __init__(self, matricule, nom, prenom, grade_id=None, unite_id=None,
                 numero=None, personnel_id=None, region=None, created_at=None):
        """
        Initialize a military personnel

        Args:
            matricule (str): Matricule (unique identifier)
            nom (str): Last name
            prenom (str): First name
            grade_id (int, optional): ID of the military rank
            unite_id (int, optional): ID of the unit
            numero (str, optional): Number
            personnel_id (int, optional): Personnel ID
            region (str, optional): Military region
            created_at (datetime, optional): Creation timestamp
        """
        self.id = personnel_id
        self.numero = numero
        self.matricule = matricule
        self.nom = nom
        self.prenom = prenom
        self.grade_id = grade_id
        self.unite_id = unite_id
        self.region = region
        self.created_at = created_at or datetime.now()

    @classmethod
    def from_dict(cls, data):
        """
        Create a PersonnelMilitaire instance from a dictionary

        Args:
            data (dict): Personnel data

        Returns:
            PersonnelMilitaire: PersonnelMilitaire instance
        """
        return cls(
            matricule=data['matricule'],
            nom=data['nom'],
            prenom=data['prenom'],
            grade_id=data.get('grade_id'),
            unite_id=data.get('unite_id'),
            numero=data.get('numero'),
            personnel_id=data.get('id'),
            region=data.get('region'),
            created_at=data.get('created_at')
        )

    def to_dict(self):
        """
        Convert the personnel to a dictionary

        Returns:
            dict: Personnel data
        """
        return {
            'id': self.id,
            'numero': self.numero,
            'matricule': self.matricule,
            'nom': self.nom,
            'prenom': self.prenom,
            'grade_id': self.grade_id,
            'unite_id': self.unite_id,
            'region': self.region,
            'created_at': self.created_at
        }

    def __str__(self):
        return f"PersonnelMilitaire(id={self.id}, matricule={self.matricule}, nom={self.nom}, prenom={self.prenom}, grade_id={self.grade_id}, unite_id={self.unite_id}, region={self.region})"

    def __repr__(self):
        return self.__str__()
