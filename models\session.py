"""
Session model for BANGHALAU database
"""

from datetime import datetime


class Session:
    """
    Represents a session in the system
    """
    
    def __init__(self, numero, date_debut, description=None, date_fin=None, etat_session=None, session_id=None, created_at=None):
        """
        Initialize a session
        
        Args:
            numero (str): Session number
            date_debut (str or datetime): Session start date
            description (str, optional): Session description
            date_fin (str or datetime, optional): Session end date
            etat_session (str, optional): Session state
            session_id (int, optional): Session ID
            created_at (datetime, optional): Creation timestamp
        """
        self.id = session_id
        self.numero = numero
        self.description = description
        self.date_debut = date_debut
        self.date_fin = date_fin
        self.etat_session = etat_session
        self.created_at = created_at or datetime.now()
    
    @classmethod
    def from_dict(cls, data):
        """
        Create a Session instance from a dictionary
        
        Args:
            data (dict): Session data
            
        Returns:
            Session: Session instance
        """
        return cls(
            numero=data['numero'],
            date_debut=data['date_debut'],
            description=data.get('description'),
            date_fin=data.get('date_fin'),
            etat_session=data.get('etat_session'),
            session_id=data.get('id'),
            created_at=data.get('created_at')
        )
    
    def to_dict(self):
        """
        Convert the session to a dictionary
        
        Returns:
            dict: Session data
        """
        return {
            'id': self.id,
            'numero': self.numero,
            'description': self.description,
            'date_debut': self.date_debut,
            'date_fin': self.date_fin,
            'etat_session': self.etat_session,
            'created_at': self.created_at
        }
    
    def __str__(self):
        return f"Session(id={self.id}, numero={self.numero}, date_debut={self.date_debut}, date_fin={self.date_fin}, etat_session={self.etat_session})"
    
    def __repr__(self):
        return self.__str__()
