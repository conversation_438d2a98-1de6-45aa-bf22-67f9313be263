"""
Unite model for BANGHALAU database
"""

from datetime import datetime


class Unite:
    """
    Represents a military unit/position in the system
    """
    
    def __init__(self, numero, description, raccourci=None, unite_id=None, created_at=None):
        """
        Initialize a unit/position
        
        Args:
            numero (str): Unit number
            description (str): Unit description
            raccourci (str, optional): Unit shortcut/abbreviation
            unite_id (int, optional): Unit ID
            created_at (datetime, optional): Creation timestamp
        """
        self.id = unite_id
        self.numero = numero
        self.description = description
        self.raccourci = raccourci
        self.created_at = created_at or datetime.now()
    
    @classmethod
    def from_dict(cls, data):
        """
        Create a Unite instance from a dictionary
        
        Args:
            data (dict): Unite data
            
        Returns:
            Unite: Unite instance
        """
        return cls(
            numero=data['numero'],
            description=data['description'],
            raccourci=data.get('raccourci'),
            unite_id=data.get('id'),
            created_at=data.get('created_at')
        )
    
    def to_dict(self):
        """
        Convert the unit to a dictionary
        
        Returns:
            dict: Unit data
        """
        return {
            'id': self.id,
            'numero': self.numero,
            'description': self.description,
            'raccourci': self.raccourci,
            'created_at': self.created_at
        }
    
    def __str__(self):
        return f"Unite(id={self.id}, numero={self.numero}, description={self.description}, raccourci={self.raccourci})"
    
    def __repr__(self):
        return self.__str__()
