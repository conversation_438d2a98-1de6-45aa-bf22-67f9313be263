"""
User model for BANGHALAU database
"""

from datetime import datetime


class User:
    """
    Represents a user in the system
    """
    
    def __init__(self, username, email, user_id=None, created_at=None):
        """
        Initialize a user
        
        Args:
            username (str): Username
            email (str): Email address
            user_id (int, optional): User ID
            created_at (datetime, optional): Creation timestamp
        """
        self.id = user_id
        self.username = username
        self.email = email
        self.created_at = created_at or datetime.now()
    
    @classmethod
    def from_dict(cls, data):
        """
        Create a User instance from a dictionary
        
        Args:
            data (dict): User data
            
        Returns:
            User: User instance
        """
        return cls(
            username=data['username'],
            email=data['email'],
            user_id=data.get('id'),
            created_at=data.get('created_at')
        )
    
    def to_dict(self):
        """
        Convert the user to a dictionary
        
        Returns:
            dict: User data
        """
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'created_at': self.created_at
        }
    
    def __str__(self):
        return f"User(id={self.id}, username={self.username}, email={self.email})"
    
    def __repr__(self):
        return self.__str__()
