#!/usr/bin/env python3
"""
Test different ports for BANGHALAU
"""

import socket
from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>BANGHALAU - اختبار الاتصال</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                text-align: center; 
                padding: 50px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }
            .container {
                background: rgba(255,255,255,0.1);
                padding: 30px;
                border-radius: 15px;
                backdrop-filter: blur(10px);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎉 BANGHALAU يعمل بنجاح!</h1>
            <h2>✅ الاتصال تم بنجاح</h2>
            <p>الخادم يعمل على هذا المنفذ</p>
            <p>👤 اسم المستخدم: admin</p>
            <p>🔑 كلمة المرور: admin123</p>
        </div>
    </body>
    </html>
    '''

def find_free_port():
    """Find a free port"""
    for port in [5000, 8000, 8080, 3000, 4000]:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind(('localhost', port))
            sock.close()
            return port
        except OSError:
            continue
    return 9999

if __name__ == '__main__':
    port = find_free_port()
    print(f"🚀 Starting server on port {port}")
    print(f"🌐 URL: http://localhost:{port}")
    
    app.run(host='0.0.0.0', port=port, debug=True)
