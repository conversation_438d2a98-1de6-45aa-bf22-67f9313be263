#!/usr/bin/env python3
"""
Quick HTTP server to test if the application works
"""

import http.server
import socketserver
import webbrowser
import threading
import time

PORT = 8080

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html_content = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BANGHALAU - نظام إدارة البنغالوهات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
        }
        .status {
            background: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 1.2em;
        }
        .info {
            background: #2196F3;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .credentials {
            background: #FF9800;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 BANGHALAU</h1>
        <h2>نظام إدارة توزيع البنغالوهات</h2>
        
        <div class="status">
            ✅ الخادم يعمل بنجاح!
        </div>
        
        <div class="info">
            📍 عنوان الخادم: http://localhost:8080<br>
            🌐 حالة الاتصال: متصل
        </div>
        
        <div class="credentials">
            🔐 بيانات تسجيل الدخول:<br>
            👤 اسم المستخدم: admin<br>
            🔑 كلمة المرور: admin123
        </div>
        
        <p>هذا خادم اختبار للتأكد من أن التطبيق يعمل بشكل صحيح.</p>
        
        <a href="http://localhost:5000" class="btn">🚀 الانتقال إلى التطبيق الرئيسي</a>
        
        <div style="margin-top: 30px; font-size: 0.9em; color: #666;">
            إذا كان هذا الخادم يعمل، فهذا يعني أن Python والشبكة يعملان بشكل صحيح.
        </div>
    </div>
</body>
</html>
            """
            
            self.wfile.write(html_content.encode('utf-8'))
        else:
            super().do_GET()

def start_server():
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"🌐 Test server running at http://localhost:{PORT}")
        print("🔍 This will help us verify if the server works...")
        httpd.serve_forever()

def open_browser():
    time.sleep(2)
    webbrowser.open(f'http://localhost:{PORT}')

if __name__ == "__main__":
    print("🚀 Starting test server...")
    
    # Start browser in a separate thread
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # Start the server
    start_server()
