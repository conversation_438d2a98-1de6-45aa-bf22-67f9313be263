#!/usr/bin/env python
"""
Script to rebuild the database structure
"""

import os
import sqlite3


def rebuild_database():
    """Rebuild the database structure"""
    print("Rebuilding database structure...")

    # Remove existing database file
    if os.path.exists("banghalau.db"):
        os.remove("banghalau.db")
        print("Removed existing database file.")

    # Create new database
    conn = sqlite3.connect("banghalau.db")
    cursor = conn.cursor()

    # Create tables in the correct order

    # Create users table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        email TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create items table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        price REAL,
        user_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')

    # Create categories table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT
    )
    ''')

    # Create item_categories table (many-to-many relationship)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS item_categories (
        item_id INTEGER,
        category_id INTEGER,
        PRIMARY KEY (item_id, category_id),
        FOREIGN KEY (item_id) REFERENCES items (id),
        FOREIGN KEY (category_id) REFERENCES categories (id)
    )
    ''')

    # Create Grades table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS grades (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        numero TEXT NOT NULL UNIQUE,
        grade TEXT NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create Personnel Militaire Et Similaires table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS personnel_militaire (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        numero TEXT,
        matricule TEXT NOT NULL UNIQUE,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        grade_id INTEGER,
        unite_id INTEGER,
        region TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (grade_id) REFERENCES grades (id),
        FOREIGN KEY (unite_id) REFERENCES unites (id)
    )
    ''')

    # Create Unités/Positions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS unites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        numero TEXT NOT NULL UNIQUE,
        description TEXT NOT NULL,
        raccourci TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create Bungalows table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS bungalows (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        numero TEXT NOT NULL UNIQUE,
        endroit TEXT NOT NULL,
        capacite INTEGER,
        caracteristiques TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create Sessions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        numero TEXT NOT NULL UNIQUE,
        description TEXT,
        date_debut DATE NOT NULL,
        date_fin DATE,
        etat_session TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create Distribution Bungalows table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS distribution_bungalows (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        numero TEXT NOT NULL UNIQUE,
        bungalow_id INTEGER,
        personnel_id INTEGER,
        session_id INTEGER,
        date_debut DATE,
        date_fin DATE,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (bungalow_id) REFERENCES bungalows (id),
        FOREIGN KEY (personnel_id) REFERENCES personnel_militaire (id),
        FOREIGN KEY (session_id) REFERENCES sessions (id)
    )
    ''')

    conn.commit()
    conn.close()

    print("Database structure rebuilt successfully.")


if __name__ == "__main__":
    rebuild_database()
