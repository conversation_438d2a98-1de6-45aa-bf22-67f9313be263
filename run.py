#!/usr/bin/env python3
"""
Production runner for BANGHALAU application
"""

import os
import sys
from app import app

def main():
    """Main entry point for production deployment"""

    # Initialize database if it doesn't exist
    from database.db_manager import DatabaseManager
    from database.operations import DatabaseOperations

    try:
        db_manager = DatabaseManager()
        db_manager.connect()
        db_manager.initialize_database()

        # Create default admin user if no users exist
        db_ops = DatabaseOperations(db_manager)
        users = db_ops.list_users()

        if not users:
            from werkzeug.security import generate_password_hash
            admin_password = generate_password_hash('admin123')
            user_id = db_ops.create_user('admin', admin_password, '<EMAIL>')
            if user_id:
                print("Default admin user created: admin/admin123")
            else:
                print("Failed to create default admin user")

        db_manager.close()

    except Exception as e:
        print(f"Database initialization error: {e}")
        sys.exit(1)

    # Get port from environment or use default
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '0.0.0.0')
    debug = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

    print(f"Starting BANGHALAU server on {host}:{port}")
    print(f"Debug mode: {debug}")
    print("Access the application at: http://localhost:5000")

    # Run the application
    app.run(host=host, port=port, debug=debug)

if __name__ == '__main__':
    main()
