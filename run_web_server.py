#!/usr/bin/env python3
"""
BANGHALAU Web Server - Enhanced startup script
"""

import os
import sys
import time
import webbrowser
import threading
import socket

def print_banner():
    print("=" * 70)
    print("🏠 BANGHALAU - نظام إدارة توزيع البنغالوهات (خادم الويب)")
    print("=" * 70)
    print("🚀 بدء تشغيل خادم الويب...")
    print()

def check_requirements():
    """Check if required packages are available"""
    try:
        import flask
        print("✅ Flask متوفر - الإصدار:", flask.__version__)
        
        import sqlite3
        print("✅ SQLite متوفر - الإصدار:", sqlite3.sqlite_version)
        
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        return False

def check_database():
    """Check if database exists and is accessible"""
    try:
        if os.path.exists('banghalau.db'):
            print("✅ قاعدة البيانات موجودة")
            return True
        else:
            print("⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها تلقائياً")
            return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def find_available_port():
    """Find an available port"""
    for port in [5000, 8000, 8080, 3000, 4000, 9000, 7000]:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            if result != 0:  # Port is available
                return port
        except:
            continue
    return 5000  # Default fallback

def open_browser_delayed(port):
    """Open browser after a delay"""
    time.sleep(3)
    print("🌐 فتح المتصفح...")
    webbrowser.open(f'http://localhost:{port}')

def main():
    print_banner()
    
    # Check requirements
    if not check_requirements():
        print("⚠️ يرجى تثبيت المتطلبات أولاً:")
        print("   pip install flask")
        return
    
    # Check database
    if not check_database():
        print("❌ مشكلة في قاعدة البيانات")
        return
    
    # Find available port
    port = find_available_port()
    
    try:
        # Import the Flask app
        print("📦 تحميل التطبيق...")
        from app import app
        print("✅ تم تحميل التطبيق بنجاح")
        
        # Setup database if needed
        print("🗄️ التحقق من قاعدة البيانات...")
        try:
            from database.db_manager import DatabaseManager
            from database.operations import DatabaseOperations
            
            db_manager = DatabaseManager()
            db_manager.connect()
            
            # Check for admin user
            db_ops = DatabaseOperations(db_manager)
            users = db_ops.list_users()
            
            if users:
                print("✅ قاعدة البيانات جاهزة مع المستخدمين الموجودين")
                admin_user = next((u for u in users if u.get('username') == 'admin'), None)
                if admin_user:
                    print("✅ المستخدم الإداري موجود")
                else:
                    print("⚠️ المستخدم الإداري غير موجود")
            else:
                print("⚠️ لم يتم العثور على مستخدمين")
                
            db_manager.close()
            
        except Exception as e:
            print(f"⚠️ تحذير في قاعدة البيانات: {e}")
        
        # Print access information
        print()
        print("🌐 معلومات الوصول:")
        print(f"   📍 الرابط: http://localhost:{port}")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        print()
        print("🔄 بدء خادم الويب...")
        print("   (اضغط Ctrl+C لإيقاف الخادم)")
        print("=" * 70)
        
        # Start browser in background
        browser_thread = threading.Thread(target=open_browser_delayed, args=(port,))
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start the Flask server
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        
        # Try alternative startup
        print("\n🔄 محاولة تشغيل بديلة...")
        try:
            from app import app
            print("🌐 تشغيل الخادم على المنفذ الافتراضي...")
            app.run(host='127.0.0.1', port=5000, debug=True)
        except Exception as e2:
            print(f"❌ فشل في التشغيل البديل: {e2}")

if __name__ == '__main__':
    main()
