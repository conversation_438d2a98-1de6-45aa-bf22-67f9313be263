// Functions to show different content sections
function showDashboard() {
    $('#dashboardContent').show();
    $('#bungalowsContent, #bungalowStatusContent, #personnelContent, #distributionsContent, #newDistributionContent, #addPersonnelContent, #gradesContent').hide();
    $('#sidebar ul li').removeClass('active');
    $('#sidebar ul li:first-child').addClass('active');
    console.log("Dashboard shown");
}

function showBungalows() {
    $('#bungalowsContent').show();
    $('#dashboardContent, #bungalowStatusContent, #personnelContent, #distributionsContent, #newDistributionContent, #addPersonnelContent, #gradesContent').hide();
    $('#sidebar ul li').removeClass('active');
    $('#sidebar ul li:nth-child(2)').addClass('active');
    console.log("Bungalows shown");
}

function showBungalowStatus() {
    $('#bungalowStatusContent').show();
    $('#dashboardContent, #bungalowsContent, #personnelContent, #distributionsContent, #newDistributionContent, #addPersonnelContent, #gradesContent').hide();
    $('#sidebar ul li').removeClass('active');
    $('#sidebar ul li:nth-child(2)').addClass('active');
    console.log("Bungalow Status shown");
}

function showPersonnel() {
    $('#personnelContent').show();
    $('#dashboardContent, #bungalowsContent, #bungalowStatusContent, #distributionsContent, #newDistributionContent, #addPersonnelContent, #gradesContent').hide();
    $('#sidebar ul li').removeClass('active');
    $('#sidebar ul li:nth-child(3)').addClass('active');
    console.log("Personnel shown");
}

function showAddPersonnel() {
    $('#addPersonnelContent').show();
    $('#dashboardContent, #bungalowsContent, #bungalowStatusContent, #personnelContent, #distributionsContent, #newDistributionContent, #gradesContent').hide();
    $('#sidebar ul li').removeClass('active');
    $('#sidebar ul li:nth-child(3)').addClass('active');
    console.log("Add Personnel shown");
}

function showGrades() {
    $('#gradesContent').show();
    $('#dashboardContent, #bungalowsContent, #bungalowStatusContent, #personnelContent, #distributionsContent, #newDistributionContent, #addPersonnelContent, #unitesContent').hide();
    $('#sidebar ul li').removeClass('active');
    $('#sidebar ul li:nth-child(3)').addClass('active');
    console.log("Grades shown");
}

function showUnites() {
    $('#unitesContent').show();
    $('#dashboardContent, #bungalowsContent, #bungalowStatusContent, #personnelContent, #distributionsContent, #newDistributionContent, #addPersonnelContent, #gradesContent').hide();
    $('#sidebar ul li').removeClass('active');
    $('#sidebar ul li:nth-child(3)').addClass('active');
    console.log("Unites shown");
}

function showDistributions() {
    $('#distributionsContent').show();
    $('#dashboardContent, #bungalowsContent, #bungalowStatusContent, #personnelContent, #newDistributionContent, #addPersonnelContent, #gradesContent').hide();
    $('#sidebar ul li').removeClass('active');
    $('#sidebar ul li:nth-child(4)').addClass('active');
    console.log("Distributions shown");
}

function showNewDistribution() {
    $('#newDistributionContent').show();
    $('#dashboardContent, #bungalowsContent, #bungalowStatusContent, #personnelContent, #distributionsContent, #addPersonnelContent, #gradesContent').hide();
    $('#sidebar ul li').removeClass('active');
    $('#sidebar ul li:nth-child(4)').addClass('active');
    console.log("New Distribution shown");
}

// Helper function to get location name in Arabic
function getLocationName(location) {
    const locations = {
        'Beach': 'شاطئ',
        'Mountain': 'جبل',
        'Forest': 'غابة',
        'Lake': 'بحيرة'
    };

    return locations[location] || location;
}

// Function to update occupancy statistics
function updateOccupancyStatistics() {
    // Count occupied and available bungalows
    const totalBungalows = $('#bungalowStatusTableBody tr').length;
    const occupiedBungalows = $('#bungalowStatusTableBody tr.table-danger').length;
    const availableBungalows = totalBungalows - occupiedBungalows;
    const occupancyRate = Math.round((occupiedBungalows / totalBungalows) * 100);

    // Update statistics
    $('#totalBungalows').text(totalBungalows);
    $('#occupiedBungalows').text(occupiedBungalows);
    $('#availableBungalows').text(availableBungalows);
    $('#occupancyRate').text(occupancyRate + '%');

    // Update chart
    if (window.occupancyChart) {
        window.occupancyChart.data.datasets[0].data = [occupiedBungalows, availableBungalows];
        window.occupancyChart.update();
    }
}

// Document ready function
$(document).ready(function() {
    // Sidebar toggle
    $('#sidebarCollapse').on('click', function() {
        $('#sidebar').toggleClass('active');
        $('#content').toggleClass('active');
    });

    // Initialize charts
    if (document.getElementById('bungalowStatusChart')) {
        const bungalowStatusCtx = document.getElementById('bungalowStatusChart').getContext('2d');
        new Chart(bungalowStatusCtx, {
            type: 'doughnut',
            data: {
                labels: ['مشغول', 'متاح'],
                datasets: [{
                    data: [67, 33],
                    backgroundColor: ['#dc3545', '#28a745'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    if (document.getElementById('distributionByRankChart')) {
        const distributionByRankCtx = document.getElementById('distributionByRankChart').getContext('2d');
        new Chart(distributionByRankCtx, {
            type: 'bar',
            data: {
                labels: ['ملازم', 'نقيب', 'رائد', 'مقدم', 'عقيد', 'عميد'],
                datasets: [{
                    label: 'عدد التوزيعات',
                    data: [12, 19, 8, 5, 2, 1],
                    backgroundColor: '#17a2b8',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Initialize occupancy chart for bungalow status page
    const occupancyCtx = document.getElementById('occupancyChart');
    if (occupancyCtx) {
        window.occupancyChart = new Chart(occupancyCtx.getContext('2d'), {
            type: 'pie',
            data: {
                labels: ['مشغول', 'متاح'],
                datasets: [{
                    data: [8, 4],
                    backgroundColor: ['#dc3545', '#28a745'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Initialize date picker with today's date
        const today = new Date().toISOString().split('T')[0];
        $('#statusDate').val(today);

        // Initialize statistics
        updateOccupancyStatistics();
    }

    // Initialize quick add personnel form with default values
    if ($('#quick_personnel_numero').length) {
        $('#quick_personnel_numero').val(`P${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`);
    }

    // Initialize new personnel form with default values
    if ($('#new_personnel_numero').length) {
        $('#new_personnel_numero').val(`P${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`);
    }

    // Generate new personnel number
    $('#generateNumeroBtn').click(function() {
        $('#new_personnel_numero').val(`P${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`);
    });

    // Show dashboard by default
    showDashboard();

    // Form submissions and other event handlers

    // Quick Add Personnel Form Submission
    $('#quickAddPersonnelForm').submit(function(e) {
        e.preventDefault();

        // Get form values
        const numero = $('#quick_personnel_numero').val();
        const matricule = $('#quick_personnel_matricule').val();
        const nom = $('#quick_personnel_nom').val();
        const prenom = $('#quick_personnel_prenom').val();
        const grade = $('#quick_personnel_grade').val();
        const unite = $('#quick_personnel_unite').val();

        // Create new personnel row for the personnel table
        const newPersonnelRow = `
            <tr class="personnel-row"
                data-id="new"
                data-numero="${numero}"
                data-matricule="${matricule}"
                data-nom="${nom}"
                data-prenom="${prenom}"
                data-grade="${grade}"
                data-unite="${unite}">
                <td>${numero}</td>
                <td>${matricule}</td>
                <td>${nom} ${prenom}</td>
                <td>${grade}</td>
                <td>${unite}</td>
                <td>
                    <span class="badge bg-secondary">غير مخصص</span>
                </td>
                <td>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-info view-personnel-btn" data-id="new" data-bs-toggle="modal" data-bs-target="#viewPersonnelModal">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-warning edit-personnel-btn" data-id="new" data-bs-toggle="modal" data-bs-target="#editPersonnelModal">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-personnel-btn" data-id="new" data-bs-toggle="modal" data-bs-target="#deletePersonnelModal">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-success assign-personnel-btn" data-id="new" data-bs-toggle="modal" data-bs-target="#assignPersonnelModal">
                            <i class="fas fa-home"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;

        // Add the new personnel row to the personnel table
        $('#personnelTable tbody').prepend(newPersonnelRow);

        // Reset form
        $('#quickAddPersonnelForm')[0].reset();
        $('#quick_personnel_numero').val(`P${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`);

        // Show success message
        alert('تمت إضافة الفرد العسكري بنجاح');

        // Optionally switch to personnel page
        if (confirm('هل تريد الانتقال إلى صفحة الأفراد العسكريين؟')) {
            showPersonnel();
        }
    });

    // Add New Personnel Form Submission
    $('#addNewPersonnelForm').submit(function(e) {
        e.preventDefault();

        // Get form values
        const numero = $('#new_personnel_numero').val();
        const matricule = $('#new_personnel_matricule').val();
        const nom = $('#new_personnel_nom').val();
        const prenom = $('#new_personnel_prenom').val();
        const grade = $('#new_personnel_grade').val();
        const unite = $('#new_personnel_unite').val();
        const dateNaissance = $('#new_personnel_date_naissance').val();
        const telephone = $('#new_personnel_telephone').val();
        const adresse = $('#new_personnel_adresse').val();
        const notes = $('#new_personnel_notes').val();
        const active = $('#new_personnel_active').is(':checked');

        // Create new personnel row for the personnel table
        const newPersonnelRow = `
            <tr class="personnel-row"
                data-id="new"
                data-numero="${numero}"
                data-matricule="${matricule}"
                data-nom="${nom}"
                data-prenom="${prenom}"
                data-grade="${grade}"
                data-unite="${unite}"
                data-date-naissance="${dateNaissance}"
                data-telephone="${telephone}"
                data-adresse="${adresse}"
                data-notes="${notes}"
                data-active="${active}">
                <td>${numero}</td>
                <td>${matricule}</td>
                <td>${nom} ${prenom}</td>
                <td>${grade}</td>
                <td>${unite}</td>
                <td>
                    <span class="badge bg-secondary">غير مخصص</span>
                </td>
                <td>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-info view-personnel-btn" data-id="new" data-bs-toggle="modal" data-bs-target="#viewPersonnelModal">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-warning edit-personnel-btn" data-id="new" data-bs-toggle="modal" data-bs-target="#editPersonnelModal">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-personnel-btn" data-id="new" data-bs-toggle="modal" data-bs-target="#deletePersonnelModal">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-success assign-personnel-btn" data-id="new" data-bs-toggle="modal" data-bs-target="#assignPersonnelModal">
                            <i class="fas fa-home"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;

        // Add the new personnel row to the personnel table
        $('#personnelTable tbody').prepend(newPersonnelRow);

        // Reset form
        $('#addNewPersonnelForm')[0].reset();
        $('#new_personnel_numero').val(`P${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`);

        // Show success message
        alert('تمت إضافة الفرد العسكري بنجاح');

        // Switch to personnel page
        showPersonnel();
    });

    // Add Grade Form Submission
    $('#addGradeForm').submit(function(e) {
        e.preventDefault();

        // Get form values
        const name = $('#grade_name').val();
        const code = $('#grade_code').val();
        const priority = $('#grade_priority').val();
        const category = $('#grade_category').val();
        const color = $('#grade_color').val();
        const icon = $('#grade_icon').val();
        const description = $('#grade_description').val();
        const active = $('#grade_active').is(':checked');

        // Generate a new ID (in a real app, this would come from the server)
        const newId = $('#gradesTableBody tr').length + 1;

        // Create new grade row for the grades table
        const newGradeRow = `
            <tr class="grade-row"
                data-id="${newId}"
                data-name="${name}"
                data-code="${code}"
                data-category="${category}"
                data-priority="${priority}"
                data-color="${color}"
                data-icon="${icon}"
                data-description="${description}"
                data-active="${active}">
                <td>${newId}</td>
                <td>${name}</td>
                <td>${code}</td>
                <td>${category}</td>
                <td>${priority}</td>
                <td>${icon ? `<i class="fas ${icon}" style="color: ${color};"></i>` : ''}</td>
                <td><span class="badge ${active ? 'bg-success' : 'bg-danger'}">${active ? 'نشط' : 'غير نشط'}</span></td>
                <td>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-warning edit-grade-btn" data-id="${newId}" data-bs-toggle="modal" data-bs-target="#editGradeModal">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-grade-btn" data-id="${newId}" data-bs-toggle="modal" data-bs-target="#deleteGradeModal">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;

        // Add the new grade row to the grades table
        $('#gradesTableBody').append(newGradeRow);

        // Reset form
        $('#addGradeForm')[0].reset();

        // Close modal
        $('#addGradeModal').modal('hide');

        // Show success message
        alert('تمت إضافة الرتبة بنجاح');

        // Update grade options in personnel forms
        updateGradeOptions();
    });

    // Edit Grade Button Click
    $(document).on('click', '.edit-grade-btn', function() {
        const id = $(this).data('id');
        const row = $(`.grade-row[data-id="${id}"]`);

        // Fill the edit form with the grade data
        $('#edit_grade_id').val(id);
        $('#edit_grade_name').val(row.data('name'));
        $('#edit_grade_code').val(row.data('code'));
        $('#edit_grade_category').val(row.data('category'));
        $('#edit_grade_priority').val(row.data('priority'));
        $('#edit_grade_color').val(row.data('color'));
        $('#edit_grade_icon').val(row.data('icon'));
        $('#edit_grade_description').val(row.data('description'));
        $('#edit_grade_active').prop('checked', row.data('active') === 'true');
    });

    // Edit Grade Form Submission
    $('#editGradeForm').submit(function(e) {
        e.preventDefault();

        // Get form values
        const id = $('#edit_grade_id').val();
        const name = $('#edit_grade_name').val();
        const code = $('#edit_grade_code').val();
        const category = $('#edit_grade_category').val();
        const priority = $('#edit_grade_priority').val();
        const color = $('#edit_grade_color').val();
        const icon = $('#edit_grade_icon').val();
        const description = $('#edit_grade_description').val();
        const active = $('#edit_grade_active').is(':checked');

        // Update the grade row in the table
        const row = $(`.grade-row[data-id="${id}"]`);
        row.data('name', name);
        row.data('code', code);
        row.data('category', category);
        row.data('priority', priority);
        row.data('color', color);
        row.data('icon', icon);
        row.data('description', description);
        row.data('active', active);

        row.find('td:nth-child(2)').text(name);
        row.find('td:nth-child(3)').text(code);
        row.find('td:nth-child(4)').text(category);
        row.find('td:nth-child(5)').text(priority);
        row.find('td:nth-child(6)').html(icon ? `<i class="fas ${icon}" style="color: ${color};"></i>` : '');
        row.find('td:nth-child(7)').html(`<span class="badge ${active ? 'bg-success' : 'bg-danger'}">${active ? 'نشط' : 'غير نشط'}</span>`);

        // Close modal
        $('#editGradeModal').modal('hide');

        // Show success message
        alert('تم تحديث الرتبة بنجاح');

        // Update grade options in personnel forms
        updateGradeOptions();
    });

    // Delete Grade Button Click
    $(document).on('click', '.delete-grade-btn', function() {
        const id = $(this).data('id');
        const row = $(`.grade-row[data-id="${id}"]`);

        // Set the grade name in the confirmation message
        $('#delete_grade_name').text(row.data('name'));

        // Set the grade ID to be deleted
        $('#confirmDeleteGradeBtn').data('id', id);
    });

    // Confirm Delete Grade Button Click
    $('#confirmDeleteGradeBtn').click(function() {
        const id = $(this).data('id');

        // Remove the grade row from the table
        $(`.grade-row[data-id="${id}"]`).remove();

        // Close modal
        $('#deleteGradeModal').modal('hide');

        // Show success message
        alert('تم حذف الرتبة بنجاح');

        // Update grade options in personnel forms
        updateGradeOptions();
    });

    // Function to update grade options in personnel forms
    function updateGradeOptions() {
        // Get all grades
        const grades = [];
        $('.grade-row').each(function() {
            grades.push({
                id: $(this).data('id'),
                name: $(this).data('name')
            });
        });

        // Sort grades by priority
        grades.sort((a, b) => {
            const priorityA = $(`.grade-row[data-id="${a.id}"]`).data('priority');
            const priorityB = $(`.grade-row[data-id="${b.id}"]`).data('priority');
            return priorityA - priorityB;
        });

        // Clear and update grade options in all forms
        const gradeSelects = $('#personnel_grade, #edit_personnel_grade, #new_personnel_grade');
        const currentValues = {};

        // Save current values
        gradeSelects.each(function() {
            currentValues[$(this).attr('id')] = $(this).val();
        });

        // Clear options except the first one (placeholder)
        gradeSelects.find('option:not(:first)').remove();

        // Add new options
        grades.forEach(grade => {
            gradeSelects.append(`<option value="${grade.name}">${grade.name}</option>`);
        });

        // Restore selected values
        gradeSelects.each(function() {
            const value = currentValues[$(this).attr('id')];
            if (value) {
                $(this).val(value);
            }
        });
    }

    // Add Unite Form Submission
    $('#addUniteForm').submit(function(e) {
        e.preventDefault();

        // Get form values
        const code = $('#unite_code').val();
        const name = $('#unite_name').val();
        const description = $('#unite_description').val();

        // Generate a new ID (in a real app, this would come from the server)
        const newId = $('#unitesTableBody tr').length + 1;

        // Create new unite row for the unites table
        const newUniteRow = `
            <tr class="unite-row"
                data-id="${newId}"
                data-code="${code}"
                data-name="${name}"
                data-description="${description}">
                <td>${newId}</td>
                <td>${code}</td>
                <td>${name}</td>
                <td>${description}</td>
                <td>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-info view-unite-btn" data-id="${newId}" data-bs-toggle="modal" data-bs-target="#viewUniteModal">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-warning edit-unite-btn" data-id="${newId}" data-bs-toggle="modal" data-bs-target="#editUniteModal">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-unite-btn" data-id="${newId}" data-bs-toggle="modal" data-bs-target="#deleteUniteModal">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;

        // Add the new unite row to the unites table
        $('#unitesTableBody').append(newUniteRow);

        // Reset form
        $('#addUniteForm')[0].reset();

        // Close modal
        $('#addUniteModal').modal('hide');

        // Show success message
        alert('تمت إضافة الوحدة العسكرية بنجاح');

        // Update unite options in personnel forms
        updateUniteOptions();
    });

    // Edit Unite Button Click
    $(document).on('click', '.edit-unite-btn', function() {
        const id = $(this).data('id');
        const row = $(`.unite-row[data-id="${id}"]`);

        // Fill the edit form with the unite data
        $('#edit_unite_id').val(id);
        $('#edit_unite_code').val(row.data('code'));
        $('#edit_unite_name').val(row.data('name'));
        $('#edit_unite_description').val(row.data('description'));
    });

    // View Unite Button Click
    $(document).on('click', '.view-unite-btn', function() {
        const id = $(this).data('id');
        const row = $(`.unite-row[data-id="${id}"]`);

        // Fill the view modal with the unite data
        $('#view_unite_code').text(row.data('code'));
        $('#view_unite_name').text(row.data('name'));
        $('#view_unite_description').text(row.data('description'));

        // Set the edit button to open the edit modal for this unite
        $('#editUniteBtn').off('click').on('click', function() {
            $('#viewUniteModal').modal('hide');
            setTimeout(() => {
                $(`.edit-unite-btn[data-id="${id}"]`).click();
            }, 500);
        });
    });

    // Edit Unite Form Submission
    $('#editUniteForm').submit(function(e) {
        e.preventDefault();

        // Get form values
        const id = $('#edit_unite_id').val();
        const code = $('#edit_unite_code').val();
        const name = $('#edit_unite_name').val();
        const description = $('#edit_unite_description').val();

        // Update the unite row in the table
        const row = $(`.unite-row[data-id="${id}"]`);
        row.data('code', code);
        row.data('name', name);
        row.data('description', description);

        row.find('td:nth-child(2)').text(code);
        row.find('td:nth-child(3)').text(name);
        row.find('td:nth-child(4)').text(description);

        // Close modal
        $('#editUniteModal').modal('hide');

        // Show success message
        alert('تم تحديث الوحدة العسكرية بنجاح');

        // Update unite options in personnel forms
        updateUniteOptions();
    });

    // Delete Unite Button Click
    $(document).on('click', '.delete-unite-btn', function() {
        const id = $(this).data('id');
        const row = $(`.unite-row[data-id="${id}"]`);

        // Set the unite name in the confirmation message
        $('#delete_unite_name').text(row.data('name'));

        // Set the unite ID to be deleted
        $('#confirmDeleteUniteBtn').data('id', id);
    });

    // Confirm Delete Unite Button Click
    $('#confirmDeleteUniteBtn').click(function() {
        const id = $(this).data('id');

        // Remove the unite row from the table
        $(`.unite-row[data-id="${id}"]`).remove();

        // Close modal
        $('#deleteUniteModal').modal('hide');

        // Show success message
        alert('تم حذف الوحدة العسكرية بنجاح');

        // Update unite options in personnel forms
        updateUniteOptions();
    });

    // Function to update unite options in personnel forms
    function updateUniteOptions() {
        // Get all unites
        const unites = [];
        $('.unite-row').each(function() {
            if ($(this).data('active') === 'true') {
                unites.push({
                    id: $(this).data('id'),
                    name: $(this).data('name')
                });
            }
        });

        // Sort unites by name
        unites.sort((a, b) => {
            const nameA = $(`.unite-row[data-id="${a.id}"]`).data('name');
            const nameB = $(`.unite-row[data-id="${b.id}"]`).data('name');
            return nameA.localeCompare(nameB);
        });

        // Clear and update unite options in all forms
        const uniteSelects = $('#personnel_unite, #edit_personnel_unite, #new_personnel_unite');
        const currentValues = {};

        // Save current values
        uniteSelects.each(function() {
            currentValues[$(this).attr('id')] = $(this).val();
        });

        // Clear options except the first one (placeholder)
        uniteSelects.find('option:not(:first)').remove();

        // Add new options
        unites.forEach(unite => {
            uniteSelects.append(`<option value="${unite.name}">${unite.name}</option>`);
        });

        // Restore selected values
        uniteSelects.each(function() {
            const value = currentValues[$(this).attr('id')];
            if (value) {
                $(this).val(value);
            }
        });
    }

    // Excel Import/Export Functions

    // Preview Excel Button Click
    $('#previewExcelBtn').click(function() {
        const fileInput = document.getElementById('excelFile');
        if (!fileInput.files.length) {
            alert('الرجاء اختيار ملف Excel أولاً');
            return;
        }

        const file = fileInput.files[0];
        const reader = new FileReader();

        reader.onload = function(e) {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });

                // Get the first sheet
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];

                // Convert to JSON
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                // Check if data is valid
                if (jsonData.length < 2) {
                    alert('الملف لا يحتوي على بيانات كافية');
                    return;
                }

                // Check headers (optional)
                const headers = jsonData[0];
                if (headers.length < 3) {
                    alert('الملف لا يحتوي على الأعمدة المطلوبة');
                    return;
                }

                // Preview data (first 5 rows)
                const previewData = jsonData.slice(1, 6);
                let previewHtml = '';

                previewData.forEach(row => {
                    const code = row[0] || '';
                    const name = row[1] || '';
                    const description = row[2] || '';

                    previewHtml += `
                        <tr>
                            <td>${code}</td>
                            <td>${name}</td>
                            <td>${description}</td>
                        </tr>
                    `;
                });

                $('#previewTableBody').html(previewHtml);
                $('#importPreview').show();

                // Show warning if there are more rows
                if (jsonData.length > 6) {
                    $('#importWarning').html(`<strong>تنبيه:</strong> الملف يحتوي على ${jsonData.length - 1} صف. يتم عرض أول 5 صفوف فقط للمعاينة.`).show();
                } else {
                    $('#importWarning').hide();
                }

                // Enable import button
                $('#importExcelBtn').prop('disabled', false);

                // Store data in a global variable for later use
                window.excelData = jsonData;

            } catch (error) {
                console.error('Error parsing Excel file:', error);
                alert('حدث خطأ أثناء قراءة ملف Excel');
            }
        };

        reader.readAsArrayBuffer(file);
    });

    // Import Excel Form Submission
    $('#importExcelForm').submit(function(e) {
        e.preventDefault();

        if (!window.excelData || window.excelData.length < 2) {
            alert('الرجاء معاينة الملف أولاً');
            return;
        }

        const replaceExisting = $('#replaceExistingData').is(':checked');

        // If replacing existing data, clear the table
        if (replaceExisting) {
            $('#unitesTableBody').empty();
        }

        // Process data (skip header row)
        const data = window.excelData.slice(1);
        let importedCount = 0;
        let errorCount = 0;

        data.forEach(row => {
            try {
                const code = row[0];
                const name = row[1];
                const description = row[2] || '';

                // Skip if required fields are missing
                if (!code || !name) {
                    errorCount++;
                    return;
                }

                // Generate a new ID
                const newId = $('#unitesTableBody tr').length + 1;

                // Create new unite row
                const newUniteRow = `
                    <tr class="unite-row"
                        data-id="${newId}"
                        data-code="${code}"
                        data-name="${name}"
                        data-description="${description}">
                        <td>${newId}</td>
                        <td>${code}</td>
                        <td>${name}</td>
                        <td>${description}</td>
                        <td>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-info view-unite-btn" data-id="${newId}" data-bs-toggle="modal" data-bs-target="#viewUniteModal">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-warning edit-unite-btn" data-id="${newId}" data-bs-toggle="modal" data-bs-target="#editUniteModal">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger delete-unite-btn" data-id="${newId}" data-bs-toggle="modal" data-bs-target="#deleteUniteModal">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;

                // Add the new unite row to the unites table
                $('#unitesTableBody').append(newUniteRow);
                importedCount++;

            } catch (error) {
                console.error('Error importing row:', error, row);
                errorCount++;
            }
        });

        // Close modal
        $('#importExcelModal').modal('hide');

        // Reset form
        $('#importExcelForm')[0].reset();
        $('#importPreview').hide();
        $('#importWarning').hide();
        $('#importExcelBtn').prop('disabled', true);

        // Show success message
        alert(`تم استيراد ${importedCount} وحدة عسكرية بنجاح${errorCount > 0 ? ` (تم تجاهل ${errorCount} صف بسبب بيانات غير صالحة)` : ''}`);

        // Update unite options in personnel forms
        updateUniteOptions();
    });

    // Download Excel Template Button Click
    $('#downloadTemplateBtn').click(function(e) {
        e.preventDefault();

        // Create a new workbook
        const wb = XLSX.utils.book_new();

        // Create headers
        const headers = ['الرمز', 'اسم الوحدة', 'الوصف'];

        // Create sample data
        const data = [
            headers,
            ['U001', 'الوحدة الأولى', 'وحدة المشاة الأولى'],
            ['U002', 'الوحدة الثانية', 'وحدة المدرعات الثانية'],
            ['U003', 'الوحدة الثالثة', 'وحدة المدفعية الثالثة']
        ];

        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(data);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'الوحدات العسكرية');

        // Generate Excel file
        const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });

        // Convert to blob
        function s2ab(s) {
            const buf = new ArrayBuffer(s.length);
            const view = new Uint8Array(buf);
            for (let i = 0; i < s.length; i++) {
                view[i] = s.charCodeAt(i) & 0xFF;
            }
            return buf;
        }

        // Save file
        saveAs(new Blob([s2ab(wbout)], { type: 'application/octet-stream' }), 'قالب_الوحدات_العسكرية.xlsx');
    });

    // Export Unites to Excel Button Click
    $('#exportUniteExcelBtn').click(function() {
        // Get all unites
        const unites = [];
        $('.unite-row').each(function() {
            unites.push({
                code: $(this).data('code'),
                name: $(this).data('name'),
                description: $(this).data('description')
            });
        });

        // Create headers
        const headers = ['الرمز', 'اسم الوحدة', 'الوصف'];

        // Create data array
        const data = [headers];

        // Add unite data
        unites.forEach(unite => {
            data.push([
                unite.code,
                unite.name,
                unite.description
            ]);
        });

        // Create a new workbook
        const wb = XLSX.utils.book_new();

        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(data);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'الوحدات العسكرية');

        // Generate Excel file
        const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });

        // Convert to blob
        function s2ab(s) {
            const buf = new ArrayBuffer(s.length);
            const view = new Uint8Array(buf);
            for (let i = 0; i < s.length; i++) {
                view[i] = s.charCodeAt(i) & 0xFF;
            }
            return buf;
        }

        // Save file
        saveAs(new Blob([s2ab(wbout)], { type: 'application/octet-stream' }), 'الوحدات_العسكرية.xlsx');
    });
});
