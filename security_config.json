{"encryption_enabled": true, "backup_encryption": true, "audit_logging": true, "max_login_attempts": 5, "session_timeout": 3600, "password_policy": {"min_length": 12, "require_uppercase": true, "require_lowercase": true, "require_numbers": true, "require_special": true}, "database_permissions": {"admin": ["read", "write", "delete", "backup", "restore"], "user": ["read", "write"], "viewer": ["read"]}, "created_at": "2025-06-04T15:47:41.709673", "last_updated": "2025-06-04T15:47:41.709687"}