#!/usr/bin/env python3
"""
BANGHALAU Security Manager
Comprehensive security management system for database protection
"""

import os
import sys
import json
import getpass
from datetime import datetime
from pathlib import Path

# Add database directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))

from database.security import DatabaseSecurity
from database.protection import DatabaseProtection
from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

class SecurityManager:
    """
    Main security management interface
    """
    
    def __init__(self):
        self.db_security = DatabaseSecurity()
        self.db_protection = DatabaseProtection()
        self.db_manager = DatabaseManager()
        self.db_ops = None
        
    def initialize(self):
        """Initialize security manager"""
        try:
            self.db_manager.connect()
            self.db_ops = DatabaseOperations(self.db_manager)
            print("🔒 Security Manager initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize Security Manager: {e}")
            return False
    
    def show_main_menu(self):
        """Show main security menu"""
        while True:
            print("\n" + "="*60)
            print("🔐 BANGHALAU - Security Management System")
            print("="*60)
            print("1. 🔍 Security Status & Reports")
            print("2. 🛡️ Database Protection")
            print("3. 🔑 User Management")
            print("4. 💾 Backup & Recovery")
            print("5. 📊 Audit & Monitoring")
            print("6. ⚙️ Security Configuration")
            print("7. 🚨 Emergency Actions")
            print("0. 🚪 Exit")
            print("="*60)
            
            choice = input("اختر الخيار (0-7): ").strip()
            
            if choice == "1":
                self.security_status_menu()
            elif choice == "2":
                self.database_protection_menu()
            elif choice == "3":
                self.user_management_menu()
            elif choice == "4":
                self.backup_recovery_menu()
            elif choice == "5":
                self.audit_monitoring_menu()
            elif choice == "6":
                self.security_config_menu()
            elif choice == "7":
                self.emergency_actions_menu()
            elif choice == "0":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please try again.")
    
    def security_status_menu(self):
        """Security status and reports menu"""
        while True:
            print("\n🔍 Security Status & Reports")
            print("-" * 40)
            print("1. Current Security Status")
            print("2. Database Integrity Check")
            print("3. Protection Report")
            print("4. Access Log Summary")
            print("5. Threat Analysis")
            print("0. Back to Main Menu")
            
            choice = input("اختر الخيار: ").strip()
            
            if choice == "1":
                self.show_security_status()
            elif choice == "2":
                self.check_database_integrity()
            elif choice == "3":
                self.show_protection_report()
            elif choice == "4":
                self.show_access_summary()
            elif choice == "5":
                self.show_threat_analysis()
            elif choice == "0":
                break
    
    def database_protection_menu(self):
        """Database protection menu"""
        while True:
            print("\n🛡️ Database Protection")
            print("-" * 40)
            print("1. Enable/Disable Protection")
            print("2. View Blocked IPs")
            print("3. Unblock IP Address")
            print("4. Create Database Snapshot")
            print("5. Monitor Real-time Activity")
            print("0. Back to Main Menu")
            
            choice = input("اختر الخيار: ").strip()
            
            if choice == "1":
                self.toggle_protection()
            elif choice == "2":
                self.view_blocked_ips()
            elif choice == "3":
                self.unblock_ip()
            elif choice == "4":
                self.create_snapshot()
            elif choice == "5":
                self.monitor_activity()
            elif choice == "0":
                break
    
    def user_management_menu(self):
        """User management menu"""
        while True:
            print("\n🔑 User Management")
            print("-" * 40)
            print("1. List All Users")
            print("2. Create Secure User")
            print("3. Reset User Password")
            print("4. Change User Role")
            print("5. Disable/Enable User")
            print("6. Password Policy Settings")
            print("0. Back to Main Menu")
            
            choice = input("اختر الخيار: ").strip()
            
            if choice == "1":
                self.list_users()
            elif choice == "2":
                self.create_secure_user()
            elif choice == "3":
                self.reset_user_password()
            elif choice == "4":
                self.change_user_role()
            elif choice == "5":
                self.toggle_user_status()
            elif choice == "6":
                self.password_policy_settings()
            elif choice == "0":
                break
    
    def backup_recovery_menu(self):
        """Backup and recovery menu"""
        while True:
            print("\n💾 Backup & Recovery")
            print("-" * 40)
            print("1. Create Encrypted Backup")
            print("2. Restore from Backup")
            print("3. List Available Backups")
            print("4. Schedule Automatic Backups")
            print("5. Verify Backup Integrity")
            print("0. Back to Main Menu")
            
            choice = input("اختر الخيار: ").strip()
            
            if choice == "1":
                self.create_encrypted_backup()
            elif choice == "2":
                self.restore_from_backup()
            elif choice == "3":
                self.list_backups()
            elif choice == "4":
                self.schedule_backups()
            elif choice == "5":
                self.verify_backup_integrity()
            elif choice == "0":
                break
    
    def show_security_status(self):
        """Show current security status"""
        print("\n📊 Current Security Status")
        print("-" * 50)
        
        # Get protection status
        status = self.db_protection.get_protection_status()
        
        print(f"🔒 Monitoring Active: {'✅ Yes' if status['monitoring_active'] else '❌ No'}")
        print(f"🚫 Blocked IPs: {len(status['blocked_ips'])}")
        print(f"⚠️ Failed Attempts: {sum(status['failed_attempts'].values())}")
        print(f"💾 Database Size: {status['database_size'] / 1024 / 1024:.2f} MB")
        print(f"🔍 Integrity Issues: {len(status['integrity_issues'])}")
        
        if status['integrity_issues']:
            print("\n⚠️ Integrity Issues Found:")
            for issue in status['integrity_issues']:
                print(f"  - {issue}")
        
        # Get security config
        config = self.db_security.config
        print(f"\n🔐 Security Configuration:")
        print(f"  - Encryption: {'✅ Enabled' if config['encryption_enabled'] else '❌ Disabled'}")
        print(f"  - Backup Encryption: {'✅ Enabled' if config['backup_encryption'] else '❌ Disabled'}")
        print(f"  - Audit Logging: {'✅ Enabled' if config['audit_logging'] else '❌ Disabled'}")
        print(f"  - Max Login Attempts: {config['max_login_attempts']}")
        print(f"  - Session Timeout: {config['session_timeout']} seconds")
    
    def check_database_integrity(self):
        """Check database integrity"""
        print("\n🔍 Checking Database Integrity...")
        
        issues = self.db_protection.verify_database_integrity()
        
        if not issues:
            print("✅ Database integrity check passed - No issues found")
        else:
            print(f"⚠️ Found {len(issues)} integrity issues:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
    
    def create_encrypted_backup(self):
        """Create encrypted backup"""
        print("\n💾 Creating Encrypted Backup")
        print("-" * 40)
        
        backup_name = input("Enter backup name (or press Enter for auto-name): ").strip()
        if not backup_name:
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_path = f"backups/{backup_name}.bak"
        
        # Get encryption password
        password = getpass.getpass("Enter encryption password: ")
        confirm_password = getpass.getpass("Confirm encryption password: ")
        
        if password != confirm_password:
            print("❌ Passwords do not match!")
            return
        
        # Validate password
        is_valid, errors = self.db_security.validate_password_policy(password)
        if not is_valid:
            print("❌ Password does not meet security policy:")
            for error in errors:
                print(f"  - {error}")
            return
        
        print("🔄 Creating encrypted backup...")
        success, message = self.db_security.create_secure_backup(backup_path, password)
        
        if success:
            print(f"✅ {message}")
            print(f"📁 Backup saved to: {backup_path}")
        else:
            print(f"❌ {message}")
    
    def list_users(self):
        """List all users"""
        print("\n👥 User List")
        print("-" * 50)
        
        try:
            users = self.db_ops.get_all_users()
            
            if not users:
                print("📭 No users found")
                return
            
            print(f"{'ID':<5} {'Username':<15} {'Role':<10} {'Status':<10} {'Last Login':<20}")
            print("-" * 70)
            
            for user in users:
                status = "Active" if user.get('is_active', True) else "Disabled"
                last_login = user.get('last_login', 'Never')
                if last_login and last_login != 'Never':
                    last_login = last_login[:19]  # Truncate timestamp
                
                print(f"{user['id']:<5} {user['username']:<15} {user.get('role', 'user'):<10} {status:<10} {last_login:<20}")
                
        except Exception as e:
            print(f"❌ Error listing users: {e}")
    
    def create_secure_user(self):
        """Create a new secure user"""
        print("\n👤 Create Secure User")
        print("-" * 40)
        
        username = input("Username: ").strip()
        if not username:
            print("❌ Username cannot be empty")
            return
        
        # Check if user exists
        try:
            existing_user = self.db_ops.get_user_by_username(username)
            if existing_user:
                print("❌ Username already exists")
                return
        except:
            pass
        
        full_name = input("Full Name: ").strip()
        email = input("Email: ").strip()
        role = input("Role (admin/user/viewer) [user]: ").strip() or "user"
        
        # Generate secure password or let user enter one
        generate_password = input("Generate secure password? (y/n) [y]: ").strip().lower()
        
        if generate_password != 'n':
            password = self.db_security.generate_secure_password(16)
            print(f"🔑 Generated password: {password}")
            print("⚠️ Please save this password securely!")
        else:
            password = getpass.getpass("Enter password: ")
            
            # Validate password
            is_valid, errors = self.db_security.validate_password_policy(password)
            if not is_valid:
                print("❌ Password does not meet security policy:")
                for error in errors:
                    print(f"  - {error}")
                return
        
        # Create user
        try:
            user_data = {
                'username': username,
                'password': self.db_security.hash_password_secure(password),
                'full_name': full_name,
                'email': email,
                'role': role,
                'is_active': True,
                'created_at': datetime.now()
            }
            
            user_id = self.db_ops.create_user(user_data)
            
            if user_id:
                print(f"✅ User created successfully with ID: {user_id}")
                self.db_security.log_security_event("USER_CREATED", user_id=user_id, details=f"User {username} created")
            else:
                print("❌ Failed to create user")
                
        except Exception as e:
            print(f"❌ Error creating user: {e}")
    
    def view_blocked_ips(self):
        """View blocked IP addresses"""
        print("\n🚫 Blocked IP Addresses")
        print("-" * 40)
        
        status = self.db_protection.get_protection_status()
        blocked_ips = status['blocked_ips']
        
        if not blocked_ips:
            print("✅ No IP addresses are currently blocked")
            return
        
        print(f"Found {len(blocked_ips)} blocked IP(s):")
        for i, ip in enumerate(blocked_ips, 1):
            failed_attempts = status['failed_attempts'].get(ip, 0)
            print(f"  {i}. {ip} (Failed attempts: {failed_attempts})")
    
    def unblock_ip(self):
        """Unblock an IP address"""
        print("\n🔓 Unblock IP Address")
        print("-" * 40)
        
        ip_address = input("Enter IP address to unblock: ").strip()
        
        if not ip_address:
            print("❌ IP address cannot be empty")
            return
        
        success = self.db_protection.unblock_ip(ip_address)
        
        if success:
            print(f"✅ IP address {ip_address} has been unblocked")
        else:
            print(f"❌ IP address {ip_address} was not blocked")
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            self.db_protection.stop_monitoring()
            if self.db_manager:
                self.db_manager.close()
        except:
            pass

def main():
    """Main function"""
    security_manager = SecurityManager()
    
    try:
        if not security_manager.initialize():
            return
        
        print("🔐 BANGHALAU Security Manager")
        print("=" * 50)
        print("Welcome to the comprehensive database security system")
        print("This tool helps you manage and monitor database security")
        print("=" * 50)
        
        security_manager.show_main_menu()
        
    except KeyboardInterrupt:
        print("\n\n👋 Security Manager interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        security_manager.cleanup()

if __name__ == "__main__":
    main()
