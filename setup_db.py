#!/usr/bin/env python3
"""
Setup script to initialize database and create default admin user
"""

import os
import sys
from werkzeug.security import generate_password_hash
from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def setup_database():
    """Setup database with default admin user"""
    print("Setting up BANGHALAU database...")
    
    # Remove existing database if it exists
    if os.path.exists("banghalau.db"):
        os.remove("banghalau.db")
        print("Removed existing database file.")
    
    try:
        # Initialize database
        db_manager = DatabaseManager()
        db_manager.connect()
        db_manager.initialize_database()
        
        # Create default admin user
        db_ops = DatabaseOperations(db_manager)
        admin_password = generate_password_hash('admin123')
        user_id = db_ops.create_user('admin', admin_password, '<EMAIL>')
        
        if user_id:
            print("✅ Default admin user created successfully!")
            print("   Username: admin")
            print("   Password: admin123")
            print("   Email: <EMAIL>")
        else:
            print("❌ Failed to create default admin user")
            return False
        
        # Add some sample data for testing
        print("\nAdding sample data...")
        
        # Add sample grades
        grade_data = [
            ('G001', 'Lieutenant', 'Grade de Lieutenant'),
            ('G002', 'Capitaine', 'Grade de Capitaine'),
            ('G003', 'Commandant', 'Grade de Commandant'),
            ('G004', 'Lieutenant-Colonel', 'Grade de Lieutenant-Colonel'),
            ('G005', 'Colonel', 'Grade de Colonel')
        ]
        
        for numero, grade, description in grade_data:
            db_manager.execute(
                "INSERT INTO grades (numero, grade, description) VALUES (?, ?, ?)",
                (numero, grade, description)
            )
        
        # Add sample units
        unit_data = [
            ('U001', 'Unité Alpha', 'UA'),
            ('U002', 'Unité Bravo', 'UB'),
            ('U003', 'Unité Charlie', 'UC'),
            ('U004', 'Unité Delta', 'UD')
        ]
        
        for numero, description, raccourci in unit_data:
            db_manager.execute(
                "INSERT INTO unites (numero, description, raccourci) VALUES (?, ?, ?)",
                (numero, description, raccourci)
            )
        
        # Add sample bungalows
        bungalow_data = [
            ('B001', 'Zone Nord', 4, 'Bungalow familial avec jardin'),
            ('B002', 'Zone Nord', 2, 'Bungalow pour couple'),
            ('B003', 'Zone Sud', 6, 'Grand bungalow familial'),
            ('B004', 'Zone Sud', 4, 'Bungalow standard'),
            ('B005', 'Zone Est', 2, 'Bungalow économique'),
            ('B006', 'Zone Ouest', 8, 'Bungalow de luxe')
        ]
        
        for numero, endroit, capacite, caracteristiques in bungalow_data:
            db_manager.execute(
                "INSERT INTO bungalows (numero, endroit, capacite, caracteristiques) VALUES (?, ?, ?, ?)",
                (numero, endroit, capacite, caracteristiques)
            )
        
        # Add sample sessions
        session_data = [
            ('S2024-01', 'Session Hiver 2024', '2024-01-01', '2024-03-31', 'Active'),
            ('S2024-02', 'Session Printemps 2024', '2024-04-01', '2024-06-30', 'Planifiée'),
            ('S2024-03', 'Session Été 2024', '2024-07-01', '2024-09-30', 'Planifiée')
        ]
        
        for numero, description, date_debut, date_fin, etat in session_data:
            db_manager.execute(
                "INSERT INTO sessions (numero, description, date_debut, date_fin, etat_session) VALUES (?, ?, ?, ?, ?)",
                (numero, description, date_debut, date_fin, etat)
            )
        
        db_manager.commit()
        db_manager.close()
        
        print("✅ Sample data added successfully!")
        print("\nDatabase setup completed successfully!")
        print("\nYou can now start the application with:")
        print("  python run.py")
        print("\nOr use the startup scripts:")
        print("  Windows: start.bat")
        print("  Linux/Mac: ./start.sh")
        
        return True
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

if __name__ == "__main__":
    success = setup_database()
    if not success:
        sys.exit(1)
