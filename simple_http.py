#!/usr/bin/env python3
"""
Simple HTTP server for BANGHALAU testing
"""

import http.server
import socketserver
import webbrowser
import threading
import time

PORT = 9000

class BanghalauHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html_content = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BANGHALAU - نظام إدارة البنغالوهات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            max-width: 800px;
            width: 100%;
            text-align: center;
        }
        
        .logo {
            font-size: 4em;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        h2 {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.5em;
        }
        
        .status-card {
            background: #4CAF50;
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            font-size: 1.2em;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }
        
        .info-card h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .info-card p {
            color: #666;
            line-height: 1.6;
        }
        
        .credentials {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid #2196F3;
        }
        
        .credentials h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .cred-item {
            background: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            font-family: monospace;
            font-size: 1.1em;
        }
        
        .features {
            text-align: right;
            margin: 30px 0;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            background: #f0f8ff;
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            border-right: 4px solid #667eea;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="logo">🏠</div>
        <h1>BANGHALAU</h1>
        <h2>نظام إدارة توزيع البنغالوهات</h2>
        
        <div class="status-card">
            ✅ الخادم يعمل بنجاح على المنفذ 9000!
        </div>
        
        <div class="credentials">
            <h3>🔐 بيانات تسجيل الدخول</h3>
            <div class="cred-item">👤 اسم المستخدم: <strong>admin</strong></div>
            <div class="cred-item">🔑 كلمة المرور: <strong>admin123</strong></div>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>🌐 معلومات الخادم</h3>
                <p>المنفذ: 9000<br>
                الحالة: متصل<br>
                النوع: HTTP Server</p>
            </div>
            
            <div class="info-card">
                <h3>📊 إحصائيات النظام</h3>
                <p>قاعدة البيانات: جاهزة<br>
                المستخدمين: 1<br>
                الحالة: نشط</p>
            </div>
        </div>
        
        <div class="features">
            <h3>✨ مميزات النظام:</h3>
            <ul>
                <li>🏠 إدارة البنغالوهات - إضافة وتعديل وحذف البنغالوهات</li>
                <li>👥 إدارة الأفراد العسكريين - تسجيل بيانات الأفراد والرتب</li>
                <li>🏢 إدارة الوحدات - تنظيم الوحدات العسكرية</li>
                <li>📋 توزيع البنغالوهات - ربط الأفراد بالبنغالوهات حسب الجلسات</li>
                <li>📊 التقارير والإحصائيات - متابعة حالة الإشغال</li>
                <li>🎨 واجهة حديثة - تصميم متجاوب مع تأثيرات Glassmorphism</li>
            </ul>
        </div>
        
        <div>
            <a href="http://localhost:5000" class="btn">🚀 الانتقال إلى التطبيق الرئيسي</a>
            <a href="#" class="btn" onclick="location.reload()">🔄 تحديث الصفحة</a>
        </div>
        
        <div class="footer">
            <p>BANGHALAU - نظام إدارة توزيع البنغالوهات</p>
            <p>تم تطوير النظام باستخدام Python Flask</p>
        </div>
    </div>
</body>
</html>
            """
            
            self.wfile.write(html_content.encode('utf-8'))
        else:
            super().do_GET()

def start_server():
    try:
        with socketserver.TCPServer(("", PORT), BanghalauHandler) as httpd:
            print(f"🚀 BANGHALAU Test Server")
            print(f"🌐 URL: http://localhost:{PORT}")
            print(f"📍 Status: Running")
            print("=" * 50)
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ Error starting server: {e}")

def open_browser():
    time.sleep(2)
    print("🌐 Opening browser...")
    webbrowser.open(f'http://localhost:{PORT}')

if __name__ == "__main__":
    # Start browser in a separate thread
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # Start the server
    start_server()
