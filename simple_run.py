#!/usr/bin/env python3
"""
Simple runner for BANGHALAU application
"""

import os
import sys

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from flask import Flask
    print("✅ Flask is available")
except ImportError:
    print("❌ Flask not found. Installing...")
    os.system("python -m pip install flask flask-login werkzeug")

try:
    # Import the app
    from app import app
    
    print("🚀 Starting BANGHALAU Application...")
    print("📍 URL: http://localhost:5000")
    print("👤 Username: admin")
    print("🔑 Password: admin123")
    print("=" * 50)
    
    # Initialize database
    try:
        from database.db_manager import DatabaseManager
        from database.operations import DatabaseOperations
        
        db_manager = DatabaseManager()
        db_manager.connect()
        db_manager.initialize_database()
        
        # Create default admin user if no users exist
        db_ops = DatabaseOperations(db_manager)
        users = db_ops.list_users()
        
        if not users:
            from werkzeug.security import generate_password_hash
            admin_password = generate_password_hash('admin123')
            user_id = db_ops.create_user('admin', admin_password, '<EMAIL>')
            if user_id:
                print("✅ Default admin user created: admin/admin123")
            else:
                print("⚠️ Failed to create default admin user")
        else:
            print("✅ Database ready with existing users")
            
        db_manager.close()
        
    except Exception as e:
        print(f"⚠️ Database setup warning: {e}")
    
    # Run the application
    app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)
    
except Exception as e:
    print(f"❌ Error starting application: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
