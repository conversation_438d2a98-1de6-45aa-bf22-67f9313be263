#!/usr/bin/env python3
"""
BANGHALAU Simple Web Server - للتشغيل السريع
"""

import os
import sys
import webbrowser
import threading
import time

def print_banner():
    print("=" * 70)
    print("🏠 BANGHALAU - نظام إدارة توزيع البنغالوهات")
    print("=" * 70)
    print("🚀 بدء تشغيل خادم الويب البسيط...")
    print()

def open_browser_delayed():
    """Open browser after a delay"""
    time.sleep(3)
    print("🌐 فتح المتصفح...")
    webbrowser.open('http://localhost:5000')

def main():
    print_banner()
    
    try:
        # Check if Flask is available
        import flask
        print("✅ Flask متوفر - الإصدار:", flask.__version__)
        
        # Check database
        if os.path.exists('banghalau.db'):
            print("✅ قاعدة البيانات موجودة")
        else:
            print("⚠️ قاعدة البيانات غير موجودة")
        
        print("📦 تحميل التطبيق...")
        
        # Import Flask app with error handling
        try:
            from app import app
            print("✅ تم تحميل التطبيق بنجاح")
        except ImportError as e:
            print(f"❌ خطأ في استيراد التطبيق: {e}")
            print("🔄 محاولة تشغيل خادم بديل...")
            
            # Create a simple Flask app as fallback
            from flask import Flask, render_template_string
            app = Flask(__name__)
            
            @app.route('/')
            def home():
                return render_template_string('''
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>BANGHALAU - نظام إدارة البنغالوهات</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
                        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
                        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
                        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
                        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
                        .btn { display: inline-block; padding: 10px 20px; margin: 10px 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
                        .btn:hover { background: #0056b3; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🏠 BANGHALAU - نظام إدارة توزيع البنغالوهات</h1>
                        
                        <div class="status success">
                            ✅ خادم الويب يعمل بنجاح على المنفذ 5000
                        </div>
                        
                        <div class="status warning">
                            ⚠️ التطبيق الرئيسي غير متاح حالياً - يتم تشغيل خادم بديل
                        </div>
                        
                        <div class="status error">
                            ❌ يرجى التحقق من ملفات التطبيق والمتطلبات
                        </div>
                        
                        <h2>📋 معلومات النظام:</h2>
                        <ul>
                            <li><strong>الخادم:</strong> Flask Development Server</li>
                            <li><strong>المنفذ:</strong> 5000</li>
                            <li><strong>الحالة:</strong> يعمل</li>
                            <li><strong>قاعدة البيانات:</strong> {{ db_status }}</li>
                        </ul>
                        
                        <h2>🔧 الإجراءات المطلوبة:</h2>
                        <ol>
                            <li>تحقق من وجود جميع ملفات التطبيق</li>
                            <li>تأكد من تثبيت جميع المتطلبات</li>
                            <li>تحقق من قاعدة البيانات</li>
                            <li>أعد تشغيل التطبيق</li>
                        </ol>
                        
                        <div style="text-align: center; margin-top: 30px;">
                            <a href="/" class="btn">🔄 إعادة تحميل الصفحة</a>
                        </div>
                    </div>
                </body>
                </html>
                ''', db_status="موجودة" if os.path.exists('banghalau.db') else "غير موجودة")
            
            print("✅ تم إنشاء خادم بديل")
        
        # Print access information
        print()
        print("🌐 معلومات الوصول:")
        print("   📍 الرابط: http://localhost:5000")
        print("   🔄 بدء خادم الويب...")
        print("   (اضغط Ctrl+C لإيقاف الخادم)")
        print("=" * 70)
        
        # Start browser in background
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start the Flask server
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
