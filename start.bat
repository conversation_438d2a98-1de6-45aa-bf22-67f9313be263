@echo off
echo Starting BANGHALAU Application...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH
    echo Please install Python 3.11 or later
    pause
    exit /b 1
)

REM Install requirements if needed
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Installing requirements...
pip install -r requirements.txt

echo.
echo Checking database...
if not exist "banghalau.db" (
    echo Database not found. Setting up database...
    python setup_db.py
    if errorlevel 1 (
        echo Failed to setup database
        pause
        exit /b 1
    )
) else (
    echo Database found.
)

echo.
echo ================================================================================
echo                              🚀 DEMARRAGE DU SERVEUR
echo ================================================================================
echo.
echo 🌐 Interface moderne accessible sur: http://localhost:5000
echo.
echo 🔐 IDENTIFIANTS DE CONNEXION:
echo    👤 Nom d'utilisateur: admin
echo    🔑 Mot de passe: admin123
echo.
echo ✨ NOUVELLES FONCTIONNALITES:
echo    🎨 Interface ultra-moderne avec Glassmorphism
echo    📊 Cartes statistiques interactives
echo    🔔 Notifications en temps reel
echo    📱 Design 100%% responsive
echo    ⚡ Operations CRUD completes
echo.
echo 🛑 Appuyez sur Ctrl+C pour arreter le serveur
echo ================================================================================
echo.

python run.py

pause
