#!/bin/bash

echo "Starting BANGHALAU Application..."
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed"
    echo "Please install Python 3.11 or later"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

echo "Activating virtual environment..."
source venv/bin/activate

echo "Installing requirements..."
pip install -r requirements.txt

echo
echo "Checking database..."
if [ ! -f "banghalau.db" ]; then
    echo "Database not found. Setting up database..."
    python3 setup_db.py
    if [ $? -ne 0 ]; then
        echo "Failed to setup database"
        exit 1
    fi
else
    echo "Database found."
fi

echo
echo "Starting BANGHALAU server..."
echo "Access the application at: http://localhost:5000"
echo
echo "Default login credentials:"
echo "Username: admin"
echo "Password: admin123"
echo
echo "Press Ctrl+C to stop the server"
echo

python3 run.py
