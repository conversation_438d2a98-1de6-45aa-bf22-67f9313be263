#!/usr/bin/env python3
"""
BANGHALAU - نظام إدارة توزيع البنغالوهات
ملف التشغيل الرئيسي للخادم
"""

import os
import sys
import webbrowser
import threading
import time

def print_banner():
    print("=" * 70)
    print("🏠 BANGHALAU - نظام إدارة توزيع البنغالوهات")
    print("=" * 70)
    print("🚀 بدء تشغيل النظام...")
    print()

def check_flask():
    """Check if Flask is installed"""
    try:
        import flask
        print(f"✅ Flask متوفر - الإصدار: {flask.__version__}")
        return True
    except ImportError:
        print("❌ Flask غير مثبت")
        print("   يرجى تثبيت Flask باستخدام: pip install flask")
        return False

def check_database():
    """Check database status"""
    if os.path.exists('banghalau.db'):
        print("✅ قاعدة البيانات موجودة")
        return True
    else:
        print("⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها تلقائياً")
        return True

def open_browser_delayed():
    """Open browser after delay"""
    time.sleep(3)
    print("🌐 فتح المتصفح...")
    webbrowser.open('http://localhost:5000')

def create_simple_app():
    """Create a simple Flask app"""
    from flask import Flask, render_template_string
    
    app = Flask(__name__)
    app.secret_key = 'banghalau_secret_key'
    
    @app.route('/')
    def home():
        return render_template_string('''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>BANGHALAU - نظام إدارة البنغالوهات</title>
            <style>
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                    margin: 0; 
                    padding: 20px; 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                }
                .container { 
                    max-width: 900px; 
                    margin: 0 auto; 
                    background: white; 
                    padding: 40px; 
                    border-radius: 15px; 
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                }
                h1 { 
                    color: #2c3e50; 
                    text-align: center; 
                    margin-bottom: 30px; 
                    font-size: 2.5em;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
                }
                .status { 
                    padding: 20px; 
                    margin: 15px 0; 
                    border-radius: 10px; 
                    font-size: 1.1em;
                    border-left: 5px solid;
                }
                .success { 
                    background: #d4edda; 
                    color: #155724; 
                    border-left-color: #28a745;
                }
                .warning { 
                    background: #fff3cd; 
                    color: #856404; 
                    border-left-color: #ffc107;
                }
                .info { 
                    background: #d1ecf1; 
                    color: #0c5460; 
                    border-left-color: #17a2b8;
                }
                .feature-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin: 30px 0;
                }
                .feature-card {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    text-align: center;
                    border: 2px solid #e9ecef;
                    transition: transform 0.3s ease;
                }
                .feature-card:hover {
                    transform: translateY(-5px);
                    border-color: #007bff;
                }
                .feature-icon {
                    font-size: 3em;
                    margin-bottom: 15px;
                }
                .btn { 
                    display: inline-block; 
                    padding: 15px 30px; 
                    margin: 10px 5px; 
                    background: linear-gradient(45deg, #007bff, #0056b3); 
                    color: white; 
                    text-decoration: none; 
                    border-radius: 25px;
                    font-weight: bold;
                    transition: all 0.3s ease;
                    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
                }
                .btn:hover { 
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px rgba(0,123,255,0.4);
                }
                .footer {
                    text-align: center;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 2px solid #e9ecef;
                    color: #6c757d;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🏠 BANGHALAU</h1>
                <h2 style="text-align: center; color: #6c757d; margin-bottom: 40px;">
                    نظام إدارة توزيع البنغالوهات
                </h2>
                
                <div class="status success">
                    ✅ خادم الويب يعمل بنجاح على المنفذ 5000
                </div>
                
                <div class="status info">
                    ℹ️ النظام جاهز للاستخدام - قاعدة البيانات: {{ db_status }}
                </div>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🏠</div>
                        <h3>إدارة البنغالوهات</h3>
                        <p>إضافة وتعديل وحذف البنغالوهات</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">👥</div>
                        <h3>إدارة الأفراد</h3>
                        <p>تسجيل بيانات الأفراد العسكريين</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">📋</div>
                        <h3>توزيع البنغالوهات</h3>
                        <p>إدارة عمليات التوزيع والإشغال</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3>التقارير</h3>
                        <p>عرض الإحصائيات والتقارير</p>
                    </div>
                </div>
                
                <div style="text-align: center; margin: 40px 0;">
                    <h3>🔧 حالة النظام:</h3>
                    <ul style="text-align: right; display: inline-block;">
                        <li><strong>الخادم:</strong> Flask Development Server</li>
                        <li><strong>المنفذ:</strong> 5000</li>
                        <li><strong>الحالة:</strong> يعمل ✅</li>
                        <li><strong>قاعدة البيانات:</strong> {{ db_status }}</li>
                        <li><strong>الوقت:</strong> {{ current_time }}</li>
                    </ul>
                </div>
                
                <div style="text-align: center;">
                    <a href="/" class="btn">🔄 إعادة تحميل الصفحة</a>
                    <a href="/login" class="btn">🔐 تسجيل الدخول</a>
                </div>
                
                <div class="footer">
                    <p>© 2024 BANGHALAU - نظام إدارة توزيع البنغالوهات</p>
                    <p>تم التطوير باستخدام Flask و Python</p>
                </div>
            </div>
        </body>
        </html>
        ''', 
        db_status="موجودة ✅" if os.path.exists('banghalau.db') else "غير موجودة ⚠️",
        current_time=time.strftime('%Y-%m-%d %H:%M:%S'))
    
    @app.route('/login')
    def login():
        return render_template_string('''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تسجيل الدخول - BANGHALAU</title>
            <style>
                body { font-family: Arial, sans-serif; background: #f5f5f5; padding: 50px; }
                .login-container { max-width: 400px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h2 { text-align: center; color: #2c3e50; margin-bottom: 30px; }
                .form-group { margin-bottom: 20px; }
                label { display: block; margin-bottom: 5px; font-weight: bold; }
                input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
                .btn { width: 100%; padding: 15px; background: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
                .btn:hover { background: #0056b3; }
                .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <div class="login-container">
                <h2>🔐 تسجيل الدخول</h2>
                
                <div class="info">
                    <strong>بيانات الدخول الافتراضية:</strong><br>
                    اسم المستخدم: admin<br>
                    كلمة المرور: admin123
                </div>
                
                <form method="POST" action="/login">
                    <div class="form-group">
                        <label>اسم المستخدم:</label>
                        <input type="text" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label>كلمة المرور:</label>
                        <input type="password" name="password" required>
                    </div>
                    
                    <button type="submit" class="btn">دخول</button>
                </form>
                
                <div style="text-align: center; margin-top: 20px;">
                    <a href="/">← العودة للصفحة الرئيسية</a>
                </div>
            </div>
        </body>
        </html>
        ''')
    
    return app

def main():
    print_banner()
    
    # Check Flask
    if not check_flask():
        return
    
    # Check database
    check_database()
    
    try:
        print("📦 تحميل التطبيق...")
        
        # Try to import the main app first
        try:
            from app import app
            print("✅ تم تحميل التطبيق الرئيسي بنجاح")
        except Exception as e:
            print(f"⚠️ تعذر تحميل التطبيق الرئيسي: {e}")
            print("🔄 تشغيل التطبيق البديل...")
            app = create_simple_app()
            print("✅ تم تحميل التطبيق البديل")
        
        # Print access information
        print()
        print("🌐 معلومات الوصول:")
        print("   📍 الرابط: http://localhost:5000")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        print()
        print("🔄 بدء خادم الويب...")
        print("   (اضغط Ctrl+C لإيقاف الخادم)")
        print("=" * 70)
        
        # Start browser in background
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start the Flask server
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
