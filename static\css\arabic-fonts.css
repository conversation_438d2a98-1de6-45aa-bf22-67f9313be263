/*
 * BANGHALAU - تحسين الخطوط العربية
 * ملف CSS مخصص لتحسين عرض النصوص العربية والمختلطة
 */

/* استيراد الخطوط العربية المحسنة */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

/* متغيرات الخطوط */
:root {
    --arabic-font-primary: '<PERSON>', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --arabic-font-secondary: 'Tajawal', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --arabic-font-elegant: 'Amiri', 'Noto Sans Arabic', serif;
    --mixed-font: 'Cairo', 'Noto Sans Arabic', 'Inter', 'Segoe UI', sans-serif;
}

/* تحسين عام للنصوص العربية */
.arabic-text {
    font-family: var(--arabic-font-primary);
    font-weight: 500;
    line-height: 1.6;
    direction: rtl;
    text-align: right;
    font-feature-settings: 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* نص مختلط (عربي وإنجليزي) */
.mixed-text {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.5;
    direction: auto;
    text-align: start;
    font-feature-settings: 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسين النصوص في الجداول */
.table-arabic {
    font-family: var(--arabic-font-primary);
    font-weight: 400;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.table-arabic th {
    font-weight: 600;
}

.table-arabic td {
    font-weight: 400;
    vertical-align: middle;
}

/* تحسين النصوص في النماذج */
.form-arabic input,
.form-arabic textarea,
.form-arabic select {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
    line-height: 1.5;
}

.form-arabic label {
    font-family: var(--mixed-font);
    font-weight: 500;
}

/* تحسين النصوص في البطاقات */
.card-arabic .card-title {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    line-height: 1.3;
}

.card-arabic .card-text {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.5;
    direction: auto;
    text-align: start;
}

/* تحسين النصوص في الأزرار */
.btn-arabic {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* تحسين النصوص في التنبيهات */
.alert-arabic {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
    line-height: 1.5;
}

/* تحسين النصوص في النوافذ المنبثقة */
.modal-arabic .modal-title {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
}

.modal-arabic .modal-body {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
    line-height: 1.6;
}

/* تحسين النصوص في القوائم */
.nav-arabic {
    font-family: var(--mixed-font);
    font-weight: 500;
}

.dropdown-arabic .dropdown-item {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين النصوص في الشارات */
.badge-arabic {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* تحسين النصوص للعناوين */
.heading-arabic {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    line-height: 1.3;
    direction: auto;
    text-align: start;
}

/* تحسين النصوص للفقرات */
.paragraph-arabic {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.6;
    direction: auto;
    text-align: start;
}

/* تحسين النصوص الصغيرة */
.small-arabic {
    font-family: var(--mixed-font);
    font-weight: 400;
    font-size: 0.875rem;
    line-height: 1.4;
}

/* تحسين النصوص في التذييل */
.footer-arabic {
    font-family: var(--mixed-font);
    font-weight: 400;
    font-size: 0.875rem;
}

/* تحسين النصوص للشاشات الصغيرة */
@media (max-width: 768px) {
    .arabic-text {
        font-size: 0.95rem;
        line-height: 1.5;
    }
    
    .mixed-text {
        font-size: 0.9rem;
        line-height: 1.4;
    }
    
    .table-arabic {
        font-size: 0.85rem;
    }
    
    .heading-arabic {
        font-size: 1.1rem;
        line-height: 1.2;
    }
}

/* تحسين النصوص للطباعة */
@media print {
    .arabic-text,
    .mixed-text,
    .table-arabic,
    .heading-arabic,
    .paragraph-arabic {
        font-family: 'Cairo', 'Noto Sans Arabic', serif;
        color: #000;
        -webkit-print-color-adjust: exact;
    }
}

/* تحسين النصوص للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .arabic-text,
    .mixed-text {
        -webkit-font-smoothing: subpixel-antialiased;
        -moz-osx-font-smoothing: auto;
    }
}

/* تحسين النصوص للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .arabic-text,
    .mixed-text,
    .table-arabic {
        color: #f8fafc;
    }
}

/* فئات مساعدة للاتجاه */
.rtl {
    direction: rtl;
    text-align: right;
}

.ltr {
    direction: ltr;
    text-align: left;
}

.auto-dir {
    direction: auto;
    text-align: start;
}

/* فئات مساعدة لوزن الخط */
.font-light-arabic {
    font-weight: 300;
}

.font-normal-arabic {
    font-weight: 400;
}

.font-medium-arabic {
    font-weight: 500;
}

.font-semibold-arabic {
    font-weight: 600;
}

.font-bold-arabic {
    font-weight: 700;
}
