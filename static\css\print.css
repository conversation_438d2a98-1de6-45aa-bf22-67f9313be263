/*
 * Print-specific CSS for BANGHALAU
 * Optimized for printing distributions and other documents
 */

/* خطوط عربية محسنة للطباعة */


/* Print Media Queries */
@media print {
    /* Page setup - Hide browser headers and footers */
    @page {
        size: A4;
        margin: 1cm 1cm 1.2cm 1.2cm;
        /* Hide browser print headers and footers */
        @top-left { content: ""; }
        @top-center { content: ""; }
        @top-right { content: ""; }
        @bottom-left { content: ""; }
        @bottom-center { content: ""; }
        @bottom-right { content: ""; }
    }

    /* Hide browser print headers and footers */
    html {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    /* Force hide browser print information */
    body::before,
    body::after,
    html::before,
    html::after {
        content: "" !important;
        display: none !important;
    }

    /* Hide non-printable elements */
    .no-print,
    .print-controls,
    .btn,
    .dropdown,
    .navbar,
    .sidebar,
    .footer,
    .alert,
    .modal,
    .tooltip,
    .popover,
    .print-sidebar,
    .print-menu,
    .print-options,
    .browser-print-dialog,
    .chrome-print-preview,
    iframe[name="print_frame"],
    .print-preview-sidebar,
    .print-destination,
    .print-settings,
    .browser-header,
    .browser-footer,
    .print-header-info,
    .print-footer-info {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
    }

    /* Body and text */
    body {
        font-family: 'Cairo', 'Noto Sans Arabic', 'Arial', sans-serif !important;
        font-size: 10pt !important;
        line-height: 1.3 !important;
        color: #000 !important;
        background: #fff !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Headers */
    h1, h2, h3, h4, h5, h6 {
        color: #000 !important;
        page-break-after: avoid;
        font-family: 'Cairo', 'Noto Sans Arabic', sans-serif !important;
    }

    h1 { font-size: 18pt !important; }
    h2 { font-size: 16pt !important; }
    h3 { font-size: 14pt !important; }
    h4 { font-size: 12pt !important; }
    h5 { font-size: 11pt !important; }
    h6 { font-size: 10pt !important; }

    /* Tables */
    table {
        border-collapse: collapse !important;
        width: 100% !important;
        page-break-inside: auto;
        font-size: 9pt !important;
    }

    table, th, td {
        border: 1px solid #000 !important;
    }

    th, td {
        padding: 4px 6px !important;
        text-align: left !important;
        vertical-align: top !important;
    }

    th {
        background-color: #f0f0f0 !important;
        font-weight: bold !important;
        text-align: center !important;
        font-size: 8pt !important;
    }

    /* Table row striping for better readability */
    tbody tr:nth-child(even) {
        background-color: #f9f9f9 !important;
    }

    /* Page breaks */
    .page-break {
        page-break-before: always !important;
    }

    .avoid-break {
        page-break-inside: avoid !important;
    }

    .page-break-after {
        page-break-after: always !important;
    }

    /* Table headers repeat on each page */
    thead {
        display: table-header-group !important;
    }

    tfoot {
        display: table-footer-group !important;
    }

    /* Prevent orphaned table rows */
    tr {
        page-break-inside: avoid !important;
    }

    /* Links */
    a {
        color: #000 !important;
        text-decoration: none !important;
    }

    /* Images */
    img {
        max-width: 100% !important;
        height: auto !important;
    }

    /* Arabic text optimization */
    .arabic-text {
        font-family: 'Cairo', 'Noto Sans Arabic', sans-serif !important;
        direction: auto !important;
        text-align: start !important;
        font-weight: 500 !important;
    }

    /* Status badges for print */
    .status-badge {
        border: 1px solid #000 !important;
        padding: 1px 4px !important;
        font-size: 7pt !important;
        font-weight: bold !important;
        border-radius: 2px !important;
    }

    .status-active {
        background-color: #d4edda !important;
        color: #000 !important;
    }

    .status-upcoming {
        background-color: #d1ecf1 !important;
        color: #000 !important;
    }

    .status-expired {
        background-color: #f8d7da !important;
        color: #000 !important;
    }

    .status-not-planned {
        background-color: #e2e3e5 !important;
        color: #000 !important;
    }

    /* Summary sections */
    .print-summary {
        border: 1px solid #000 !important;
        padding: 8px !important;
        margin-bottom: 15px !important;
        background-color: #f8f9fa !important;
        page-break-inside: avoid !important;
    }

    /* Grid layouts for print */
    .summary-grid {
        display: table !important;
        width: 100% !important;
        border-collapse: collapse !important;
    }

    .summary-item {
        display: table-cell !important;
        text-align: center !important;
        border: 1px solid #ddd !important;
        padding: 8px !important;
        vertical-align: middle !important;
    }

    /* Header and footer */
    .print-header {
        text-align: center !important;
        margin-bottom: 20px !important;
        padding-bottom: 15px !important;
        border-bottom: 2px solid #000 !important;
        page-break-after: avoid !important;
    }

    .print-footer {
        margin-top: 20px !important;
        padding-top: 10px !important;
        border-top: 1px solid #000 !important;
        font-size: 8pt !important;
        page-break-inside: avoid !important;
    }

    /* Column widths for distribution table */
    .col-numero { width: 8% !important; }
    .col-bungalow { width: 22% !important; }
    .col-personnel { width: 25% !important; }
    .col-session { width: 15% !important; }
    .col-periode { width: 18% !important; }
    .col-statut { width: 8% !important; }
    .col-notes { width: 4% !important; }

    /* Text truncation and wrapping */
    .text-truncate {
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: clip !important;
    }

    /* Small text elements */
    small, .small {
        font-size: 8pt !important;
    }

    /* Date formatting */
    .date-text {
        font-size: 8pt !important;
        color: #333 !important;
    }

    /* Notes column */
    .notes-cell {
        font-size: 7pt !important;
        color: #666 !important;
        word-wrap: break-word !important;
        hyphens: auto !important;
    }

    /* Additional Subject Box for Official Documents */
    .additional-subject-box {
        border: 2px solid #000000 !important;
        padding: 8px 12px !important;
        margin: 15px auto !important;
        max-width: 400px !important;
        text-align: center !important;
        direction: rtl !important;
        background-color: #ffffff !important;
        display: block !important;
        visibility: visible !important;
        page-break-inside: avoid !important;
        font-family: Arial, sans-serif !important;
    }

    .additional-subject-box div {
        font-family: Arial, sans-serif !important;
        font-size: 16pt !important;
        font-weight: bold !important;
        line-height: 1.2 !important;
        color: #000000 !important;
        margin: 2px 0 !important;
        direction: rtl !important;
        text-align: center !important;
        text-decoration: underline !important;
        text-decoration-thickness: 1px !important;
        text-underline-offset: 2px !important;
    }

    /* تسطير السطر الأول والثاني */
    .additional-subject-box .first-line {
        text-decoration: underline !important;
        text-decoration-thickness: 1px !important;
        text-underline-offset: 2px !important;
    }

    /* تسطير السطر الثاني أيضاً */
    .additional-subject-box .second-line {
        text-decoration: underline !important;
        text-decoration-thickness: 1px !important;
        text-underline-offset: 2px !important;
    }

    /* تسطير جميع عناصر موضوع الوثيقة */
    .additional-subject-box * {
        text-decoration: underline !important;
        text-decoration-thickness: 1px !important;
        text-underline-offset: 2px !important;
    }

    /* Republic Title */
    .republic-title {
        font-size: 15pt !important;
        font-family: Arial, sans-serif !important;
        font-weight: bold !important;
        text-decoration: underline !important;
        color: #000000 !important;
        direction: rtl !important;
        text-align: center !important;
    }

    /* Document Subject */
    .document-subject {
        border: 2px solid #000000 !important;
        padding: 10px 15px !important;
        margin: 10px auto !important;
        max-width: 500px !important;
        background-color: #ffffff !important;
        text-align: center !important;
        direction: rtl !important;
        page-break-inside: avoid !important;
    }

    .subject-line {
        font-size: 14pt !important;
        font-weight: bold !important;
        font-family: Arial, sans-serif !important;
        color: #000000 !important;
        direction: rtl !important;
        text-align: center !important;
    }

    /* Ministry and Document Number Styles */
    .ministry-line, .military-region, .social-service {
        font-size: 14pt !important;
        font-weight: bold !important;
        font-family: Arial, sans-serif !important;
        color: #000000 !important;
        direction: rtl !important;
        text-align: right !important;
    }

    .number-line {
        font-size: 14pt !important;
        font-weight: bold !important;
        font-family: Arial, sans-serif !important;
        color: #000000 !important;
        direction: rtl !important;
        text-align: right !important;
    }

    /* Director Signature Section */
    .director-signature {
        font-size: 14pt !important;
        font-family: Arial, sans-serif !important;
        text-align: left !important;
        margin-bottom: 20px !important;
    }

    .director-signature div {
        font-size: 14pt !important;
        font-family: Arial, sans-serif !important;
        font-weight: bold !important;
        text-decoration: underline !important;
        text-decoration-thickness: 1px !important;
        text-underline-offset: 2px !important;
    }

    .director-signature * {
        text-decoration: underline !important;
    }

    /* Copy Distribution Section */
    .copy-section {
        font-size: 14pt !important;
        font-family: Arial, sans-serif !important;
        direction: rtl !important;
        text-align: right !important;
        margin-top: 30px !important;
        margin-right: 0 !important;
        padding-right: 0 !important;
    }

    .copy-section div {
        font-size: 14pt !important;
        font-family: Arial, sans-serif !important;
        margin-right: 0 !important;
        padding-right: 0 !important;
    }

    /* Signature section */
    .signature-section {
        margin-top: 30px !important;
        page-break-inside: avoid !important;
    }

    .signature-box {
        border-top: 1px solid #000 !important;
        padding-top: 5px !important;
        margin-top: 40px !important;
        text-align: center !important;
        font-size: 9pt !important;
    }
}

/* Screen styles for print preview */
@media screen {
    .print-preview {
        max-width: 21cm;
        margin: 0 auto;
        background: white;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        padding: 1.5cm 1cm;
    }

    .print-controls {
        position: sticky;
        top: 0;
        background: #f8f9fa;
        padding: 10px;
        border-bottom: 1px solid #ddd;
        z-index: 1000;
        margin-bottom: 20px;
    }

    .print-controls button {
        margin-right: 10px;
    }
}

/* High contrast mode for better printing */
@media print and (prefers-contrast: high) {
    body {
        background: white !important;
        color: black !important;
    }

    .status-badge {
        background-color: white !important;
        color: black !important;
        border: 2px solid black !important;
    }

    .print-summary {
        background-color: white !important;
        border: 2px solid black !important;
    }
}

/* Optimize for different paper sizes */
@media print and (max-width: 8.5in) {
    /* US Letter size adjustments */
    body {
        font-size: 9pt !important;
    }

    table {
        font-size: 8pt !important;
    }

    th {
        font-size: 7pt !important;
    }
}

@media print and (max-width: 21cm) {
    /* A4 size adjustments */
    .col-bungalow { width: 20% !important; }
    .col-personnel { width: 23% !important; }
    .col-session { width: 13% !important; }
    .col-periode { width: 16% !important; }
}

/* Print-specific utilities */
.print-only {
    display: none;
}

@media print {
    .print-only {
        display: block !important;
    }

    .screen-only {
        display: none !important;
    }
}
