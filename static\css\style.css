/* BANGHALAU - Modern Dashboard CSS */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --sidebar-width: 280px;
    --topbar-height: 70px;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--dark-color);
    overflow-x: hidden;
}

.modern-layout {
    background: #f1f5f9;
}

/* Top Navigation Bar */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--topbar-height);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    box-shadow: var(--box-shadow);
}

.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 2rem;
    max-width: 100%;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: white;
    font-weight: 700;
    font-size: 1.25rem;
    text-decoration: none;
}

.navbar-brand i {
    font-size: 1.5rem;
    color: #fbbf24;
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

/* Sidebar (Fixed and Optimized) */
.sidebar {
    position: fixed;
    top: var(--topbar-height);
    left: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--topbar-height));
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 999;
    transform: translateX(-100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

/* Sidebar States (Fixed & Enhanced) */
.sidebar.active,
.sidebar.show {
    transform: translateX(0) !important;
}

.sidebar.collapsed {
    width: 80px !important;
}

.sidebar.collapsed .sidebar-brand div,
.sidebar.collapsed .nav-link span,
.sidebar.collapsed .nav-section-title,
.sidebar.collapsed .nav-badge {
    opacity: 0;
    visibility: hidden;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem;
}

.sidebar.collapsed .nav-link i {
    margin-right: 0;
}

/* Sidebar Toggle Button (Enhanced) */
#sidebarToggle {
    z-index: 1001;
    position: relative;
    cursor: pointer !important;
    transition: all 0.3s ease;
}

#sidebarToggle:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.05);
}

#sidebarToggle:active {
    transform: scale(0.95);
}

/* Force sidebar visibility states */
.sidebar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.sidebar:not(.active):not(.show) {
    transform: translateX(-100%) !important;
}

/* Main content adjustments */
.main-content {
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.sidebar-header {
    padding: 2rem 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
}

.sidebar-brand i {
    font-size: 2rem;
    color: #fbbf24;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar-brand .fw-bold {
    font-size: 1.25rem;
    font-weight: 700;
}

.sidebar-brand small {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    margin: 0.25rem 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.875rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.nav-link i {
    font-size: 1.125rem;
    width: 20px;
    text-align: center;
}

.nav-badge {
    margin-left: auto;
    background: var(--warning-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    min-width: 20px;
    text-align: center;
    font-weight: 600;
}

/* Main Content */
.main-content {
    margin-left: 0;
    margin-top: var(--topbar-height);
    min-height: calc(100vh - var(--topbar-height));
    padding: 2rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.main-content.sidebar-open {
    margin-left: var(--sidebar-width);
}

/* Cards */
.card-modern {
    background: white;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.card-modern:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    padding: 1rem 1.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.625rem 1.25rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

/* Tables */
.table-modern {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table-modern thead th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
}

.table-modern tbody td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.table-modern tbody tr {
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
}

.table-modern tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transform: scale(1.01);
}

/* Main Content Adjustments (Fixed) */
.main-content {
    margin-left: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: calc(100vh - var(--topbar-height));
    padding: 2rem;
}

.main-content.sidebar-open {
    margin-left: var(--sidebar-width);
}

.main-content.expanded {
    margin-left: 80px;
}

/* Body States */
body.sidebar-open {
    overflow-x: hidden;
}

body.sidebar-collapsed .main-content {
    margin-left: 80px;
}

/* Sidebar Overlay (Fixed) */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active,
.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Responsive Design (Fixed) */
@media (min-width: 992px) {
    .sidebar {
        transform: translateX(0);
    }

    .main-content {
        margin-left: var(--sidebar-width);
    }

    .sidebar-overlay {
        display: none !important;
    }

    .sidebar.collapsed + .main-content {
        margin-left: 80px;
    }
}

@media (max-width: 991px) {
    .main-content {
        padding: 1rem;
        margin-left: 0 !important;
    }

    .navbar-container {
        padding: 0 1rem;
    }

    .sidebar {
        top: var(--topbar-height);
        height: calc(100vh - var(--topbar-height));
    }

    .sidebar.active,
    .sidebar.show {
        transform: translateX(0);
    }

    body.sidebar-open {
        overflow: hidden;
    }

    /* Mobile sidebar overlay */
    .sidebar-overlay.active,
    .sidebar-overlay.show {
        display: block !important;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

/* Navigation Sections */
.nav-section {
    margin: 1.5rem 0;
}

.nav-section-title {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 1rem 0.5rem 1rem;
    margin: 1rem 1rem 0.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Dashboard Stats Cards */
.stats-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #fbbf24, #f59e0b);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.stats-icon {
    opacity: 0.3;
    transition: all 0.3s ease;
}

.stats-card:hover .stats-icon {
    opacity: 0.5;
    transform: scale(1.1);
}

/* Enhanced Gradients */
.bg-primary-dark {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color)) !important;
}

.bg-success-dark {
    background: linear-gradient(135deg, #059669, var(--success-color)) !important;
}

.bg-warning-dark {
    background: linear-gradient(135deg, #d97706, var(--warning-color)) !important;
}

.bg-info-dark {
    background: linear-gradient(135deg, #0891b2, var(--info-color)) !important;
}

/* Text Colors */
.text-white-50 {
    color: rgba(255, 255, 255, 0.5) !important;
}

.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Search Results */
.search-container {
    position: relative;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-result-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-result-item:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.search-result-item:last-child {
    border-bottom: none;
}

.selected-item {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #0ea5e9;
    border-radius: var(--border-radius);
    display: none;
    position: relative;
}

.selected-item button {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
}

/* Activity Timeline */
.activity-timeline {
    position: relative;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    position: relative;
    animation: fadeInUp 0.6s ease-out;
}

.activity-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 40px;
    width: 2px;
    height: calc(100% + 0.5rem);
    background: linear-gradient(180deg, #e5e7eb, transparent);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    margin-right: 1rem;
    flex-shrink: 0;
    position: relative;
    z-index: 1;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.activity-content {
    flex: 1;
    padding-top: 5px;
}

/* System Status */
.system-status .status-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.system-status .status-item:last-child {
    border-bottom: none;
}

.system-status .status-item:hover {
    background: rgba(59, 130, 246, 0.05);
    border-radius: var(--border-radius);
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

/* Enhanced Form Controls */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    transform: translateY(-1px);
}



/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}



@keyframes subtitleSlide {
    0% {
        opacity: 0.6;
        transform: translateX(-15px) translateY(0px);
        color: rgba(255, 255, 255, 0.7);
    }
    25% {
        opacity: 0.8;
        transform: translateX(-5px) translateY(-2px);
        color: rgba(251, 191, 36, 0.8);
    }
    50% {
        opacity: 1;
        transform: translateX(5px) translateY(0px);
        color: #fbbf24;
    }
    75% {
        opacity: 0.9;
        transform: translateX(10px) translateY(-1px);
        color: rgba(251, 191, 36, 0.9);
    }
    100% {
        opacity: 0.6;
        transform: translateX(-15px) translateY(0px);
        color: rgba(255, 255, 255, 0.7);
    }
}

@keyframes textShimmer {
    0% {
        background-position: -200% center;
        opacity: 0.8;
    }
    50% {
        background-position: 0% center;
        opacity: 1;
    }
    100% {
        background-position: 200% center;
        opacity: 0.8;
    }
}

@keyframes colorWave {
    0% {
        background: linear-gradient(45deg, #fff, #fbbf24, #fff, #e3f2fd, #fff);
    }
    25% {
        background: linear-gradient(45deg, #fbbf24, #fff, #667eea, #fff, #fbbf24);
    }
    50% {
        background: linear-gradient(45deg, #667eea, #fbbf24, #fff, #667eea, #fff);
    }
    75% {
        background: linear-gradient(45deg, #fff, #667eea, #fbbf24, #fff, #667eea);
    }
    100% {
        background: linear-gradient(45deg, #fff, #fbbf24, #fff, #e3f2fd, #fff);
    }
}



.animated-subtitle {
    animation: subtitleSlide 3.5s ease-in-out infinite;
    font-size: 0.75rem;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    display: inline-block;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}



/* Custom styling for system subtitle */
.system-subtitle {
    color: #fbbf24 !important;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(251, 191, 36, 0.3);
    transition: all 0.3s ease;
}

.system-subtitle:hover {
    color: #f59e0b !important;
    text-shadow: 0 2px 8px rgba(251, 191, 36, 0.5);
    transform: translateY(-1px);
}
