// Modern App JavaScript - Enhanced Version
console.log('🚀 BANGHALAU System Loaded Successfully!');
$(document).ready(function() {
    // Initialize the modern interface
    initializeModernInterface();

    // Load sidebar statistics
    loadSidebarStats();

    // Update stats every 30 seconds
    setInterval(loadSidebarStats, 30000);

    // Initialize search functionality
    initializeSearch();

    // Initialize notifications
    initializeNotifications();
});

function initializeModernInterface() {
    // Unified sidebar toggle functionality (Fixed)
    $('#sidebarToggle, #sidebarCollapse').on('click', function(e) {
        e.preventDefault();
        toggleSidebar();
    });

    // Close sidebar when clicking overlay (mobile)
    $('#sidebarOverlay, .sidebar-overlay').on('click', function() {
        closeSidebar();
    });

    // Handle window resize
    $(window).on('resize', function() {
        handleWindowResize();
    });

    // Add smooth scrolling to navigation links
    $('.nav-link').on('click', function(e) {
        // Simple navigation without loading animation
        if (window.innerWidth <= 768) {
            // Close sidebar on mobile after clicking a link
            setTimeout(closeSidebar, 300);
        }
    });

    // Add hover effects to navigation items
    $('.nav-link').hover(
        function() {
            $(this).find('.nav-icon, i').addClass('animate__animated animate__pulse');
        },
        function() {
            $(this).find('.nav-icon, i').removeClass('animate__animated animate__pulse');
        }
    );
}

// Unified sidebar toggle function (Performance Optimized & Fixed)
function toggleSidebar() {
    console.log('toggleSidebar called, window width:', window.innerWidth);

    const sidebar = $('#sidebar, #modernSidebar');
    const mainContent = $('.main-content, #mainContent, #content');
    const overlay = $('#sidebarOverlay, .sidebar-overlay');

    console.log('Elements found:', {
        sidebar: sidebar.length,
        mainContent: mainContent.length,
        overlay: overlay.length
    });

    if (window.innerWidth <= 768) {
        // Mobile behavior
        console.log('Mobile mode - toggling sidebar');
        sidebar.toggleClass('show active');
        overlay.toggleClass('show active');
        $('body').toggleClass('sidebar-open');

        // Force visibility for mobile
        if (sidebar.hasClass('show')) {
            sidebar.css('transform', 'translateX(0)');
            overlay.css('display', 'block');
        } else {
            sidebar.css('transform', 'translateX(-100%)');
            overlay.css('display', 'none');
        }
    } else {
        // Desktop behavior
        console.log('Desktop mode - toggling sidebar');
        sidebar.toggleClass('collapsed active');
        mainContent.toggleClass('expanded sidebar-open');
        $('body').toggleClass('sidebar-collapsed');

        // Force visibility for desktop
        if (sidebar.hasClass('collapsed')) {
            sidebar.css('width', '80px');
            mainContent.css('margin-left', '80px');
        } else {
            sidebar.css('width', '280px');
            mainContent.css('margin-left', '280px');
        }
    }

    console.log('Sidebar classes after toggle:', sidebar.attr('class'));
}

// Close sidebar function
function closeSidebar() {
    const sidebar = $('#sidebar, #modernSidebar');
    const mainContent = $('.main-content, #mainContent, #content');
    const overlay = $('#sidebarOverlay, .sidebar-overlay');

    sidebar.removeClass('show active');
    overlay.removeClass('show active');
    mainContent.removeClass('sidebar-open');
    $('body').removeClass('sidebar-open');
}

// Handle window resize
function handleWindowResize() {
    const sidebar = $('#sidebar, #modernSidebar');
    const overlay = $('#sidebarOverlay, .sidebar-overlay');
    const mainContent = $('.main-content, #mainContent, #content');

    if (window.innerWidth > 768) {
        // Desktop: show sidebar, hide overlay
        sidebar.removeClass('show').addClass('active');
        overlay.removeClass('show active');
        mainContent.addClass('sidebar-open');
        $('body').removeClass('sidebar-open');
    } else {
        // Mobile: hide sidebar
        sidebar.removeClass('active');
        mainContent.removeClass('sidebar-open');
    }
}

// Initialize sidebar on page load (Performance Optimized & Fixed)
function initializeSidebar() {
    console.log('Initializing sidebar...');

    // Set initial state based on screen size
    const sidebar = $('#sidebar, #modernSidebar');
    const mainContent = $('.main-content, #mainContent, #content');

    if (window.innerWidth > 768) {
        // Desktop: show sidebar by default
        sidebar.addClass('active').css({
            'transform': 'translateX(0)',
            'width': '280px'
        });
        mainContent.addClass('sidebar-open').css('margin-left', '280px');
        console.log('Desktop mode: sidebar shown by default');
    } else {
        // Mobile: hide sidebar by default
        sidebar.removeClass('active show').css({
            'transform': 'translateX(-100%)',
            'width': '280px'
        });
        mainContent.removeClass('sidebar-open').css('margin-left', '0');
        console.log('Mobile mode: sidebar hidden by default');
    }

    // Add keyboard support
    $(document).off('keydown.sidebar').on('keydown.sidebar', function(e) {
        // ESC key closes sidebar on mobile
        if (e.key === 'Escape' && window.innerWidth <= 768) {
            closeSidebar();
        }

        // Ctrl+B toggles sidebar
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            toggleSidebar();
        }
    });

    console.log('Sidebar initialized successfully');
}

function loadSidebarStats() {
    // Load bungalow count
    $.get('/api/bungalows/count')
        .done(function(data) {
            updateCounter('#bungalowCount', data.count || 0);
        })
        .fail(function() {
            $('#bungalowCount').text('?').addClass('error');
        });

    // Load personnel count
    $.get('/api/personnel/count')
        .done(function(data) {
            updateCounter('#personnelCount', data.count || 0);
        })
        .fail(function() {
            $('#personnelCount').text('?').addClass('error');
        });

    // Load distribution count
    $.get('/api/distributions/count')
        .done(function(data) {
            updateCounter('#distributionCount', data.count || 0);
        })
        .fail(function() {
            $('#distributionCount').text('?').addClass('error');
        });
}

function updateCounter(selector, newValue) {
    const $counter = $(selector);
    const currentValue = parseInt($counter.text()) || 0;

    if (currentValue !== newValue) {
        $counter.addClass('animate__animated animate__bounceIn');

        // Animate counter
        $({ counter: currentValue }).animate({ counter: newValue }, {
            duration: 1000,
            easing: 'swing',
            step: function() {
                $counter.text(Math.ceil(this.counter));
            },
            complete: function() {
                $counter.text(newValue);
                setTimeout(() => {
                    $counter.removeClass('animate__animated animate__bounceIn');
                }, 1000);
            }
        });
    }
}

function initializeSearch() {
    const searchInput = $('#globalSearch');
    let searchTimeout;

    searchInput.on('input', function() {
        const query = $(this).val().trim();

        clearTimeout(searchTimeout);

        if (query.length > 2) {
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, 300);
        } else {
            clearSearchResults();
        }
    });

    // Handle search on Enter key
    searchInput.on('keypress', function(e) {
        if (e.which === 13) {
            const query = $(this).val().trim();
            if (query.length > 0) {
                performSearch(query);
            }
        }
    });
}

function performSearch(query) {
    // Perform search API call
    $.get('/api/search', { q: query })
        .done(function(data) {
            displaySearchResults(data);
        })
        .fail(function() {
            showSearchError();
        });
}

function displaySearchResults(results) {
    // Display results (implement based on your needs)
    console.log('Search results:', results);
}

function clearSearchResults() {
    // Clear any displayed results
}

function showSearchError() {
    // Show error message
    showNotification('Erreur lors de la recherche', 'error');
}

function initializeNotifications() {
    // Initialize notification system
    loadNotifications();

    // Handle notification button click
    $('.quick-btn').on('click', function() {
        const icon = $(this).find('i');

        if (icon.hasClass('fa-bell')) {
            toggleNotificationPanel();
        } else if (icon.hasClass('fa-plus')) {
            // Handle quick add action
            window.location.href = '/distributions/new';
        }
    });
}

function loadNotifications() {
    // Load notifications from API
    $.get('/api/notifications')
        .done(function(data) {
            updateNotificationBadge(data.unread_count || 0);
        })
        .fail(function() {
            updateNotificationBadge(0);
        });
}

function updateNotificationBadge(count) {
    const badge = $('.notification-badge');

    if (count > 0) {
        badge.text(count).show();
        badge.addClass('animate__animated animate__pulse');
    } else {
        badge.hide();
    }
}

function toggleNotificationPanel() {
    // Toggle notification panel (implement based on your needs)
    console.log('Toggle notification panel');
}

function showNotification(message, type = 'info') {
    // Create and show notification toast
    const notification = $(`
        <div class="notification notification-${type} animate__animated animate__slideInRight">
            <div class="notification-content">
                <i class="fas fa-${getNotificationIcon(type)} me-2"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `);

    // Add to notification container (create if doesn't exist)
    let container = $('.notification-container');
    if (container.length === 0) {
        container = $('<div class="notification-container"></div>');
        $('body').append(container);
    }

    container.append(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.addClass('animate__slideOutRight');
        setTimeout(() => {
            notification.remove();
        }, 500);
    }, 5000);

    // Handle close button
    notification.find('.notification-close').on('click', function() {
        notification.addClass('animate__slideOutRight');
        setTimeout(() => {
            notification.remove();
        }, 500);
    });
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

// Utility functions
function formatNumber(num) {
    return new Intl.NumberFormat('fr-FR').format(num);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(date));
}

function formatTime(date) {
    return new Intl.DateTimeFormat('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

// Export functions for global use
window.modernApp = {
    showNotification,
    loadSidebarStats,
    updateCounter,
    formatNumber,
    formatDate,
    formatTime
};
