<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة بنغل جديد - BANGHALAU</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 10px;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: bold;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }
        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .alert {
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h3 class="mb-0">
                            <i class="fas fa-plus-circle me-2"></i>إضافة بنغل جديد
                        </h3>
                        <p class="mb-0 mt-2">نموذج بسيط لإضافة بنغل جديد</p>
                    </div>
                    <div class="card-body p-4">
                        
                        <form method="POST" action="/bungalows/add">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="numero" class="form-label fw-bold">
                                        <i class="fas fa-hashtag me-1"></i>رقم البنغل *
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="numero" 
                                           name="numero" 
                                           placeholder="مثال: B001" 
                                           required>
                                    <small class="form-text text-muted">أدخل رقم البنغل (مطلوب)</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="endroit" class="form-label fw-bold">
                                        <i class="fas fa-map-marker-alt me-1"></i>المكان *
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="endroit" 
                                           name="endroit" 
                                           placeholder="مثال: المنطقة الشمالية" 
                                           required>
                                    <small class="form-text text-muted">أدخل موقع البنغل (مطلوب)</small>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="capacite" class="form-label fw-bold">
                                        <i class="fas fa-users me-1"></i>السعة (أشخاص) *
                                    </label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="capacite" 
                                           name="capacite" 
                                           min="1" 
                                           max="20" 
                                           placeholder="مثال: 4" 
                                           required>
                                    <small class="form-text text-muted">عدد الأشخاص (1-20)</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="statut" class="form-label fw-bold">
                                        <i class="fas fa-info-circle me-1"></i>حالة البنغل *
                                    </label>
                                    <select class="form-control" id="statut" name="statut" required>
                                        <option value="">اختر الحالة</option>
                                        <option value="Disponible">متاح</option>
                                        <option value="Occupé">مشغول</option>
                                        <option value="Maintenance">صيانة</option>
                                        <option value="Réservé">محجوز</option>
                                        <option value="Hors Service">خارج الخدمة</option>
                                        <option value="En Rénovation">قيد التجديد</option>
                                    </select>
                                    <small class="form-text text-muted">اختر حالة البنغل الحالية</small>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="caracteristiques" class="form-label fw-bold">
                                        <i class="fas fa-list-ul me-1"></i>الخصائص
                                    </label>
                                    <textarea class="form-control" 
                                              id="caracteristiques" 
                                              name="caracteristiques" 
                                              rows="3" 
                                              placeholder="مثال: مكيف، حديقة، إطلالة على البحر..."></textarea>
                                    <small class="form-text text-muted">وصف خصائص البنغل (اختياري)</small>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12 mb-4">
                                    <label for="notes_statut" class="form-label fw-bold">
                                        <i class="fas fa-sticky-note me-1"></i>ملاحظات الحالة
                                    </label>
                                    <textarea class="form-control" 
                                              id="notes_statut" 
                                              name="notes_statut" 
                                              rows="2" 
                                              placeholder="مثال: صيانة مجدولة، مشكلة في السباكة..."></textarea>
                                    <small class="form-text text-muted">ملاحظات إضافية حول حالة البنغل (اختياري)</small>
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-save me-2"></i>حفظ البنغل
                                </button>
                                <a href="/bungalows" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                            </div>
                        </form>

                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="text-center mt-4">
                    <a href="/dashboard" class="btn btn-outline-primary me-2">
                        <i class="fas fa-home me-1"></i>لوحة التحكم
                    </a>
                    <a href="/bungalows" class="btn btn-outline-info me-2">
                        <i class="fas fa-list me-1"></i>قائمة البنغلات
                    </a>
                    <a href="/test_form" class="btn btn-outline-warning">
                        <i class="fas fa-flask me-1"></i>صفحة الاختبار
                    </a>
                </div>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
</body>
</html>
