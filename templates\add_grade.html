{% extends "base.html" %}

{% block title %}Ajouter un Grade - BANGHALAU{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="card-title">
                            <i class="fas fa-plus-circle me-2 text-primary"></i>
                            Ajouter un Nouveau Grade
                        </h2>
                        <a href="{{ url_for('grades') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="grade" class="form-label">
                                    <i class="fas fa-star me-1"></i>Nom du Grade *
                                </label>
                                <input type="text"
                                       class="form-control arabic-input"
                                       id="grade"
                                       name="grade"
                                       placeholder="Ex: Capitaine, Sergent, Colonel..."
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir le nom du grade.
                                </div>
                                <small class="form-text text-muted">
                                    Nom officiel du grade militaire
                                </small>
                            </div>
                        </div>


                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>Description
                                </label>
                                <textarea class="form-control arabic-input"
                                          id="description"
                                          name="description"
                                          rows="3"
                                          placeholder="Description détaillée du grade, responsabilités, etc."></textarea>
                                <small class="form-text text-muted">
                                    Description optionnelle du grade et de ses responsabilités
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Exemples de grades par niveau -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-lightbulb text-warning me-2"></i>
                                        Exemples de Grades par Niveau
                                    </h6>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-secondary">
                                                <i class="fas fa-user me-1"></i>Soldats et Caporaux
                                            </h6>
                                            <div class="example-grades">
                                                <div class="example-grade" onclick="fillExample('Soldat', 'Soldat de base')">
                                                    <strong>Soldat</strong> - Soldat de base
                                                </div>
                                                <div class="example-grade" onclick="fillExample('Caporal', 'Caporal chef d\'équipe')">
                                                    <strong>Caporal</strong> - Caporal chef d'équipe
                                                </div>
                                                <div class="example-grade" onclick="fillExample('Caporal-Chef', 'Caporal-Chef expérimenté')">
                                                    <strong>Caporal-Chef</strong> - Caporal-Chef expérimenté
                                                </div>
                                            </div>

                                            <h6 class="text-info mt-3">
                                                <i class="fas fa-user-tie me-1"></i>Sous-officiers
                                            </h6>
                                            <div class="example-grades">
                                                <div class="example-grade" onclick="fillExample('Sergent', 'Sergent chef de section')">
                                                    <strong>Sergent</strong> - Sergent chef de section
                                                </div>
                                                <div class="example-grade" onclick="fillExample('Sergent-Chef', 'Sergent-Chef expérimenté')">
                                                    <strong>Sergent-Chef</strong> - Sergent-Chef expérimenté
                                                </div>
                                                <div class="example-grade" onclick="fillExample('Adjudant', 'Adjudant sous-officier supérieur')">
                                                    <strong>Adjudant</strong> - Adjudant sous-officier supérieur
                                                </div>
                                            </div>

                                            <h6 class="text-primary mt-3">
                                                <i class="fas fa-user-graduate me-1"></i>Officiers subalternes
                                            </h6>
                                            <div class="example-grades">
                                                <div class="example-grade" onclick="fillExample('Sous-Lieutenant', 'Sous-Lieutenant débutant')">
                                                    <strong>Sous-Lieutenant</strong> - Sous-Lieutenant débutant
                                                </div>
                                                <div class="example-grade" onclick="fillExample('Lieutenant', 'Lieutenant chef de peloton')">
                                                    <strong>Lieutenant</strong> - Lieutenant chef de peloton
                                                </div>
                                                <div class="example-grade" onclick="fillExample('Capitaine', 'Capitaine commandant de compagnie')">
                                                    <strong>Capitaine</strong> - Capitaine commandant de compagnie
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <h6 class="text-warning">
                                                <i class="fas fa-user-shield me-1"></i>Officiers supérieurs
                                            </h6>
                                            <div class="example-grades">
                                                <div class="example-grade" onclick="fillExample('Commandant', 'Commandant chef de bataillon')">
                                                    <strong>Commandant</strong> - Commandant chef de bataillon
                                                </div>
                                                <div class="example-grade" onclick="fillExample('Lieutenant-Colonel', 'Lieutenant-Colonel adjoint')">
                                                    <strong>Lieutenant-Colonel</strong> - Lieutenant-Colonel adjoint
                                                </div>
                                                <div class="example-grade" onclick="fillExample('Colonel', 'Colonel chef de régiment')">
                                                    <strong>Colonel</strong> - Colonel chef de régiment
                                                </div>
                                            </div>

                                            <h6 class="text-danger mt-3">
                                                <i class="fas fa-crown me-1"></i>Officiers généraux
                                            </h6>
                                            <div class="example-grades">
                                                <div class="example-grade" onclick="fillExample('Général de Brigade', 'Général de Brigade')">
                                                    <strong>Général de Brigade</strong> - Général de Brigade
                                                </div>
                                                <div class="example-grade" onclick="fillExample('Général de Division', 'Général de Division')">
                                                    <strong>Général de Division</strong> - Général de Division
                                                </div>
                                                <div class="example-grade" onclick="fillExample('Général de Corps d\'Armée', 'Général de Corps d\'Armée')">
                                                    <strong>Général de Corps d'Armée</strong> - Général de Corps d'Armée
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <small class="text-muted">
                                        <i class="fas fa-mouse-pointer me-1"></i>
                                        Cliquez sur un exemple pour remplir automatiquement le formulaire
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('grades') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Enregistrer le Grade
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* خطوط عربية محسنة لإضافة الرتب */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

/* تحسين حقل اسم الرتبة */
#grade {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
    line-height: 1.5;
    direction: auto;
    text-align: start;
    font-size: 1rem;
}

/* تحسين حقل الوصف */
#description {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.6;
    direction: auto;
    text-align: start;
    font-size: 1rem;
    resize: vertical;
    min-height: 100px;
}

/* تحسين النماذج العربية */
.form-control.arabic-input {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين التسميات */
.form-label {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين النصوص المساعدة */
.form-text {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين العناوين */
.card-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

/* تحسين التنبيهات */
.alert {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين الأزرار */
.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين أمثلة الرتب */
.example-grades {
    margin-bottom: 1rem;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

.example-grade {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

.example-grade:hover {
    background-color: #f8fafc;
    border-color: #3b82f6;
    transform: translateY(-1px);
}

.example-grade strong {
    color: #3b82f6;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

/* تحسين رسائل التحقق */
.invalid-feedback, .valid-feedback {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    #grade, #description {
        font-size: 0.95rem;
    }
}

/* Toast notification styling */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    animation: slideInRight 0.3s ease;
}

.toast-notification.success {
    background: #10b981;
}

.toast-notification.error {
    background: #ef4444;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Real-time validation feedback
    $('input[required], textarea[required]').on('input', function() {
        if ($(this).val().trim() !== '') {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });

    // Character counter for description
    $('#description').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;

        if (!$('#charCounter').length) {
            $(this).after(`<small id="charCounter" class="form-text text-muted"></small>`);
        }

        $('#charCounter').text(`${currentLength} caractères`);

        if (currentLength > maxLength * 0.9) {
            $('#charCounter').removeClass('text-muted').addClass('text-warning');
        } else {
            $('#charCounter').removeClass('text-warning').addClass('text-muted');
        }
    });

    // Grade name formatting
    $('#grade').on('input', function() {
        let value = $(this).val();
        // Capitalize first letter of each word
        value = value.replace(/\b\w/g, l => l.toUpperCase());
        $(this).val(value);
    });
});

function fillExample(grade, description) {
    $('#grade').val(grade);
    $('#description').val(description);

    // Trigger validation
    $('#grade').trigger('input');
    $('#description').trigger('input');

    // Show success feedback
    showToast('Exemple rempli avec succès', 'success');
}

function showToast(message, type = 'info') {
    const toast = $(`
        <div class="toast-notification ${type}">
            <i class="fas fa-check-circle me-2"></i>
            ${message}
        </div>
    `);

    $('body').append(toast);

    setTimeout(() => {
        toast.fadeOut(() => toast.remove());
    }, 3000);
}
</script>
{% endblock %}
