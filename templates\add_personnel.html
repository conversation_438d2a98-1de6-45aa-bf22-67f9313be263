{% extends "base.html" %}

{% block title %}Ajouter du Personnel - BANGHALAU{% endblock %}

{% block extra_css %}
<style>
/* خطوط عربية محسنة لإضافة الأفراد */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* تحسين حقول الأسماء */
#nom, #prenom {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.5;
    direction: auto;
    text-align: start;
    font-size: 1rem;
}

/* تحسين قوائم الاختيار */
#grade_id, #unite_id {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

#grade_id option, #unite_id option {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين النماذج العربية */
.form-control.arabic-input {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين التسميات */
.form-label {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين النصوص المساعدة */
.form-text {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين العناوين */
.card-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

/* تحسين التنبيهات */
.alert {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين الأزرار */
.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين رسائل التحقق */
.invalid-feedback, .valid-feedback {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    #nom, #prenom {
        font-size: 0.95rem;
    }
}

/* تحسين مظهر البحث للوحدات */
.unite-search-container {
    position: relative;
}

#unite_dropdown {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    background: white;
}

#unite_dropdown .dropdown-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.2s ease;
    cursor: pointer;
}

#unite_dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

#unite_dropdown .dropdown-item:last-child {
    border-bottom: none;
}

#unite_search {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m11.25 11.25 3 3m-3-3a6 6 0 1 1-12 0 6 6 0 0 1 12 0Z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
}

.unite-option .badge {
    font-size: 0.7rem;
}

.unite-option small {
    font-size: 0.75rem;
    opacity: 0.7;
}

.unite-option .fw-bold {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="card-title">
                            <i class="fas fa-user-plus me-2 text-primary"></i>
                            Ajouter un Nouveau Personnel
                        </h2>
                        <a href="{{ url_for('personnel') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="numero" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>Numéro Matricule *
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="numero"
                                       name="numero"
                                       placeholder="Ex: M001"
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir un numéro matricule.
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="nom" class="form-label">
                                    <i class="fas fa-user me-1"></i>Nom de Famille *
                                </label>
                                <input type="text"
                                       class="form-control arabic-input"
                                       id="nom"
                                       name="nom"
                                       placeholder="Ex: Dupont"
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir le nom de famille.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="prenom" class="form-label">
                                    <i class="fas fa-user me-1"></i>Prénom *
                                </label>
                                <input type="text"
                                       class="form-control arabic-input"
                                       id="prenom"
                                       name="prenom"
                                       placeholder="Ex: Jean"
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir le prénom.
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="grade_id" class="form-label">
                                    <i class="fas fa-star me-1"></i>Grade *
                                </label>
                                <select class="form-control" id="grade_id" name="grade_id" required>
                                    <option value="">Sélectionnez un grade</option>
                                    {% for grade in grades %}
                                        <option value="{{ grade.id }}">{{ grade.grade }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner un grade.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="unite_search" class="form-label">
                                    <i class="fas fa-flag me-1"></i>Unité *
                                </label>
                                <div class="position-relative">
                                    <input type="text"
                                           class="form-control"
                                           id="unite_search"
                                           placeholder="Rechercher une unité..."
                                           autocomplete="off">
                                    <input type="hidden" id="unite_id" name="unite_id" required>
                                    <div class="position-absolute top-100 start-0 w-100" style="z-index: 1000;">
                                        <div id="unite_dropdown" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto; display: none;">
                                            {% for unite in unites %}
                                            <a class="dropdown-item unite-option"
                                               href="#"
                                               data-id="{{ unite.id }}"
                                               data-description="{{ unite.description }}"
                                               data-raccourci="{{ unite.raccourci }}">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="fw-bold">{{ unite.description }}</span>
                                                    <span class="badge bg-primary">{{ unite.raccourci }}</span>
                                                </div>
                                                <small class="text-muted">ID: {{ unite.id }}</small>
                                            </a>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner une unité.
                                </div>
                                <small class="form-text text-muted">
                                    <i class="fas fa-search me-1"></i>Tapez pour rechercher une unité
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="telephone" class="form-label">
                                    <i class="fas fa-phone me-1"></i>Téléphone
                                </label>
                                <input type="tel"
                                       class="form-control"
                                       id="telephone"
                                       name="telephone"
                                       placeholder="Ex: +33 1 23 45 67 89">
                                <small class="form-text text-muted">
                                    Numéro de téléphone (optionnel)
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Email
                                </label>
                                <input type="email"
                                       class="form-control"
                                       id="email"
                                       name="email"
                                       placeholder="Ex: <EMAIL>">
                                <small class="form-text text-muted">
                                    Adresse email (optionnel)
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="region" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>Région Militaire
                                </label>
                                <select class="form-control" id="region" name="region">
                                    <option value="">Sélectionner une région</option>
                                    <option value="الناحية العسكرية الأولى">الناحية العسكرية الأولى</option>
                                    <option value="الناحية العسكرية الثانية">الناحية العسكرية الثانية</option>
                                    <option value="الناحية العسكرية الثالثة">الناحية العسكرية الثالثة</option>
                                    <option value="الناحية العسكرية الرابعة">الناحية العسكرية الرابعة</option>
                                    <option value="الناحية العسكرية الخامسة">الناحية العسكرية الخامسة</option>
                                    <option value="الناحية العسكرية السادسة">الناحية العسكرية السادسة</option>
                                </select>
                                <small class="form-text text-muted">
                                    Région militaire d'affectation (optionnel)
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('personnel') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Enregistrer le Personnel
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Auto-generate numero suggestion
    $('#numero').on('blur', function() {
        const value = $(this).val().toUpperCase();
        if (value && !value.startsWith('M')) {
            $(this).val('M' + value.padStart(3, '0'));
        }
    });

    // Email validation
    $('#email').on('input', function() {
        const email = $(this).val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (email && !emailRegex.test(email)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid').addClass('is-valid');
        }
    });

    // Phone validation
    $('#telephone').on('input', function() {
        const phone = $(this).val();
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;

        if (phone && !phoneRegex.test(phone)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid').addClass('is-valid');
        }
    });

    // Real-time validation feedback
    $('input[required], select[required]').on('change input', function() {
        if ($(this).val().trim() !== '') {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });

    // Unite search functionality
    let uniteOptions = [];
    $('.unite-option').each(function() {
        uniteOptions.push({
            id: $(this).data('id'),
            description: $(this).data('description'),
            raccourci: $(this).data('raccourci'),
            element: $(this)
        });
    });

    $('#unite_search').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        const dropdown = $('#unite_dropdown');

        if (searchTerm.length === 0) {
            dropdown.hide();
            $('#unite_id').val('');
            return;
        }

        // Filter options
        let hasResults = false;
        $('.unite-option').each(function() {
            const description = $(this).data('description').toLowerCase();
            const raccourci = $(this).data('raccourci').toLowerCase();

            if (description.includes(searchTerm) || raccourci.includes(searchTerm)) {
                $(this).show();
                hasResults = true;
            } else {
                $(this).hide();
            }
        });

        if (hasResults) {
            dropdown.show();
        } else {
            dropdown.hide();
        }
    });

    // Handle unite selection
    $(document).on('click', '.unite-option', function(e) {
        e.preventDefault();
        const id = $(this).data('id');
        const description = $(this).data('description');
        const raccourci = $(this).data('raccourci');

        $('#unite_search').val(`${description} (${raccourci})`);
        $('#unite_id').val(id);
        $('#unite_dropdown').hide();

        // Trigger validation
        $('#unite_id').trigger('change');
    });

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.position-relative').length) {
            $('#unite_dropdown').hide();
        }
    });

    // Show all options when focusing on empty search
    $('#unite_search').on('focus', function() {
        if ($(this).val() === '') {
            $('.unite-option').show();
            $('#unite_dropdown').show();
        }
    });

    // Clear selection when search is cleared
    $('#unite_search').on('keyup', function() {
        if ($(this).val() === '') {
            $('#unite_id').val('');
        }
    });
});
</script>
{% endblock %}
