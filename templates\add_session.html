{% extends "base.html" %}

{% block title %}Ajouter une Session - BANGHALAU{% endblock %}

{% block extra_css %}
<!-- استيراد الخطوط العربية المحسنة -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
/* خطوط عربية محسنة لصفحة إضافة Session */
:root {
    --arabic-font-primary: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --mixed-font: 'Cairo', 'Noto Sans Arabic', 'Inter', 'Segoe UI', sans-serif;
}

/* تحسين عام للنصوص */
body, .card, .btn, .form-control, .form-select {
    font-family: var(--mixed-font);
    font-feature-settings: 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* العناوين */
.card-title, h1, h2, h3, h4, h5, h6 {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    line-height: 1.3;
}

/* النماذج */
.form-control, .form-select {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
    line-height: 1.5;
}

.form-label {
    font-family: var(--mixed-font);
    font-weight: 500;
}

/* الأزرار */
.btn {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* التنبيهات */
.alert {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.5;
}

/* النصوص العربية */
.arabic-text {
    font-family: var(--arabic-font-primary);
    font-weight: 500;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="card-title">
                            <i class="fas fa-plus-circle me-2 text-primary"></i>
                            Ajouter une Nouvelle Session
                        </h2>
                        <a href="{{ url_for('sessions') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="numero" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>Numéro de Session *
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="numero" 
                                       name="numero" 
                                       placeholder="Ex: S001, S002, SESSION-2024-01..."
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir le numéro de session.
                                </div>
                                <small class="form-text text-muted">
                                    Numéro unique d'identification de la session
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="etat_session" class="form-label">
                                    <i class="fas fa-chart-pie me-1"></i>Statut de la Session
                                </label>
                                <select class="form-control" id="etat_session" name="etat_session">
                                    <option value="">Sélectionnez un statut</option>
                                    <option value="Planifiée">Planifiée</option>
                                    <option value="En cours">En cours</option>
                                    <option value="Terminée">Terminée</option>
                                    <option value="Annulée">Annulée</option>
                                </select>
                                <small class="form-text text-muted">
                                    Statut actuel de la session (optionnel)
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>Description *
                                </label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="3"
                                          placeholder="Description détaillée de la session de formation..."
                                          required></textarea>
                                <div class="invalid-feedback">
                                    Veuillez saisir une description.
                                </div>
                                <small class="form-text text-muted">
                                    Description détaillée de la session et de ses objectifs
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_debut" class="form-label">
                                    <i class="fas fa-calendar-start me-1"></i>Date de Début *
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="date_debut" 
                                       name="date_debut" 
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner la date de début.
                                </div>
                                <small class="form-text text-muted">
                                    Date de commencement de la session
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_fin" class="form-label">
                                    <i class="fas fa-calendar-end me-1"></i>Date de Fin
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="date_fin" 
                                       name="date_fin">
                                <small class="form-text text-muted">
                                    Date de fin prévue (optionnel)
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Exemples de sessions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-lightbulb text-warning me-2"></i>
                                        Exemples de Sessions
                                    </h6>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-primary">
                                                <i class="fas fa-graduation-cap me-1"></i>Sessions de Formation
                                            </h6>
                                            <div class="example-sessions">
                                                <div class="example-session" onclick="fillExample('FORM-2024-01', 'Formation Initiale des Recrues', 'Planifiée')">
                                                    <strong>FORM-2024-01</strong> - Formation Initiale des Recrues
                                                </div>
                                                <div class="example-session" onclick="fillExample('FORM-2024-02', 'Formation Continue des Sous-Officiers', 'En cours')">
                                                    <strong>FORM-2024-02</strong> - Formation Continue des Sous-Officiers
                                                </div>
                                                <div class="example-session" onclick="fillExample('FORM-2024-03', 'Stage de Perfectionnement Officiers', 'Planifiée')">
                                                    <strong>FORM-2024-03</strong> - Stage de Perfectionnement Officiers
                                                </div>
                                            </div>

                                            <h6 class="text-success mt-3">
                                                <i class="fas fa-shield-alt me-1"></i>Sessions Spécialisées
                                            </h6>
                                            <div class="example-sessions">
                                                <div class="example-session" onclick="fillExample('SPEC-2024-01', 'Formation Sécurité et Défense', 'Planifiée')">
                                                    <strong>SPEC-2024-01</strong> - Formation Sécurité et Défense
                                                </div>
                                                <div class="example-session" onclick="fillExample('SPEC-2024-02', 'Formation Logistique Militaire', 'Planifiée')">
                                                    <strong>SPEC-2024-02</strong> - Formation Logistique Militaire
                                                </div>
                                                <div class="example-session" onclick="fillExample('SPEC-2024-03', 'Formation Communication Tactique', 'Planifiée')">
                                                    <strong>SPEC-2024-03</strong> - Formation Communication Tactique
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <h6 class="text-warning">
                                                <i class="fas fa-dumbbell me-1"></i>Sessions d'Entraînement
                                            </h6>
                                            <div class="example-sessions">
                                                <div class="example-session" onclick="fillExample('ENTR-2024-01', 'Entraînement Physique Intensif', 'En cours')">
                                                    <strong>ENTR-2024-01</strong> - Entraînement Physique Intensif
                                                </div>
                                                <div class="example-session" onclick="fillExample('ENTR-2024-02', 'Exercices de Terrain', 'Planifiée')">
                                                    <strong>ENTR-2024-02</strong> - Exercices de Terrain
                                                </div>
                                                <div class="example-session" onclick="fillExample('ENTR-2024-03', 'Simulation de Combat', 'Planifiée')">
                                                    <strong>ENTR-2024-03</strong> - Simulation de Combat
                                                </div>
                                            </div>

                                            <h6 class="text-info mt-3">
                                                <i class="fas fa-users me-1"></i>Sessions de Perfectionnement
                                            </h6>
                                            <div class="example-sessions">
                                                <div class="example-session" onclick="fillExample('PERF-2024-01', 'Leadership et Management', 'Planifiée')">
                                                    <strong>PERF-2024-01</strong> - Leadership et Management
                                                </div>
                                                <div class="example-session" onclick="fillExample('PERF-2024-02', 'Techniques Avancées de Combat', 'Planifiée')">
                                                    <strong>PERF-2024-02</strong> - Techniques Avancées de Combat
                                                </div>
                                                <div class="example-session" onclick="fillExample('PERF-2024-03', 'Formation aux Nouvelles Technologies', 'Planifiée')">
                                                    <strong>PERF-2024-03</strong> - Formation aux Nouvelles Technologies
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <small class="text-muted">
                                        <i class="fas fa-mouse-pointer me-1"></i>
                                        Cliquez sur un exemple pour remplir automatiquement le formulaire
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('sessions') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Enregistrer la Session
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.example-sessions {
    margin-bottom: 1rem;
}

.example-session {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.example-session:hover {
    background-color: #f8fafc;
    border-color: #3b82f6;
    transform: translateY(-1px);
}

.example-session strong {
    color: #3b82f6;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Real-time validation feedback
    $('input[required], textarea[required]').on('input', function() {
        if ($(this).val().trim() !== '') {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    // Date validation
    $('#date_debut, #date_fin').on('change', function() {
        const dateDebut = $('#date_debut').val();
        const dateFin = $('#date_fin').val();
        
        if (dateDebut && dateFin && dateFin < dateDebut) {
            $('#date_fin')[0].setCustomValidity('La date de fin doit être postérieure à la date de début');
            $('#date_fin').addClass('is-invalid');
        } else {
            $('#date_fin')[0].setCustomValidity('');
            $('#date_fin').removeClass('is-invalid');
        }
    });
    
    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    $('#date_debut').val(today);
    
    // Character counter for description
    $('#description').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;
        
        if (!$('#charCounter').length) {
            $(this).after(`<small id="charCounter" class="form-text text-muted"></small>`);
        }
        
        $('#charCounter').text(`${currentLength} caractères`);
        
        if (currentLength > maxLength * 0.9) {
            $('#charCounter').removeClass('text-muted').addClass('text-warning');
        } else {
            $('#charCounter').removeClass('text-warning').addClass('text-muted');
        }
    });
    
    // Session number formatting
    $('#numero').on('input', function() {
        let value = $(this).val();
        // Convert to uppercase
        value = value.toUpperCase();
        $(this).val(value);
    });
});

function fillExample(numero, description, status) {
    $('#numero').val(numero);
    $('#description').val(description);
    $('#etat_session').val(status);
    
    // Set dates (start today, end in 30 days)
    const today = new Date();
    const endDate = new Date(today);
    endDate.setDate(today.getDate() + 30);
    
    $('#date_debut').val(today.toISOString().split('T')[0]);
    $('#date_fin').val(endDate.toISOString().split('T')[0]);
    
    // Trigger validation
    $('#numero').trigger('input');
    $('#description').trigger('input');
    
    // Show success feedback
    showToast('Exemple rempli avec succès', 'success');
}

function showToast(message, type = 'info') {
    const toast = $(`
        <div class="toast-notification ${type}">
            <i class="fas fa-check-circle me-2"></i>
            ${message}
        </div>
    `);
    
    $('body').append(toast);
    
    setTimeout(() => {
        toast.fadeOut(() => toast.remove());
    }, 3000);
}
</script>

<style>
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    animation: slideInRight 0.3s ease;
}

.toast-notification.success {
    background: #10b981;
}

.toast-notification.error {
    background: #ef4444;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
{% endblock %}
