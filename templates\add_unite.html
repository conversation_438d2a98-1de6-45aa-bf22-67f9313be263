{% extends "base.html" %}

{% block title %}Ajouter une Unité - BANGHALAU{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="card-title">
                            <i class="fas fa-plus-circle me-2 text-primary"></i>
                            Ajouter une Nouvelle Unité
                        </h2>
                        <a href="{{ url_for('unites') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="numero" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>Numéro de l'Unité *
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="numero"
                                       name="numero"
                                       placeholder="Ex: U001"
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir un numéro d'unité.
                                </div>
                                <small class="form-text text-muted">
                                    Numéro unique pour identifier l'unité
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="raccourci" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Raccourci
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="raccourci"
                                       name="raccourci"
                                       placeholder="Ex: INF, CAV, ART"
                                       maxlength="10">
                                <small class="form-text text-muted">
                                    Abréviation de l'unité (optionnel, max 10 caractères)
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>Description de l'Unité *
                                </label>
                                <textarea class="form-control arabic-input"
                                          id="description"
                                          name="description"
                                          rows="4"
                                          placeholder="Ex: 1er Régiment d'Infanterie"
                                          required></textarea>
                                <div class="invalid-feedback">
                                    Veuillez saisir une description de l'unité.
                                </div>
                                <small class="form-text text-muted">
                                    Description complète de l'unité militaire
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Exemples d'unités -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-lightbulb text-warning me-2"></i>
                                        Exemples d'Unités Militaires
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="example-unit" onclick="fillExample('U001', '1er Régiment d\'Infanterie', 'INF')">
                                                <strong>U001</strong> - 1er Régiment d'Infanterie (INF)
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="example-unit" onclick="fillExample('U002', '2ème Escadron de Cavalerie', 'CAV')">
                                                <strong>U002</strong> - 2ème Escadron de Cavalerie (CAV)
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="example-unit" onclick="fillExample('U003', '3ème Groupe d\'Artillerie', 'ART')">
                                                <strong>U003</strong> - 3ème Groupe d'Artillerie (ART)
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-md-4">
                                            <div class="example-unit" onclick="fillExample('U004', 'Bataillon du Génie', 'GEN')">
                                                <strong>U004</strong> - Bataillon du Génie (GEN)
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="example-unit" onclick="fillExample('U005', 'Compagnie de Transmissions', 'TRANS')">
                                                <strong>U005</strong> - Compagnie de Transmissions (TRANS)
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="example-unit" onclick="fillExample('U006', 'Service de Santé Militaire', 'SSM')">
                                                <strong>U006</strong> - Service de Santé Militaire (SSM)
                                            </div>
                                        </div>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fas fa-mouse-pointer me-1"></i>
                                        Cliquez sur un exemple pour remplir automatiquement le formulaire
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('unites') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Enregistrer l'Unité
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* خطوط عربية محسنة */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* تحسين حقل الوصف */
#description {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.6;
    direction: auto;
    text-align: start;
    font-size: 1rem;
    resize: vertical;
    min-height: 120px;
}

/* تحسين النماذج العربية */
.form-control.arabic-input {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين الأمثلة */
.example-unit {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.example-unit:hover {
    background-color: #f8fafc;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.example-unit strong {
    color: #3b82f6;
    font-weight: 600;
}

/* تحسين التسميات */
.form-label {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين النصوص المساعدة */
.form-text {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين العناوين */
.card-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

/* تحسين التنبيهات */
.alert {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين الأزرار */
.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين النص في البطاقات */
.card-body {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    .example-unit {
        font-size: 0.85rem;
        padding: 0.6rem;
    }

    #description {
        font-size: 0.95rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Auto-generate numero suggestion
    $('#numero').on('blur', function() {
        const value = $(this).val().toUpperCase();
        if (value && !value.startsWith('U')) {
            $(this).val('U' + value.padStart(3, '0'));
        }
    });

    // Raccourci validation and formatting
    $('#raccourci').on('input', function() {
        let value = $(this).val().toUpperCase();
        // Remove special characters and numbers
        value = value.replace(/[^A-Z]/g, '');
        // Limit to 10 characters
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        $(this).val(value);
    });

    // Real-time validation feedback
    $('input[required], textarea[required]').on('input', function() {
        if ($(this).val().trim() !== '') {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });

    // Character counter for description
    $('#description').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;

        if (!$('#charCounter').length) {
            $(this).after(`<small id="charCounter" class="form-text text-muted"></small>`);
        }

        $('#charCounter').text(`${currentLength} caractères`);

        if (remaining < 50) {
            $('#charCounter').removeClass('text-muted').addClass('text-warning');
        } else {
            $('#charCounter').removeClass('text-warning').addClass('text-muted');
        }
    });
});

function fillExample(numero, description, raccourci) {
    $('#numero').val(numero);
    $('#description').val(description);
    $('#raccourci').val(raccourci);

    // Trigger validation
    $('#numero').trigger('input');
    $('#description').trigger('input');
    $('#raccourci').trigger('input');

    // Show success feedback
    showToast('Exemple rempli avec succès', 'success');
}

function showToast(message, type = 'info') {
    // Simple toast implementation
    const toast = $(`
        <div class="toast-notification ${type}">
            <i class="fas fa-check-circle me-2"></i>
            ${message}
        </div>
    `);

    $('body').append(toast);

    setTimeout(() => {
        toast.fadeOut(() => toast.remove());
    }, 3000);
}
</script>

<style>
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    animation: slideInRight 0.3s ease;
}

.toast-notification.success {
    background: #10b981;
}

.toast-notification.error {
    background: #ef4444;
}

.toast-notification.warning {
    background: #f59e0b;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
{% endblock %}
