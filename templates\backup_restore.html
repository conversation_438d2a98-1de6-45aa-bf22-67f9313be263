{% extends "base.html" %}

{% block title %}Sauvegarde et Restauration - BANGHALAU{% endblock %}

{% block extra_css %}
<style>
/* خطوط عربية محسنة */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

.backup-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.backup-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.restore-card {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
    border-radius: 15px;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(17, 153, 142, 0.3);
}

.restore-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(17, 153, 142, 0.4);
}

.backup-history-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    border-radius: 15px;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(240, 147, 251, 0.3);
}

.backup-history-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(240, 147, 251, 0.4);
}

.backup-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.progress-container {
    display: none;
    margin-top: 1rem;
}

.backup-item {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.backup-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.status-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
}

.animated-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid animate-fade-in-up">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gradient">
                        <i class="fas fa-database me-2 animated-icon"></i>Sauvegarde et Restauration
                    </h1>
                    <p class="text-muted mb-0">Gestion des sauvegardes de la base de données BANGHALAU</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshPage()">
                        <i class="fas fa-sync-alt me-1"></i>Actualiser
                    </button>
                    <button class="btn btn-outline-secondary" onclick="showHelp()">
                        <i class="fas fa-question-circle me-1"></i>Aide
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Cards -->
    <div class="row mb-5">
        <!-- Backup Card -->
        <div class="col-lg-4 mb-4">
            <div class="card backup-card h-100">
                <div class="card-body text-center p-4">
                    <i class="fas fa-download backup-icon"></i>
                    <h4 class="card-title mb-3">Créer une Sauvegarde</h4>
                    <p class="card-text mb-4">Créez une sauvegarde complète de la base de données avec toutes les données actuelles.</p>

                    <div class="mb-3">
                        <label for="backupName" class="form-label">Nom de la sauvegarde:</label>
                        <input type="text" class="form-control" id="backupName" placeholder="Sauvegarde_AAAA-MM-JJ">
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeFiles" checked>
                            <label class="form-check-label" for="includeFiles">
                                Inclure les fichiers uploadés
                            </label>
                        </div>
                    </div>

                    <button class="btn btn-light btn-lg" onclick="createBackup()">
                        <i class="fas fa-download me-2"></i>Créer Sauvegarde
                    </button>

                    <div class="progress-container" id="backupProgress">
                        <div class="progress mt-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-light mt-2 d-block">Création en cours...</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Restore Card -->
        <div class="col-lg-4 mb-4">
            <div class="card restore-card h-100">
                <div class="card-body text-center p-4">
                    <i class="fas fa-upload backup-icon"></i>
                    <h4 class="card-title mb-3">Restaurer la Base</h4>
                    <p class="card-text mb-4">Restaurez la base de données à partir d'un fichier de sauvegarde existant.</p>

                    <div class="mb-3">
                        <label for="restoreFile" class="form-label">Fichier de sauvegarde:</label>
                        <input type="file" class="form-control" id="restoreFile" accept=".sql,.db,.backup">
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmRestore">
                            <label class="form-check-label" for="confirmRestore">
                                Je confirme vouloir restaurer
                            </label>
                        </div>
                    </div>

                    <button class="btn btn-light btn-lg" onclick="restoreDatabase()" disabled id="restoreBtn">
                        <i class="fas fa-upload me-2"></i>Restaurer Base
                    </button>

                    <div class="progress-container" id="restoreProgress">
                        <div class="progress mt-3">
                            <div class="progress-bar bg-success progress-bar-striped progress-bar-animated"
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-light mt-2 d-block">Restauration en cours...</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup History Card -->
        <div class="col-lg-4 mb-4">
            <div class="card backup-history-card h-100">
                <div class="card-body text-center p-4">
                    <i class="fas fa-history backup-icon"></i>
                    <h4 class="card-title mb-3">Historique</h4>
                    <p class="card-text mb-4">Consultez l'historique des sauvegardes et restaurations effectuées.</p>

                    <div class="mb-3">
                        <div class="row text-start">
                            <div class="col-6">
                                <small class="text-light">Dernière sauvegarde:</small>
                                <div class="fw-bold" id="lastBackupDate">Aucune</div>
                            </div>
                            <div class="col-6">
                                <small class="text-light">Taille totale:</small>
                                <div class="fw-bold" id="totalBackupSize">0 MB</div>
                            </div>
                        </div>
                    </div>

                    <button class="btn btn-light btn-lg" onclick="showBackupHistory()">
                        <i class="fas fa-list me-2"></i>Voir Historique
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup History Table -->
    <div class="row" id="historySection" style="display: none;">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Historique des Sauvegardes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-modern">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-file me-1"></i>Nom</th>
                                    <th><i class="fas fa-calendar me-1"></i>Date</th>
                                    <th><i class="fas fa-weight me-1"></i>Taille</th>
                                    <th><i class="fas fa-info-circle me-1"></i>Type</th>
                                    <th><i class="fas fa-check-circle me-1"></i>Statut</th>
                                    <th><i class="fas fa-cogs me-1"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="backupHistoryTable">
                                <!-- Data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Info -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations Système
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3">
                                <i class="fas fa-database fa-2x text-primary mb-2"></i>
                                <h6>Base de Données</h6>
                                <span class="badge bg-success">SQLite</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3">
                                <i class="fas fa-hdd fa-2x text-info mb-2"></i>
                                <h6>Taille DB</h6>
                                <span class="fw-bold" id="dbSize">Calcul...</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3">
                                <i class="fas fa-table fa-2x text-warning mb-2"></i>
                                <h6>Tables</h6>
                                <span class="fw-bold" id="tableCount">Calcul...</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3">
                                <i class="fas fa-clock fa-2x text-secondary mb-2"></i>
                                <h6>Dernière Modif.</h6>
                                <span class="fw-bold" id="lastModified">Calcul...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadSystemInfo();
    loadBackupHistory();

    // Auto-generate backup name
    const now = new Date();
    const defaultName = `Sauvegarde_${now.getFullYear()}-${String(now.getMonth()+1).padStart(2,'0')}-${String(now.getDate()).padStart(2,'0')}_${String(now.getHours()).padStart(2,'0')}h${String(now.getMinutes()).padStart(2,'0')}`;
    $('#backupName').val(defaultName);

    // Enable restore button when file is selected and confirmed
    $('#restoreFile, #confirmRestore').on('change', function() {
        const fileSelected = $('#restoreFile')[0].files.length > 0;
        const confirmed = $('#confirmRestore').is(':checked');
        $('#restoreBtn').prop('disabled', !(fileSelected && confirmed));
    });
});

function createBackup() {
    const backupName = $('#backupName').val().trim();
    const includeFiles = $('#includeFiles').is(':checked');

    if (!backupName) {
        showToast('Veuillez saisir un nom pour la sauvegarde', 'error');
        return;
    }

    // Show progress
    $('#backupProgress').show();
    const progressBar = $('#backupProgress .progress-bar');

    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 90) progress = 90;
        progressBar.css('width', progress + '%');
    }, 500);

    // Make backup request
    $.ajax({
        url: '/api/backup/create',
        method: 'POST',
        data: {
            name: backupName,
            include_files: includeFiles
        },
        success: function(response) {
            clearInterval(interval);
            progressBar.css('width', '100%');

            setTimeout(() => {
                $('#backupProgress').hide();
                progressBar.css('width', '0%');
                showToast('Sauvegarde créée avec succès!', 'success');
                loadBackupHistory();
                loadSystemInfo();
            }, 1000);
        },
        error: function(xhr) {
            clearInterval(interval);
            $('#backupProgress').hide();
            progressBar.css('width', '0%');
            showToast('Erreur lors de la création de la sauvegarde', 'error');
        }
    });
}

function restoreDatabase() {
    const fileInput = $('#restoreFile')[0];
    const file = fileInput.files[0];

    if (!file) {
        showToast('Veuillez sélectionner un fichier de sauvegarde', 'error');
        return;
    }

    if (!$('#confirmRestore').is(':checked')) {
        showToast('Veuillez confirmer la restauration', 'error');
        return;
    }

    // Confirmation dialog
    if (!confirm('⚠️ ATTENTION ⚠️\n\nLa restauration va remplacer toutes les données actuelles.\nCette action est irréversible.\n\nÊtes-vous sûr de vouloir continuer?')) {
        return;
    }

    // Show progress
    $('#restoreProgress').show();
    const progressBar = $('#restoreProgress .progress-bar');

    // Create FormData
    const formData = new FormData();
    formData.append('backup_file', file);

    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 85) progress = 85;
        progressBar.css('width', progress + '%');
    }, 800);

    // Make restore request
    $.ajax({
        url: '/api/backup/restore',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            clearInterval(interval);
            progressBar.css('width', '100%');

            setTimeout(() => {
                $('#restoreProgress').hide();
                progressBar.css('width', '0%');
                showToast('Base de données restaurée avec succès!', 'success');

                // Reset form
                $('#restoreFile').val('');
                $('#confirmRestore').prop('checked', false);
                $('#restoreBtn').prop('disabled', true);

                loadSystemInfo();

                // Suggest page reload
                setTimeout(() => {
                    if (confirm('La restauration est terminée.\nVoulez-vous recharger la page pour voir les nouvelles données?')) {
                        location.reload();
                    }
                }, 2000);
            }, 1000);
        },
        error: function(xhr) {
            clearInterval(interval);
            $('#restoreProgress').hide();
            progressBar.css('width', '0%');

            let errorMsg = 'Erreur lors de la restauration';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            }
            showToast(errorMsg, 'error');
        }
    });
}

function showBackupHistory() {
    $('#historySection').toggle();
    if ($('#historySection').is(':visible')) {
        loadBackupHistory();
    }
}

function loadBackupHistory() {
    $.ajax({
        url: '/api/backup/history',
        method: 'GET',
        success: function(response) {
            const tbody = $('#backupHistoryTable');
            tbody.empty();

            if (response.backups && response.backups.length > 0) {
                response.backups.forEach(backup => {
                    const statusBadge = backup.status === 'success' ?
                        '<span class="badge bg-success">Réussie</span>' :
                        '<span class="badge bg-danger">Échouée</span>';

                    const row = `
                        <tr>
                            <td>
                                <i class="fas fa-file-archive me-2 text-primary"></i>
                                ${backup.name}
                            </td>
                            <td>${new Date(backup.date).toLocaleString('fr-FR')}</td>
                            <td>${formatFileSize(backup.size)}</td>
                            <td>
                                <span class="badge bg-info">${backup.type}</span>
                            </td>
                            <td>${statusBadge}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="downloadBackup('${backup.id}')" title="Télécharger">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="restoreFromHistory('${backup.id}')" title="Restaurer">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteBackup('${backup.id}')" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });

                // Update summary
                if (response.backups.length > 0) {
                    const lastBackup = response.backups[0];
                    $('#lastBackupDate').text(new Date(lastBackup.date).toLocaleDateString('fr-FR'));

                    const totalSize = response.backups.reduce((sum, backup) => sum + backup.size, 0);
                    $('#totalBackupSize').text(formatFileSize(totalSize));
                }
            } else {
                tbody.append(`
                    <tr>
                        <td colspan="6" class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <div>Aucune sauvegarde trouvée</div>
                        </td>
                    </tr>
                `);
            }
        },
        error: function() {
            showToast('Erreur lors du chargement de l\'historique', 'error');
        }
    });
}

function loadSystemInfo() {
    $.ajax({
        url: '/api/backup/system-info',
        method: 'GET',
        success: function(response) {
            $('#dbSize').text(formatFileSize(response.db_size));
            $('#tableCount').text(response.table_count);
            $('#lastModified').text(new Date(response.last_modified).toLocaleDateString('fr-FR'));
        },
        error: function() {
            $('#dbSize').text('Erreur');
            $('#tableCount').text('Erreur');
            $('#lastModified').text('Erreur');
        }
    });
}

function downloadBackup(backupId) {
    window.open(`/api/backup/download/${backupId}`, '_blank');
}

function restoreFromHistory(backupId) {
    if (!confirm('Voulez-vous restaurer cette sauvegarde?\nCela remplacera toutes les données actuelles.')) {
        return;
    }

    $.ajax({
        url: `/api/backup/restore/${backupId}`,
        method: 'POST',
        success: function(response) {
            showToast('Restauration effectuée avec succès!', 'success');
            loadSystemInfo();
        },
        error: function() {
            showToast('Erreur lors de la restauration', 'error');
        }
    });
}

function deleteBackup(backupId) {
    if (!confirm('Voulez-vous supprimer cette sauvegarde?\nCette action est irréversible.')) {
        return;
    }

    $.ajax({
        url: `/api/backup/delete/${backupId}`,
        method: 'DELETE',
        success: function(response) {
            showToast('Sauvegarde supprimée avec succès!', 'success');
            loadBackupHistory();
        },
        error: function() {
            showToast('Erreur lors de la suppression', 'error');
        }
    });
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function refreshPage() {
    location.reload();
}

function showHelp() {
    const helpText = `
🔧 AIDE - Sauvegarde et Restauration

📥 CRÉER UNE SAUVEGARDE:
• Saisissez un nom descriptif
• Choisissez d'inclure les fichiers uploadés
• Cliquez sur "Créer Sauvegarde"

📤 RESTAURER:
• Sélectionnez un fichier .sql, .db ou .backup
• Cochez la case de confirmation
• Cliquez sur "Restaurer Base"

📋 HISTORIQUE:
• Consultez toutes les sauvegardes
• Téléchargez ou restaurez depuis l'historique
• Supprimez les anciennes sauvegardes

⚠️ ATTENTION:
• La restauration remplace TOUTES les données
• Créez toujours une sauvegarde avant restauration
• Les fichiers .sql doivent être compatibles SQLite
    `;

    alert(helpText);
}
</script>
{% endblock %}
