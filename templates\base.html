<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}BANGHALAU{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">



    <!-- Arabic Fonts CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/arabic-fonts.css') }}">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    {% block extra_css %}{% endblock %}
</head>
<body class="modern-layout">
    <!-- Top Navigation Bar -->
    <nav class="top-navbar">
        <div class="navbar-container">
            <div class="d-flex align-items-center gap-3">
                <button class="btn btn-outline-light btn-sm" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="navbar-brand">
                </div>
            </div>

            <div class="d-none d-lg-flex align-items-center">
                <div class="input-group" style="width: 350px;">
                    <span class="input-group-text bg-white border-end-0">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input type="text" class="form-control border-start-0" placeholder="Rechercher dans le système..." id="globalSearch">
                </div>
            </div>

            <div class="navbar-user">
                <div class="d-flex align-items-center gap-3">
                    <!-- Notifications -->
                    <div class="dropdown">
                        <button class="btn btn-outline-light btn-sm position-relative" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">
                                3
                            </span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">Notifications</h6></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-home me-2"></i>Nouveau bungalow disponible</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Nouvelle demande de distribution</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-clock me-2"></i>Distribution expirée</a></li>
                        </ul>
                    </div>

                    <!-- User Profile -->
                    <div class="d-flex align-items-center gap-2">
                        <div class="user-avatar">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <div class="d-none d-md-block text-white">
                            <div class="fw-bold">Administrateur</div>
                            <small style="color: rgba(255,255,255,0.8);">En ligne</small>
                        </div>
                    </div>

                    <!-- Logout -->
                    <div class="dropdown">
                        <button class="btn btn-outline-light btn-sm" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Paramètres</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-building"></i>
                <div>
                    <div class="fw-bold">BANGHALAU</div>

                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <!-- Dashboard -->
            <div class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}">
                    <i class="fas fa-chart-pie"></i>
                    <span>Tableau de Bord</span>
                    <span class="nav-badge" id="dashboardBadge">•</span>
                </a>
            </div>

            <!-- Bungalows Section -->
            <div class="nav-section">
                <div class="nav-section-title">Gestion des Bungalows</div>

                <div class="nav-item">
                    <a href="{{ url_for('bungalows') }}" class="nav-link {% if request.endpoint in ['bungalows', 'add_bungalow', 'edit_bungalow'] %}active{% endif %}">
                        <i class="fas fa-home"></i>
                        <span>Bungalows</span>
                        <span class="nav-badge" id="bungalowsBadge">0</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="{{ url_for('distributions') }}" class="nav-link {% if request.endpoint in ['distributions', 'new_distribution', 'edit_distribution'] %}active{% endif %}">
                        <i class="fas fa-exchange-alt"></i>
                        <span>Distributions</span>
                        <span class="nav-badge" id="distributionsBadge">0</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="{{ url_for('nouvelle_distribution') }}" class="nav-link {% if request.endpoint == 'nouvelle_distribution' %}active{% endif %}">
                        <i class="fas fa-plus-circle"></i>
                        <span>Nouvelle Distribution</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="{{ url_for('liste_distributions') }}" class="nav-link {% if request.endpoint == 'liste_distributions' %}active{% endif %}">
                        <i class="fas fa-filter"></i>
                        <span>Filtre Distribution</span>
                    </a>
                </div>
            </div>

            <!-- Personnel Section -->
            <div class="nav-section">
                <div class="nav-section-title">Gestion du Personnel</div>

                <div class="nav-item">
                    <a href="{{ url_for('personnel') }}" class="nav-link {% if request.endpoint in ['personnel', 'add_personnel', 'edit_personnel'] %}active{% endif %}">
                        <i class="fas fa-users"></i>
                        <span>Personnel Militaire</span>
                        <span class="nav-badge" id="personnelBadge">0</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="{{ url_for('unites') }}" class="nav-link {% if request.endpoint in ['unites', 'add_unite', 'edit_unite', 'import_unites'] %}active{% endif %}">
                        <i class="fas fa-flag"></i>
                        <span>Unités Militaires</span>
                        <span class="nav-badge" id="unitesBadge">0</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="{{ url_for('grades') }}" class="nav-link {% if request.endpoint in ['grades', 'add_grade', 'edit_grade'] %}active{% endif %}">
                        <i class="fas fa-star"></i>
                        <span>Grades Militaires</span>
                        <span class="nav-badge" id="gradesBadge">0</span>
                    </a>
                </div>
            </div>

            <!-- Sessions Section -->
            <div class="nav-section">
                <div class="nav-section-title">Gestion des Sessions</div>

                <div class="nav-item">
                    <a href="{{ url_for('sessions') }}" class="nav-link {% if request.endpoint in ['sessions', 'add_session', 'edit_session'] %}active{% endif %}">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Sessions</span>
                        <span class="nav-badge" id="sessionsBadge">0</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="{{ url_for('bungalow_session_links') }}" class="nav-link {% if request.endpoint in ['bungalow_session_links', 'create_bungalow_session_link', 'edit_bungalow_session_link'] %}active{% endif %}">
                        <i class="fas fa-link"></i>
                        <span>Liaisons B-S</span>
                        <span class="nav-badge" id="linksBadge">0</span>
                    </a>
                </div>
            </div>

            <!-- Reports & Settings -->
            <div class="nav-section">
                <div class="nav-section-title">Système</div>

                <div class="nav-item">
                    <a href="{{ url_for('backup_restore') }}" class="nav-link {% if request.endpoint == 'backup_restore' %}active{% endif %}">
                        <i class="fas fa-database"></i>
                        <span>Sauvegarde & Restauration</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showReports()">
                        <i class="fas fa-chart-bar"></i>
                        <span>Rapports</span>
                    </a>
                </div>



                <div class="nav-item">
                    <a href="{{ url_for('users_simple') }}" class="nav-link {% if request.endpoint == 'users_simple' %}active{% endif %}">
                        <i class="fas fa-users-cog"></i>
                        <span>Gestion Utilisateurs</span>
                    </a>
                </div>



                <div class="nav-item">
                    <a href="{{ url_for('control_panel') }}" class="nav-link {% if request.endpoint == 'control_panel' %}active{% endif %}">
                        <i class="fas fa-cogs"></i>
                        <span>Panneau de Contrôle</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="{{ url_for('security_dashboard') }}" class="nav-link {% if request.endpoint == 'security_dashboard' %}active{% endif %}">
                        <i class="fas fa-shield-alt"></i>
                        <span>Gestion de la Sécurité</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showSettings()">
                        <i class="fas fa-cog"></i>
                        <span>Paramètres</span>
                    </a>
                </div>
            </div>
        </nav>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <div class="container-fluid">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
        <div id="liveToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">BANGHALAU</strong>
                <small class="text-muted">maintenant</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                <!-- Toast message will be inserted here -->
            </div>
        </div>
    </div>



    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    {% block extra_js %}{% endblock %}

    <script>
        // Initialize sidebar and navigation (Fixed - Unified)
        $(document).ready(function() {
            console.log('Initializing BANGHALAU interface...');

            // Initialize sidebar first
            initializeSidebar();

            // Initialize modern interface (includes sidebar toggle)
            initializeModernInterface();

            // Force sidebar toggle button event
            $('#sidebarToggle').off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Sidebar toggle clicked!');
                toggleSidebar();
                return false;
            });

            // Update navigation badges
            updateNavigationBadges();

            // Global search functionality
            $('#globalSearch').on('keyup', function(e) {
                if (e.key === 'Enter') {
                    performGlobalSearch($(this).val());
                }
            });

            // Auto-refresh badges every 30 seconds
            setInterval(updateNavigationBadges, 30000);

            console.log('BANGHALAU interface initialized successfully!');
        });

        // Update navigation badges with current counts
        function updateNavigationBadges() {
            // Update bungalows count
            $.get('/api/bungalows/count')
                .done(function(data) {
                    $('#bungalowsBadge').text(data.count);
                })
                .fail(function() {
                    $('#bungalowsBadge').text('?');
                });

            // Update personnel count
            $.get('/api/personnel/count')
                .done(function(data) {
                    $('#personnelBadge').text(data.count);
                })
                .fail(function() {
                    $('#personnelBadge').text('?');
                });

            // Update distributions count
            $.get('/api/distributions/count')
                .done(function(data) {
                    $('#distributionsBadge').text(data.count);
                })
                .fail(function() {
                    $('#distributionsBadge').text('?');
                });

            // Update unites count
            $.get('/api/unites/count')
                .done(function(data) {
                    $('#unitesBadge').text(data.count);
                })
                .fail(function() {
                    $('#unitesBadge').text('?');
                });

            // Update grades count
            $.get('/api/grades/count')
                .done(function(data) {
                    $('#gradesBadge').text(data.count);
                })
                .fail(function() {
                    $('#gradesBadge').text('?');
                });

            // Update sessions count
            $.get('/api/sessions/count')
                .done(function(data) {
                    $('#sessionsBadge').text(data.count);
                })
                .fail(function() {
                    $('#sessionsBadge').text('?');
                });

            // Update links count
            $.get('/api/links/count')
                .done(function(data) {
                    $('#linksBadge').text(data.count);
                })
                .fail(function() {
                    $('#linksBadge').text('?');
                });
        }

        // Global search function
        function performGlobalSearch(query) {
            if (query.trim() === '') return;

            showToast('Recherche en cours...', 'info');
            // Implement global search logic here
        }

        // Show toast notification
        function showToast(message, type = 'info') {
            const toast = $('#liveToast');
            const toastBody = toast.find('.toast-body');
            const toastIcon = toast.find('.toast-header i');

            // Update icon based on type
            toastIcon.removeClass().addClass('fas me-2');
            switch(type) {
                case 'success':
                    toastIcon.addClass('fa-check-circle text-success');
                    break;
                case 'error':
                    toastIcon.addClass('fa-exclamation-triangle text-danger');
                    break;
                case 'warning':
                    toastIcon.addClass('fa-exclamation-circle text-warning');
                    break;
                default:
                    toastIcon.addClass('fa-info-circle text-primary');
            }

            toastBody.text(message);

            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();
        }



        // Show reports (placeholder)
        function showReports() {
            showToast('Fonctionnalité des rapports en développement', 'info');
        }

        // Show settings (placeholder)
        function showSettings() {
            showToast('Fonctionnalité des paramètres en développement', 'info');
        }


    </script>

    <style>
        /* Additional styles for improved layout */

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        @media (min-width: 992px) {
            .sidebar {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 280px;
            }

            .sidebar-overlay {
                display: none;
            }
        }

        /* Improved form styles */
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        /* Better button hover effects */
        .btn {
            transition: all 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        /* Enhanced table styles */
        .table-modern tbody tr:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }

        /* Improved badge animations */
        .nav-badge {
            transition: all 0.3s ease;
        }

        .nav-link:hover .nav-badge {
            transform: scale(1.1);
        }
    </style>
</body>
</html>
