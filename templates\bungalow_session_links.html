{% extends "base.html" %}

{% block title %}Liaisons Bungalows-Sessions - BANGHALAU{% endblock %}

{% block extra_css %}
<style>
/* خطوط عربية محسنة لصفحة الروابط */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* تحسين عرض النصوص العربية */
.link-details {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

.link-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.bungalow-info {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.session-info {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.card-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.table-modern {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

.table-modern th {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.table-modern td {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    vertical-align: middle;
}

.badge {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

.form-control, .form-select {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    .bungalow-info, .session-info {
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gradient link-title">
                        <i class="fas fa-link me-2"></i>Liaisons Bungalows-Sessions
                    </h1>
                    <p class="text-muted mb-0">Gérez les liens directs entre bungalows et sessions</p>
                </div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLinkModal">
                    <i class="fas fa-plus me-1"></i>Nouvelle Liaison
                </button>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title text-white-50 mb-1">Total Liaisons</h6>
                                    <h3 class="mb-0">{{ links|length }}</h3>
                                    <small class="text-white-75">Liens directs actifs</small>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-link fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title text-white-50 mb-1">Liaisons Actives</h6>
                                    <h3 class="mb-0">{{ links|selectattr('statut', 'equalto', 'active')|list|length }}</h3>
                                    <small class="text-white-75">En cours d'utilisation</small>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title text-white-50 mb-1">Bungalows Liés</h6>
                                    <h3 class="mb-0">{{ links|map(attribute='bungalow_id')|unique|list|length }}</h3>
                                    <small class="text-white-75">Bungalows avec sessions</small>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-home fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title text-white-50 mb-1">Sessions Liées</h6>
                                    <h3 class="mb-0">{{ links|map(attribute='session_id')|unique|list|length }}</h3>
                                    <small class="text-white-75">Sessions avec bungalows</small>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-calendar fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Links Table -->
            <div class="card-modern">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>Liste des Liaisons
                        </h5>
                        <div class="d-flex gap-2">
                            <select class="form-control" id="statusFilter" style="width: 150px;">
                                <option value="">Tous les statuts</option>
                                <option value="active">Actives</option>
                                <option value="inactive">Inactives</option>
                            </select>
                            <input type="text" class="form-control" id="searchInput" placeholder="Rechercher..." style="width: 250px;">
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-modern" id="linksTable">
                        <thead>
                            <tr>
                                <th><i class="fas fa-home me-1"></i>Bungalow</th>
                                <th><i class="fas fa-calendar me-1"></i>Session</th>
                                <th><i class="fas fa-calendar-alt me-1"></i>Date Assignation</th>
                                <th><i class="fas fa-info-circle me-1"></i>Statut</th>
                                <th><i class="fas fa-sticky-note me-1"></i>Notes</th>
                                <th><i class="fas fa-cogs me-1"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for link in links %}
                            <tr data-status="{{ link.statut }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-home text-primary me-2"></i>
                                        <div>
                                            <strong>{{ link.bungalow_numero }}</strong>
                                            <small class="text-muted d-block bungalow-info">{{ link.bungalow_endroit }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-calendar text-info me-2"></i>
                                        <div>
                                            <strong>{{ link.session_numero }}</strong>
                                            {% if link.session_description %}
                                                <small class="text-muted d-block session-info">{{ link.session_description }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <i class="fas fa-calendar-alt text-success me-1"></i>
                                    {{ link.date_assignation }}
                                </td>
                                <td>
                                    {% if link.statut == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if link.notes %}
                                        <small class="text-muted">{{ link.notes[:50] }}{% if link.notes|length > 50 %}...{% endif %}</small>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-warning"
                                                onclick="editLink({{ link.id }}, '{{ link.statut }}', '{{ link.notes or '' }}')"
                                                title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger"
                                                onclick="confirmDeleteLink({{ link.id }}, '{{ link.bungalow_numero }}', '{{ link.session_numero }}')"
                                                title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-link fa-3x mb-3"></i>
                                        <h5>Aucune liaison trouvée</h5>
                                        <p>Commencez par créer votre première liaison bungalow-session.</p>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLinkModal">
                                            <i class="fas fa-plus me-1"></i>Nouvelle Liaison
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Link Modal -->
<div class="modal fade" id="addLinkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus text-primary me-2"></i>
                    Nouvelle Liaison Bungalow-Session
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('create_bungalow_session_link') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="bungalow_id" class="form-label">Bungalow</label>
                        <select class="form-control" id="bungalow_id" name="bungalow_id" required>
                            <option value="">Sélectionnez un bungalow</option>
                            {% for bungalow in bungalows %}
                                <option value="{{ bungalow.id }}">{{ bungalow.numero }} - {{ bungalow.endroit }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="session_id" class="form-label">Session</label>
                        <select class="form-control" id="session_id" name="session_id" required>
                            <option value="">Sélectionnez une session</option>
                            {% for session in sessions %}
                                <option value="{{ session.id }}">{{ session.numero }} - {{ session.description or 'Sans description' }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="statut" class="form-label">Statut</label>
                        <select class="form-control" id="statut" name="statut">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Notes optionnelles..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Annuler
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Créer la Liaison
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Link Modal -->
<div class="modal fade" id="editLinkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit text-warning me-2"></i>
                    Modifier la Liaison
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editLinkForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_statut" class="form-label">Statut</label>
                        <select class="form-control" id="edit_statut" name="statut">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Annuler
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i>Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteLinkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Confirmer la Suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la liaison entre <strong id="linkInfo"></strong> ?</p>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    Cette action est irréversible.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteLinkBtn">
                    <i class="fas fa-trash me-1"></i>Supprimer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden delete form -->
<form id="deleteLinkForm" method="POST" style="display: none;"></form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterTable();
    });

    // Status filter
    $('#statusFilter').on('change', function() {
        filterTable();
    });

    function filterTable() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const statusFilter = $('#statusFilter').val();

        $('#linksTable tbody tr').each(function() {
            const row = $(this);
            const text = row.text().toLowerCase();
            const status = row.data('status');

            let showRow = true;

            // Search filter
            if (searchTerm && !text.includes(searchTerm)) {
                showRow = false;
            }

            // Status filter
            if (statusFilter && status !== statusFilter) {
                showRow = false;
            }

            row.toggle(showRow);
        });
    }

    // Form validation
    $('#addLinkModal form').on('submit', function(e) {
        const bungalowId = $('#bungalow_id').val();
        const sessionId = $('#session_id').val();

        if (!bungalowId || !sessionId) {
            e.preventDefault();
            alert('Veuillez sélectionner un bungalow et une session.');
            return false;
        }
    });
});

function editLink(linkId, currentStatus, currentNotes) {
    $('#edit_statut').val(currentStatus);
    $('#edit_notes').val(currentNotes);
    $('#editLinkForm').attr('action', `/bungalow-session-links/edit/${linkId}`);
    $('#editLinkModal').modal('show');
}

function confirmDeleteLink(linkId, bungalowNumero, sessionNumero) {
    $('#linkInfo').text(`${bungalowNumero} et ${sessionNumero}`);
    $('#confirmDeleteLinkBtn').off('click').on('click', function() {
        deleteLink(linkId);
    });
    $('#deleteLinkModal').modal('show');
}

function deleteLink(linkId) {
    $('#deleteLinkForm').attr('action', `/bungalow-session-links/delete/${linkId}`);
    $('#deleteLinkForm').submit();
}
</script>
{% endblock %}
