{% extends "base.html" %}

{% block title %}Bungalows - BANGHALAU{% endblock %}

{% block extra_css %}
<!-- استيراد الخطوط العربية المحسنة -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
/* خطوط عربية محسنة لصفحة Bungalows */
:root {
    --arabic-font-primary: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --arabic-font-secondary: '<PERSON><PERSON><PERSON>', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --mixed-font: 'Cairo', 'Noto Sans Arabic', 'Inter', 'Segoe UI', sans-serif;
}

/* تحسين عام للنصوص */
body, .card, .btn, .form-control, .modal-content {
    font-family: var(--mixed-font);
    font-feature-settings: 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* العناوين */
.page-title, .card-title, .modal-title, h1, h2, h3, h4, h5, h6 {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    line-height: 1.3;
}

/* تحسين عرض النص في خانة الموقع */
.location-text {
    font-family: var(--mixed-font);
    font-weight: 500;
    line-height: 1.4;
    direction: auto;
    text-align: start;
    font-size: 0.95rem;
    color: #374151;
}

/* تحسين عرض النص في خانة الخصائص */
.characteristics-text {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.5;
    direction: auto;
    text-align: start;
    font-size: 0.9rem;
    color: #4b5563;
    max-width: 250px;
    word-wrap: break-word;
    white-space: normal;
}

/* تحسين عرض النص في الجدول */
.table-bungalows {
    font-family: var(--mixed-font);
}

.table-bungalows th {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    font-size: 0.95rem;
}

.table-bungalows td {
    font-family: var(--mixed-font);
    font-weight: 400;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* الشارات والتسميات */
.badge {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* التنبيهات */
.alert {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.5;
}

/* الأزرار */
.btn {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* النماذج */
.form-control, .form-select {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
}

.form-label {
    font-family: var(--mixed-font);
    font-weight: 500;
}

/* النصوص العربية المخصصة */
.arabic-text {
    font-family: var(--arabic-font-primary);
    font-weight: 500;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
}

.mixed-text {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
    line-height: 1.5;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .table-bungalows th, .table-bungalows td {
        font-size: 0.85rem;
    }

    .page-title, .card-title {
        font-size: 1.1rem;
    }

    .characteristics-text {
        max-width: 150px;
        font-size: 0.8rem;
    }
}

/* تحسين للطباعة */
@media print {
    body, .table, .card, .btn, .form-control, .modal-content {
        font-family: 'Cairo', 'Noto Sans Arabic', serif;
        color: #000;
    }
}

/* تحسين عرض معلومات الجلسة */
.session-info {
    font-family: var(--mixed-font);
    line-height: 1.4;
}

.session-description {
    font-family: var(--mixed-font);
    font-weight: 400;
    font-size: 0.8rem;
    color: #6b7280;
    max-width: 200px;
    word-wrap: break-word;
    white-space: normal;
}

.badge-sm {
    font-size: 0.65rem;
    padding: 0.25em 0.5em;
}

/* تحسين عرض حالة الجلسة */
.session-status-info {
    font-family: var(--mixed-font);
    line-height: 1.4;
}

.session-dates {
    font-family: var(--mixed-font);
    font-weight: 400;
    font-size: 0.75rem;
    color: #6b7280;
    max-width: 180px;
    word-wrap: break-word;
    white-space: normal;
}

/* ألوان مخصصة لحالات الجلسة */
.badge.bg-success {
    background-color: #10b981 !important;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.badge.bg-warning {
    background-color: #f59e0b !important;
    color: #1f2937 !important;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.badge.bg-secondary {
    background-color: #6b7280 !important;
    box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
}

.badge.bg-purple {
    background-color: #6f42c1 !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(111, 66, 193, 0.3);
}

.badge.bg-danger {
    background-color: #ef4444 !important;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.badge.bg-info {
    background-color: #3b82f6 !important;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* تأثيرات تفاعلية للشارات */
.badge-bungalow {
    transition: all 0.2s ease;
    cursor: default;
}

.badge-bungalow:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* تحسين عرض التواريخ */
.session-dates i {
    color: #9ca3af;
}

/* تحسين عرض حالة الجلسة */
.session-status-info .badge i {
    margin-right: 0.25rem;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    .location-text {
        font-size: 0.9rem;
    }

    .characteristics-text {
        font-size: 0.85rem;
        max-width: 180px;
    }

    .session-description {
        font-size: 0.75rem;
        max-width: 150px;
    }

    .session-info .badge {
        font-size: 0.7rem;
    }

    .session-status-info .badge {
        font-size: 0.65rem;
    }

    .session-dates {
        font-size: 0.7rem;
        max-width: 120px;
    }

    /* تقليل حجم الجدول للشاشات الصغيرة */
    .table-bungalows th,
    .table-bungalows td {
        padding: 0.5rem 0.25rem;
        font-size: 0.85rem;
    }
}

/* Statistics Cards Styles */
#bungalowStats .card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

#bungalowStats .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

#bungalowStats .card-body {
    padding: 1.5rem;
}

#bungalowStats h4 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0;
}

#bungalowStats p {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0;
}

#bungalowStats .fa-2x {
    opacity: 0.7;
}

/* Animation for updating counters */
.updating {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Responsive adjustments for statistics */
@media (max-width: 768px) {
    #bungalowStats .col-md-3 {
        margin-bottom: 1rem;
    }

    #bungalowStats h4 {
        font-size: 1.5rem;
    }

    #bungalowStats .fa-2x {
        font-size: 1.5em;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gradient page-title">
                        <i class="fas fa-home me-2"></i>Gestion des Bungalows
                        {% if search_query %}
                            <small class="text-muted">- Résultats pour "{{ search_query }}"</small>
                        {% endif %}
                    </h1>
                    <p class="text-muted mb-0">
                        {% if search_query %}
                            {{ bungalows|length }} bungalow(s) trouvé(s) pour "{{ search_query }}"
                        {% else %}
                            Gérez tous les bungalows disponibles
                        {% endif %}
                    </p>
                </div>
                <a href="{{ url_for('add_bungalow') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Nouveau Bungalow
                </a>
            </div>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Statistics Cards -->
    <div class="row mb-4" id="bungalowStats">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="totalCount">{{ total_count or bungalows|length }}</h4>
                            <p class="mb-0">Total</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-home fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="availableCount">
                                {{ available_count or (bungalows|selectattr('statut', 'equalto', 'Disponible')|list|length) }}
                            </h4>
                            <p class="mb-0">Disponibles</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="occupiedCount">
                                {{ occupied_count or (bungalows|selectattr('statut', 'equalto', 'Occupé')|list|length) }}
                            </h4>
                            <p class="mb-0">Occupés</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="maintenanceCount">
                                {{ maintenance_count or (bungalows|selectattr('statut', 'equalto', 'Maintenance')|list|length) }}
                            </h4>
                            <p class="mb-0">Maintenance</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tools fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card-modern">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Liste des Bungalows
                </h5>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control" id="searchInput" placeholder="Rechercher un bungalow..."
                           value="{{ search_query or '' }}" style="width: 250px;">
                    {% if search_query %}
                        <a href="{{ url_for('bungalows') }}" class="btn btn-outline-secondary" title="Effacer la recherche">
                            <i class="fas fa-times"></i>
                        </a>
                    {% endif %}
                    <select class="form-select" id="statusFilter" style="width: 200px;">
                        <option value="">Tous les statuts</option>
                        <option value="Active">Sessions Actives</option>
                        <option value="Planifiée">Sessions Planifiées</option>
                        <option value="Terminée">Sessions Terminées</option>
                        <option value="Annulée">Sessions Annulées</option>
                        <option value="no-session">Sans Session</option>
                    </select>
                    <button class="btn btn-outline-primary" onclick="exportData()">
                        <i class="fas fa-download me-1"></i>Exporter
                    </button>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-modern table-bungalows" id="bungalowsTable">
                <thead>
                    <tr>
                        <th><i class="fas fa-hashtag me-1"></i>Numéro</th>
                        <th><i class="fas fa-map-marker-alt me-1"></i>Emplacement</th>
                        <th><i class="fas fa-users me-1"></i>Capacité</th>
                        <th><i class="fas fa-calendar-alt me-1"></i>Session</th>
                        <th><i class="fas fa-traffic-light me-1"></i>Statut Session</th>
                        <th><i class="fas fa-list-ul me-1"></i>Caractéristiques</th>
                        <th><i class="fas fa-chart-pie me-1"></i>Statut Bungalow</th>
                        <th><i class="fas fa-cogs me-1"></i>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for bungalow in bungalows %}
                    <tr>
                        <td>
                            <strong class="text-primary">{{ bungalow.numero }}</strong>
                        </td>
                        <td>
                            <i class="fas fa-map-marker-alt text-muted me-1"></i>
                            <span class="location-text">{{ bungalow.endroit }}</span>
                        </td>
                        <td>
                            <span class="badge bg-info badge-bungalow">
                                <i class="fas fa-users me-1"></i>{{ bungalow.capacite }} pers.
                            </span>
                        </td>
                        <td>
                            {% if bungalow.session_numero %}
                                <div class="session-info">
                                    <span class="badge bg-primary badge-bungalow">
                                        <i class="fas fa-calendar-alt me-1"></i>{{ bungalow.session_numero }}
                                    </span>
                                    {% if bungalow.session_description %}
                                        <br>
                                        <small class="text-muted session-description">{{ bungalow.session_description }}</small>
                                    {% endif %}
                                </div>
                            {% else %}
                                <span class="text-muted">
                                    <i class="fas fa-minus-circle me-1"></i>Aucune session
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if bungalow.session_etat %}
                                <div class="session-status-info">
                                    {% if bungalow.session_etat == 'Active' %}
                                        <span class="badge bg-success badge-bungalow"
                                              title="Session active - En cours d'exécution"
                                              data-bs-toggle="tooltip">
                                            <i class="fas fa-play-circle me-1"></i>Active
                                        </span>
                                    {% elif bungalow.session_etat == 'Planifiée' %}
                                        <span class="badge bg-warning badge-bungalow"
                                              title="Session planifiée - Pas encore commencée"
                                              data-bs-toggle="tooltip">
                                            <i class="fas fa-clock me-1"></i>Planifiée
                                        </span>
                                    {% elif bungalow.session_etat == 'Terminée' %}
                                        <span class="badge bg-secondary badge-bungalow"
                                              title="Session terminée - Complétée avec succès"
                                              data-bs-toggle="tooltip">
                                            <i class="fas fa-check-circle me-1"></i>Terminée
                                        </span>
                                    {% elif bungalow.session_etat == 'Annulée' %}
                                        <span class="badge bg-danger badge-bungalow"
                                              title="Session annulée - Interrompue ou supprimée"
                                              data-bs-toggle="tooltip">
                                            <i class="fas fa-times-circle me-1"></i>Annulée
                                        </span>
                                    {% else %}
                                        <span class="badge bg-info badge-bungalow"
                                              title="Statut: {{ bungalow.session_etat }}"
                                              data-bs-toggle="tooltip">
                                            <i class="fas fa-info-circle me-1"></i>{{ bungalow.session_etat }}
                                        </span>
                                    {% endif %}
                                    {% if bungalow.session_date_debut and bungalow.session_date_fin %}
                                        <br>
                                        <small class="text-muted session-dates">
                                            <i class="fas fa-calendar me-1"></i>{{ bungalow.session_date_debut }} - {{ bungalow.session_date_fin }}
                                        </small>
                                    {% elif bungalow.session_date_debut %}
                                        <br>
                                        <small class="text-muted session-dates">
                                            <i class="fas fa-calendar me-1"></i>Début: {{ bungalow.session_date_debut }}
                                        </small>
                                    {% endif %}
                                </div>
                            {% else %}
                                <span class="text-muted">
                                    <i class="fas fa-minus me-1"></i>N/A
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if bungalow.caracteristiques %}
                                <span class="characteristics-text" title="{{ bungalow.caracteristiques }}">
                                    {{ bungalow.caracteristiques }}
                                </span>
                            {% else %}
                                <span class="text-muted">Aucune caractéristique</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if bungalow.statut %}
                                {% if bungalow.statut == 'Disponible' %}
                                    <span class="badge bg-success badge-bungalow"
                                          title="Bungalow disponible pour attribution"
                                          data-bs-toggle="tooltip">
                                        <i class="fas fa-check-circle me-1"></i>Disponible
                                    </span>
                                {% elif bungalow.statut == 'Occupé' %}
                                    <span class="badge bg-danger badge-bungalow"
                                          title="Bungalow actuellement occupé"
                                          data-bs-toggle="tooltip">
                                        <i class="fas fa-user-check me-1"></i>Occupé
                                    </span>
                                {% elif bungalow.statut == 'Maintenance' %}
                                    <span class="badge bg-warning badge-bungalow"
                                          title="Bungalow en maintenance"
                                          data-bs-toggle="tooltip">
                                        <i class="fas fa-tools me-1"></i>Maintenance
                                    </span>
                                {% elif bungalow.statut == 'Réservé' %}
                                    <span class="badge bg-info badge-bungalow"
                                          title="Bungalow réservé"
                                          data-bs-toggle="tooltip">
                                        <i class="fas fa-calendar-check me-1"></i>Réservé
                                    </span>
                                {% elif bungalow.statut == 'Hors Service' %}
                                    <span class="badge bg-secondary badge-bungalow"
                                          title="Bungalow hors service"
                                          data-bs-toggle="tooltip">
                                        <i class="fas fa-times-circle me-1"></i>Hors Service
                                    </span>
                                {% elif bungalow.statut == 'En Rénovation' %}
                                    <span class="badge bg-purple badge-bungalow"
                                          title="Bungalow en rénovation"
                                          data-bs-toggle="tooltip">
                                        <i class="fas fa-hammer me-1"></i>En Rénovation
                                    </span>
                                {% else %}
                                    <span class="badge bg-light text-dark badge-bungalow"
                                          title="Statut: {{ bungalow.statut }}"
                                          data-bs-toggle="tooltip">
                                        <i class="fas fa-question-circle me-1"></i>{{ bungalow.statut }}
                                    </span>
                                {% endif %}
                                {% if bungalow.notes_statut %}
                                    <br>
                                    <small class="text-muted" title="{{ bungalow.notes_statut }}">
                                        <i class="fas fa-sticky-note me-1"></i>{{ bungalow.notes_statut[:30] }}{% if bungalow.notes_statut|length > 30 %}...{% endif %}
                                    </small>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-light text-dark badge-bungalow">
                                    <i class="fas fa-question-circle me-1"></i>Non défini
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('edit_bungalow', bungalow_id=bungalow.id) }}"
                                   class="btn btn-sm btn-outline-warning"
                                   title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-info"
                                        onclick="viewDetails({{ bungalow.id }})"
                                        title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger"
                                        onclick="confirmDelete({{ bungalow.id }}, '{{ bungalow.numero }}')"
                                        title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <h5>Aucun bungalow trouvé</h5>
                                <p>Commencez par ajouter votre premier bungalow.</p>
                                <a href="{{ url_for('add_bungalow') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>Ajouter un Bungalow
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Confirmer la Suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le bungalow <strong id="bungalowName"></strong> ?</p>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    Cette action est irréversible.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>Supprimer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden delete form -->
<form id="deleteForm" method="POST" style="display: none;"></form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Search functionality
    function filterTable() {
        const searchValue = $('#searchInput').val().toLowerCase();
        const statusValue = $('#statusFilter').val();

        $('#bungalowsTable tbody tr').filter(function() {
            const row = $(this);
            const text = row.text().toLowerCase();
            const sessionStatus = row.find('.session-status-info .badge').text().trim();
            const hasSession = row.find('.session-info .badge').length > 0;

            // Check search criteria
            const matchesSearch = text.indexOf(searchValue) > -1;

            // Check status filter
            let matchesStatus = true;
            if (statusValue) {
                if (statusValue === 'no-session') {
                    matchesStatus = !hasSession;
                } else {
                    matchesStatus = sessionStatus === statusValue;
                }
            }

            row.toggle(matchesSearch && matchesStatus);
        });

        // Update visible count
        updateVisibleCount();
    }

    function updateVisibleCount() {
        const visibleRows = $('#bungalowsTable tbody tr:visible').length;
        const totalRows = $('#bungalowsTable tbody tr').length;

        // Add or update count display
        let countDisplay = $('#rowCount');
        if (countDisplay.length === 0) {
            countDisplay = $('<small id="rowCount" class="text-muted ms-2"></small>');
            $('.card-title').append(countDisplay);
        }

        if (visibleRows === totalRows) {
            countDisplay.text(`(${totalRows} bungalows)`);
        } else {
            countDisplay.text(`(${visibleRows}/${totalRows} bungalows)`);
        }

        // Update statistics cards
        updateStatisticsCards();
    }

    function updateStatisticsCards() {
        const visibleRows = $('#bungalowsTable tbody tr:visible');

        let total = 0;
        let available = 0;
        let occupied = 0;
        let maintenance = 0;

        visibleRows.each(function() {
            total++;
            const statusCell = $(this).find('td:nth-child(7)'); // Statut Bungalow column
            const statusText = statusCell.text().toLowerCase();

            if (statusText.includes('disponible')) {
                available++;
            } else if (statusText.includes('occupé')) {
                occupied++;
            } else if (statusText.includes('maintenance')) {
                maintenance++;
            }
        });

        // Update the statistics cards with animation
        animateCounterUpdate('#totalCount', total);
        animateCounterUpdate('#availableCount', available);
        animateCounterUpdate('#occupiedCount', occupied);
        animateCounterUpdate('#maintenanceCount', maintenance);
    }

    function animateCounterUpdate(selector, newValue) {
        const element = $(selector);
        const currentValue = parseInt(element.text()) || 0;

        if (currentValue !== newValue) {
            element.addClass('updating');

            // Simple counter animation
            let start = currentValue;
            const increment = newValue > start ? 1 : -1;
            const timer = setInterval(() => {
                start += increment;
                element.text(start);

                if (start === newValue) {
                    clearInterval(timer);
                    element.removeClass('updating');
                }
            }, 50);
        }
    }

    $('#searchInput').on('keyup', filterTable);
    $('#statusFilter').on('change', filterTable);

    // Handle Enter key for direct search
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            const searchTerm = $(this).val().trim();
            if (searchTerm) {
                window.location.href = `/bungalows?search=${encodeURIComponent(searchTerm)}`;
            } else {
                window.location.href = '/bungalows';
            }
        }
    });

    // Table row hover effects
    $('#bungalowsTable tbody tr').hover(
        function() {
            $(this).addClass('table-hover-effect');
        },
        function() {
            $(this).removeClass('table-hover-effect');
        }
    );

    // Enhanced search placeholder
    $('#searchInput').attr('placeholder', 'Rechercher par numéro, emplacement, session, statut...');

    // Initialize count
    updateVisibleCount();
});

function viewDetails(bungalowId) {
    // Implement view details functionality
    window.location.href = `/bungalows/view/${bungalowId}`;
}

function confirmDelete(bungalowId, bungalowName) {
    $('#bungalowName').text(bungalowName);
    $('#deleteForm').attr('action', `/bungalows/delete/${bungalowId}`);
    $('#deleteModal').modal('show');
}

$('#confirmDeleteBtn').on('click', function() {
    $('#deleteForm').submit();
});

function exportData() {
    // Implement export functionality
    window.location.href = '/api/bungalows/export';
}
</script>
{% endblock %}
