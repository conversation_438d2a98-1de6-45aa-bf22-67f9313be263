{% extends 'base.html' %}

{% block title %}État d'Occupation des Bungalows - BANGHALAU{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="mb-4">État d'Occupation des Bungalows</h1>

    <!-- Date Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="dateFilterForm" class="row g-3 align-items-center">
                <div class="col-md-4">
                    <label for="statusDate" class="form-label">Date</label>
                    <input type="date" class="form-control" id="statusDate" name="date" value="{{ date }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">Afficher</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Status Summary -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Résumé d'Occupation</h5>
                </div>
                <div class="card-body">
                    <canvas id="occupancyChart" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Statistiques</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6>Total des Bungalows</h6>
                                <h2 id="totalBungalows">{{ bungalows|length }}</h2>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6>Bungalows Occupés</h6>
                                <h2 id="occupiedBungalows">{{ bungalows|selectattr('status', 'equalto', 'Occupied')|list|length }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6>Bungalows Disponibles</h6>
                                <h2 id="availableBungalows">{{ bungalows|selectattr('status', 'equalto', 'Available')|list|length }}</h2>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6>Taux d'Occupation</h6>
                                <h2 id="occupancyRate">
                                    {% set occupied = bungalows|selectattr('status', 'equalto', 'Occupied')|list|length %}
                                    {% set total = bungalows|length %}
                                    {% if total > 0 %}
                                        {{ ((occupied / total) * 100)|round(1) }}%
                                    {% else %}
                                        0%
                                    {% endif %}
                                </h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bungalows Status Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title">Détails de l'État des Bungalows</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Numéro</th>
                            <th>Emplacement</th>
                            <th>Capacité</th>
                            <th>État</th>
                            <th>Personnel Assigné</th>
                            <th>Session</th>
                            <th>Période</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for bungalow in bungalows %}
                        <tr class="{{ 'table-success' if bungalow.status == 'Available' else 'table-danger' }}">
                            <td>{{ bungalow.numero }}</td>
                            <td>{{ bungalow.endroit }}</td>
                            <td>{{ bungalow.capacite }}</td>
                            <td>
                                <span class="badge {{ 'bg-success' if bungalow.status == 'Available' else 'bg-danger' }}">
                                    {{ 'Disponible' if bungalow.status == 'Available' else 'Occupé' }}
                                </span>
                            </td>
                            <td>
                                {% if bungalow.status == 'Occupied' and bungalow.personnel_nom %}
                                    {{ bungalow.personnel_nom }} {{ bungalow.personnel_prenom }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if bungalow.status == 'Occupied' and bungalow.session_numero %}
                                    {{ bungalow.session_numero }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if bungalow.status == 'Occupied' and bungalow.date_debut %}
                                    {{ bungalow.date_debut }} - {{ bungalow.date_fin or 'Non spécifié' }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if bungalow.status == 'Available' %}
                                    <button class="btn btn-sm btn-primary assign-btn" data-id="{{ bungalow.id }}" data-bs-toggle="modal" data-bs-target="#assignBungalowModal">
                                        <i class="fas fa-user-plus"></i> Assigner
                                    </button>
                                {% else %}
                                    <button class="btn btn-sm btn-warning release-btn" data-id="{{ bungalow.distribution_id }}">
                                        <i class="fas fa-user-minus"></i> Libérer
                                    </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center">Aucun bungalow enregistré</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Assign Bungalow Modal -->
<div class="modal fade" id="assignBungalowModal" tabindex="-1" aria-labelledby="assignBungalowModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignBungalowModalLabel">Assigner un Bungalow</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="assignBungalowForm">
                <input type="hidden" id="bungalow_id" name="bungalow_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="distribution_numero" class="form-label">Numéro de Distribution</label>
                        <input type="text" class="form-control" id="distribution_numero" name="numero" required>
                    </div>
                    <div class="mb-3">
                        <label for="personnel_id" class="form-label">Personnel Militaire</label>
                        <select class="form-select" id="personnel_id" name="personnel_id">
                            <option value="">Choisir le personnel militaire</option>
                            <!-- Will be filled dynamically -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="session_id" class="form-label">Session</label>
                        <select class="form-select" id="session_id" name="session_id">
                            <option value="">Choisir la session</option>
                            <!-- Will be filled dynamically -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="date_debut" class="form-label">Date de Début</label>
                        <input type="date" class="form-control" id="date_debut" name="date_debut" required>
                    </div>
                    <div class="mb-3">
                        <label for="date_fin" class="form-label">Date de Fin</label>
                        <input type="date" class="form-control" id="date_fin" name="date_fin">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Assigner</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Date filter form submission
        $('#dateFilterForm').submit(function(e) {
            e.preventDefault();
            const date = $('#statusDate').val();
            window.location.href = "{{ url_for('bungalows_status') }}?date=" + date;
        });

        // Occupancy Chart
        const occupancyCtx = document.getElementById('occupancyChart').getContext('2d');
        const occupiedCount = {{ bungalows|selectattr('status', 'equalto', 'Occupied')|list|length }};
        const availableCount = {{ bungalows|selectattr('status', 'equalto', 'Available')|list|length }};

        const occupancyChart = new Chart(occupancyCtx, {
            type: 'pie',
            data: {
                labels: ['Occupé', 'Disponible'],
                datasets: [{
                    data: [occupiedCount, availableCount],
                    backgroundColor: ['#dc3545', '#28a745'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Load personnel and sessions for assign modal
        $('#assignBungalowModal').on('show.bs.modal', function(event) {
            const button = $(event.relatedTarget);
            const bungalowId = button.data('id');
            $('#bungalow_id').val(bungalowId);

            // Set default dates
            const today = new Date().toISOString().split('T')[0];
            $('#date_debut').val(today);

            // Load personnel
            $.ajax({
                url: '/api/personnel',
                type: 'GET',
                success: function(personnel) {
                    let options = '<option value="">Choisir le personnel militaire</option>';
                    personnel.forEach(function(person) {
                        options += `<option value="${person.id}">${person.nom} ${person.prenom} (${person.grade_name || 'Sans grade'})</option>`;
                    });
                    $('#personnel_id').html(options);
                }
            });

            // Load sessions
            $.ajax({
                url: '/api/sessions',
                type: 'GET',
                success: function(sessions) {
                    let options = '<option value="">Choisir la session</option>';
                    sessions.forEach(function(session) {
                        options += `<option value="${session.id}">${session.numero} (${session.date_debut} - ${session.date_fin || 'Non spécifié'})</option>`;
                    });
                    $('#session_id').html(options);
                }
            });
        });

        // Assign bungalow form submission
        $('#assignBungalowForm').submit(function(e) {
            e.preventDefault();

            $.ajax({
                url: '/api/distributions',
                type: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    $('#assignBungalowModal').modal('hide');
                    location.reload();
                },
                error: function(xhr) {
                    alert('Error: ' + xhr.responseJSON.error);
                }
            });
        });

        // Release bungalow
        $('.release-btn').click(function() {
            if (confirm('Êtes-vous sûr de vouloir libérer ce bungalow ?')) {
                const distributionId = $(this).data('id');

                $.ajax({
                    url: `/api/distributions/${distributionId}`,
                    type: 'DELETE',
                    success: function() {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('Error: ' + xhr.responseJSON.error);
                    }
                });
            }
        });
    });
</script>
{% endblock %}
