{% extends "base.html" %}

{% block title %}Panneau de Contrôle - BANGHALAU{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-cogs me-2 text-primary"></i>
                Panneau de Contrôle
            </h1>
            <p class="text-muted mb-0">Personnalisation de l'interface et paramètres système</p>
        </div>
    </div>

    <!-- Control Sections -->
    <div class="row">
        <!-- Interface Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-palette me-2"></i>
                        Paramètres d'Interface
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Language Settings -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-language me-2 text-info"></i>
                            Langue de l'Interface
                        </label>
                        <select class="form-select" id="interfaceLanguage">
                            <option value="fr" selected>Français</option>
                            <option value="ar">العربية</option>
                            <option value="en">English</option>
                        </select>
                        <div class="form-text">Langue principale de l'interface utilisateur</div>
                    </div>

                    <!-- Theme Settings -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-moon me-2 text-warning"></i>
                            Thème de l'Interface
                        </label>
                        <select class="form-select" id="interfaceTheme">
                            <option value="light" selected>Clair</option>
                            <option value="dark">Sombre</option>
                            <option value="auto">Automatique</option>
                        </select>
                        <div class="form-text">Apparence générale de l'interface</div>
                    </div>

                    <!-- Font Settings -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-font me-2 text-success"></i>
                            Police de Caractères
                        </label>
                        <select class="form-select" id="interfaceFont">
                            <option value="arial" selected>Arial</option>
                            <option value="helvetica">Helvetica</option>
                            <option value="roboto">Roboto</option>
                            <option value="opensans">Open Sans</option>
                        </select>
                        <div class="form-text">Police utilisée dans toute l'application</div>
                    </div>

                    <!-- Animation Settings -->
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableAnimations" checked>
                            <label class="form-check-label fw-bold" for="enableAnimations">
                                <i class="fas fa-magic me-2 text-purple"></i>
                                Activer les Animations
                            </label>
                        </div>
                        <div class="form-text">Animations et transitions dans l'interface</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Paramètres du Tableau de Bord
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Dashboard Type -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-chart-pie me-2 text-primary"></i>
                            Type de Tableau de Bord
                        </label>
                        <select class="form-select" id="dashboardType">
                            <option value="modern" selected>Moderne</option>
                            <option value="classic">Classique</option>
                            <option value="minimal">Minimal</option>
                        </select>
                        <div class="form-text">Style d'affichage du tableau de bord principal</div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-chart-bar me-2 text-info"></i>
                            Cartes Statistiques à Afficher
                        </label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showBungalows" checked>
                                    <label class="form-check-label" for="showBungalows">Bungalows</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showPersonnel" checked>
                                    <label class="form-check-label" for="showPersonnel">Personnel</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showDistributions" checked>
                                    <label class="form-check-label" for="showDistributions">Distributions</label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showSessions" checked>
                                    <label class="form-check-label" for="showSessions">Sessions</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showUnites" checked>
                                    <label class="form-check-label" for="showUnites">Unités</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showGrades" checked>
                                    <label class="form-check-label" for="showGrades">Grades</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Auto Refresh -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sync-alt me-2 text-warning"></i>
                            Actualisation Automatique
                        </label>
                        <select class="form-select" id="autoRefresh">
                            <option value="0">Désactivée</option>
                            <option value="30" selected>30 secondes</option>
                            <option value="60">1 minute</option>
                            <option value="300">5 minutes</option>
                        </select>
                        <div class="form-text">Fréquence de mise à jour des données</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Settings -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Paramètres Système
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Date Format -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-calendar me-2 text-primary"></i>
                            Format de Date
                        </label>
                        <select class="form-select" id="dateFormat">
                            <option value="dd/mm/yyyy" selected>DD/MM/YYYY</option>
                            <option value="mm/dd/yyyy">MM/DD/YYYY</option>
                            <option value="yyyy-mm-dd">YYYY-MM-DD</option>
                        </select>
                    </div>

                    <!-- Pagination -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-list me-2 text-info"></i>
                            Éléments par Page
                        </label>
                        <select class="form-select" id="itemsPerPage">
                            <option value="10">10</option>
                            <option value="25" selected>25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>

                    <!-- Backup Settings -->
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoBackup" checked>
                            <label class="form-check-label fw-bold" for="autoBackup">
                                <i class="fas fa-database me-2 text-success"></i>
                                Sauvegarde Automatique
                            </label>
                        </div>
                        <div class="form-text">Sauvegarde quotidienne automatique de la base de données</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Menu Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bars me-2"></i>
                        Paramètres du Menu
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Sidebar Style -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sidebar me-2 text-primary"></i>
                            Style de la Barre Latérale
                        </label>
                        <select class="form-select" id="sidebarStyle">
                            <option value="expanded" selected>Étendue</option>
                            <option value="collapsed">Réduite</option>
                            <option value="overlay">Superposition</option>
                        </select>
                    </div>

                    <!-- Menu Items -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-eye me-2 text-success"></i>
                            Éléments du Menu Visibles
                        </label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="menuDashboard" checked>
                                    <label class="form-check-label" for="menuDashboard">Tableau de Bord</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="menuBungalows" checked>
                                    <label class="form-check-label" for="menuBungalows">Bungalows</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="menuDistributions" checked>
                                    <label class="form-check-label" for="menuDistributions">Distributions</label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="menuPersonnel" checked>
                                    <label class="form-check-label" for="menuPersonnel">Personnel</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="menuSessions" checked>
                                    <label class="form-check-label" for="menuSessions">Sessions</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="menuSystem" checked>
                                    <label class="form-check-label" for="menuSystem">Système</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Menu Icons -->
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="showMenuIcons" checked>
                            <label class="form-check-label fw-bold" for="showMenuIcons">
                                <i class="fas fa-icons me-2 text-warning"></i>
                                Afficher les Icônes du Menu
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <button type="button" class="btn btn-primary btn-lg me-3" onclick="saveSettings()">
                        <i class="fas fa-save me-2"></i>
                        Enregistrer les Paramètres
                    </button>
                    <button type="button" class="btn btn-secondary btn-lg me-3" onclick="resetSettings()">
                        <i class="fas fa-undo me-2"></i>
                        Réinitialiser
                    </button>
                    <button type="button" class="btn btn-info btn-lg" onclick="previewChanges()">
                        <i class="fas fa-eye me-2"></i>
                        Aperçu des Modifications
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Load saved settings
    loadSettings();
    
    // Apply real-time preview for some settings
    $('#interfaceTheme, #interfaceFont').on('change', function() {
        applyPreview();
    });
});

function loadSettings() {
    // Load settings from localStorage or server
    const settings = JSON.parse(localStorage.getItem('banghalauSettings') || '{}');
    
    // Apply loaded settings to form
    Object.keys(settings).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = settings[key];
            } else {
                element.value = settings[key];
            }
        }
    });
}

function saveSettings() {
    const settings = {};
    
    // Collect all settings
    const inputs = document.querySelectorAll('#control_panel input, #control_panel select');
    inputs.forEach(input => {
        if (input.type === 'checkbox') {
            settings[input.id] = input.checked;
        } else {
            settings[input.id] = input.value;
        }
    });
    
    // Save to localStorage
    localStorage.setItem('banghalauSettings', JSON.stringify(settings));
    
    // Show success message
    showToast('Paramètres enregistrés avec succès!', 'success');
    
    // Apply settings
    applySettings(settings);
}

function resetSettings() {
    if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres?')) {
        localStorage.removeItem('banghalauSettings');
        location.reload();
    }
}

function previewChanges() {
    applyPreview();
    showToast('Aperçu appliqué temporairement', 'info');
}

function applyPreview() {
    const theme = document.getElementById('interfaceTheme').value;
    const font = document.getElementById('interfaceFont').value;
    
    // Apply theme
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${theme}`);
    
    // Apply font
    document.body.style.fontFamily = font === 'arial' ? 'Arial, sans-serif' :
                                   font === 'helvetica' ? 'Helvetica, sans-serif' :
                                   font === 'roboto' ? 'Roboto, sans-serif' :
                                   font === 'opensans' ? 'Open Sans, sans-serif' : 'Arial, sans-serif';
}

function applySettings(settings) {
    // Apply all settings to the interface
    if (settings.interfaceTheme) {
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${settings.interfaceTheme}`);
    }
    
    if (settings.interfaceFont) {
        const fontFamily = settings.interfaceFont === 'arial' ? 'Arial, sans-serif' :
                          settings.interfaceFont === 'helvetica' ? 'Helvetica, sans-serif' :
                          settings.interfaceFont === 'roboto' ? 'Roboto, sans-serif' :
                          settings.interfaceFont === 'opensans' ? 'Open Sans, sans-serif' : 'Arial, sans-serif';
        document.body.style.fontFamily = fontFamily;
    }
    
    // Apply other settings as needed
    console.log('Settings applied:', settings);
}

// Show toast notification
function showToast(message, type = 'info') {
    // Use the existing toast function from base.html
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        alert(message);
    }
}
</script>
{% endblock %}
