{% extends "base.html" %}

{% block title %}Tableau de Bord - BANGHALAU{% endblock %}

{% block content %}
<style>
/* Reset and Override Styles */
.main-content .container-fluid {
    padding: 0 !important;
    margin: 0 !important;
    max-width: none !important;
}

/* Modern Elegant Dashboard Container */
.elegant-dashboard {
    font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 0;
    margin: 0;
    direction: ltr;
    overflow-x: hidden;
}

/* Dashboard Header */
.dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.welcome-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.welcome-icon .icon-circle {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.welcome-text .main-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.welcome-text .subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 1rem 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: blink 2s infinite;
}

.status-dot.active {
    background: #2ecc71;
    box-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

.status-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    cursor: pointer;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    color: white;
}

/* Statistics Overview */
.stats-overview {
    max-width: 1400px;
    margin: 0 auto 3rem auto;
    padding: 0 2rem;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.primary-card::before { background: linear-gradient(90deg, #667eea, #764ba2); }
.success-card::before { background: linear-gradient(90deg, #2ecc71, #27ae60); }
.warning-card::before { background: linear-gradient(90deg, #f39c12, #e67e22); }
.info-card::before { background: linear-gradient(90deg, #3498db, #2980b9); }

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.primary-card .card-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
.success-card .card-icon { background: linear-gradient(135deg, #2ecc71, #27ae60); }
.warning-card .card-icon { background: linear-gradient(135deg, #f39c12, #e67e22); }
.info-card .card-icon { background: linear-gradient(135deg, #3498db, #2980b9); }

.card-menu {
    color: #bdc3c7;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.card-menu:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #7f8c8d;
}

.card-body {
    margin-bottom: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
}

.stat-label {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin: 0 0 1rem 0;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.trend-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.trend-icon.up {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
}

.trend-icon.down {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.trend-text {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.card-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 1rem;
}

.view-details {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.view-details:hover {
    color: #764ba2;
    gap: 0.75rem;
}

/* Quick Actions Section */
.quick-actions {
    max-width: 1400px;
    margin: 0 auto 3rem auto;
    padding: 0 2rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.quick-action-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.quick-action-card:hover::before {
    left: 100%;
}

.quick-action-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    color: inherit;
    text-decoration: none;
}

.action-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
}

.action-icon.bungalow { background: linear-gradient(135deg, #667eea, #764ba2); }
.action-icon.personnel { background: linear-gradient(135deg, #2ecc71, #27ae60); }
.action-icon.distribution { background: linear-gradient(135deg, #f39c12, #e67e22); }
.action-icon.unite { background: linear-gradient(135deg, #e74c3c, #c0392b); }

.action-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.action-desc {
    font-size: 0.9rem;
    color: #7f8c8d;
    line-height: 1.4;
}

/* Animation Keyframes */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Animation Classes */
.animate-in {
    animation: fadeInUp 0.6s ease-out forwards;
}

.stat-card {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.stat-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.quick-action-card {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.quick-action-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Hover Effects */
.stat-card:hover .card-icon {
    transform: rotate(10deg) scale(1.1);
    transition: transform 0.3s ease;
}

.quick-action-card:hover .action-icon {
    transform: rotate(-5deg) scale(1.1);
    transition: transform 0.3s ease;
}

/* Gradient Text Effect */
.main-title {
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Loading Animation for Buttons */
.action-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.action-btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Enhanced Card Shadows */
.stat-card,
.quick-action-card {
    box-shadow:
        0 10px 40px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-card:hover,
.quick-action-card:hover {
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 24px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .welcome-section {
        flex-direction: column;
        text-align: center;
    }

    .stats-container,
    .actions-grid {
        grid-template-columns: 1fr;
    }

    .welcome-text .main-title {
        font-size: 2rem;
    }

    .stat-card,
    .quick-action-card {
        margin-bottom: 1rem;
    }
}
</style>

<!-- Elegant Dashboard Container -->
<div class="elegant-dashboard">
    
    <!-- Header Section -->
    <div class="dashboard-header">
        <div class="header-content">
            <div class="welcome-section">
                <div class="welcome-icon">
                    <div class="icon-circle">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="welcome-text">
                    <h1 class="main-title">Tableau de Bord Principal</h1>
                    <p class="subtitle">Système de Gestion des Bungalows Militaires - BANGHALAU</p>
                    <div class="status-indicator">
                        <span class="status-dot active"></span>
                        <span class="status-text">Système opérationnel</span>
                    </div>
                </div>
            </div>
            <div class="header-actions">
                <button class="action-btn refresh-btn" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i>
                    <span>Actualiser</span>
                </button>
                <button class="action-btn export-btn" onclick="exportReport()">
                    <i class="fas fa-download"></i>
                    <span>Exporter</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="stats-overview">
        <div class="stats-container">
            <div class="stat-card primary-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="card-menu">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
                <div class="card-body">
                    <h3 class="stat-number">{{ bungalow_count }}</h3>
                    <p class="stat-label">Total Bungalows</p>
                    <div class="stat-trend">
                        <span class="trend-icon up">
                            <i class="fas fa-arrow-up"></i>
                        </span>
                        <span class="trend-text">+12% du mois dernier</span>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('bungalows') }}" class="view-details">
                        Voir détails
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>

            <div class="stat-card success-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="card-menu">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
                <div class="card-body">
                    <h3 class="stat-number">{{ personnel_count }}</h3>
                    <p class="stat-label">Personnel Militaire</p>
                    <div class="stat-trend">
                        <span class="trend-icon up">
                            <i class="fas fa-arrow-up"></i>
                        </span>
                        <span class="trend-text">+8% du mois dernier</span>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('personnel') }}" class="view-details">
                        Voir détails
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>

            <div class="stat-card warning-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="card-menu">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
                <div class="card-body">
                    <h3 class="stat-number">{{ distribution_count }}</h3>
                    <p class="stat-label">Distributions Actives</p>
                    <div class="stat-trend">
                        <span class="trend-icon up">
                            <i class="fas fa-arrow-up"></i>
                        </span>
                        <span class="trend-text">+15% du mois dernier</span>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('distributions') }}" class="view-details">
                        Voir détails
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>

            <div class="stat-card info-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="card-menu">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
                <div class="card-body">
                    <h3 class="stat-number">{{ "%.1f"|format(occupancy_rate) }}%</h3>
                    <p class="stat-label">Taux d'Occupation</p>
                    <div class="stat-trend">
                        <span class="trend-icon down">
                            <i class="fas fa-arrow-down"></i>
                        </span>
                        <span class="trend-text">-3% du mois dernier</span>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('bungalows') }}" class="view-details">
                        Voir détails
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="quick-actions">
        <div class="actions-grid">
            <a href="{{ url_for('add_bungalow') }}" class="quick-action-card">
                <div class="action-icon bungalow">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="action-title">Ajouter Nouveau Bungalow</div>
                <div class="action-desc">Ajouter une nouvelle unité résidentielle au système avec spécifications et localisation</div>
            </a>

            <a href="{{ url_for('add_personnel') }}" class="quick-action-card">
                <div class="action-icon personnel">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="action-title">Ajouter Personnel Militaire</div>
                <div class="action-desc">Enregistrer nouveau personnel militaire avec grade et unité militaire</div>
            </a>

            <a href="{{ url_for('nouvelle_distribution') }}" class="quick-action-card">
                <div class="action-icon distribution">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div class="action-title">Nouvelle Distribution</div>
                <div class="action-desc">Créer une nouvelle distribution avec interface améliorée</div>
            </a>

            <a href="{{ url_for('add_unite') }}" class="quick-action-card">
                <div class="action-icon unite">
                    <i class="fas fa-flag"></i>
                </div>
                <div class="action-title">Ajouter Unité Militaire</div>
                <div class="action-desc">Enregistrer une nouvelle unité militaire dans le système avec détails</div>
            </a>

            <a href="{{ url_for('distributions') }}" class="quick-action-card">
                <div class="action-icon bungalow">
                    <i class="fas fa-list"></i>
                </div>
                <div class="action-title">Voir Toutes Distributions</div>
                <div class="action-desc">Consulter et gérer toutes les distributions actives et terminées</div>
            </a>

            <a href="{{ url_for('filtre_distributions') }}" class="quick-action-card">
                <div class="action-icon personnel">
                    <i class="fas fa-filter"></i>
                </div>
                <div class="action-title">Filtrer Distributions</div>
                <div class="action-desc">Rechercher et filtrer les distributions selon différents critères</div>
            </a>

            <a href="{{ url_for('bungalows_status') }}" class="quick-action-card">
                <div class="action-icon distribution">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="action-title">État des Bungalows</div>
                <div class="action-desc">Afficher l'état d'occupation des bungalows et statistiques détaillées</div>
            </a>

            <a href="{{ url_for('backup_restore') }}" class="quick-action-card">
                <div class="action-icon unite">
                    <i class="fas fa-database"></i>
                </div>
                <div class="action-title">Sauvegarde & Restauration</div>
                <div class="action-desc">Créer et restaurer les sauvegardes de la base de données</div>
            </a>
        </div>
    </div>

    <!-- Developer Credit -->
    <div style="text-align: center; padding: 2rem; color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">
        <p style="margin: 0;">Ce logiciel Développé Par MAMMERI-WAHID 2025</p>
        <p style="margin: 0.5rem 0 0 0; font-size: 0.8rem;">Dashboard Moderne et Élégant - Version 2.0</p>
    </div>

</div>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    initializeElegantDashboard();
    setInterval(refreshDashboardData, 300000);
});

function initializeElegantDashboard() {
    // Enhanced hover effects for cards
    $('.stat-card, .quick-action-card').hover(
        function() {
            $(this).css({
                'transform': 'translateY(-5px) scale(1.02)',
                'box-shadow': '0 20px 60px rgba(0, 0, 0, 0.15)',
                'transition': 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
            });
        },
        function() {
            $(this).css({
                'transform': 'translateY(0) scale(1)',
                'box-shadow': '0 10px 40px rgba(0, 0, 0, 0.1)',
                'transition': 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
            });
        }
    );

    // Animate numbers on load
    animateNumbers();

    // Add click ripple effect
    addRippleEffect();

    // Animate cards on scroll
    animateOnScroll();
}

function animateNumbers() {
    $('.stat-number').each(function() {
        const $this = $(this);
        const finalValue = parseInt($this.text()) || 0;
        $this.text('0');

        $({ counter: 0 }).animate({ counter: finalValue }, {
            duration: 2000,
            easing: 'easeOutQuart',
            step: function() {
                $this.text(Math.ceil(this.counter));
            },
            complete: function() {
                $this.text(finalValue);
            }
        });
    });
}

function addRippleEffect() {
    $('.action-btn, .quick-action-card').on('click', function(e) {
        const $this = $(this);
        const ripple = $('<span class="ripple"></span>');

        $this.append(ripple);

        const size = Math.max($this.outerWidth(), $this.outerHeight());
        const x = e.pageX - $this.offset().left - size / 2;
        const y = e.pageY - $this.offset().top - size / 2;

        ripple.css({
            width: size,
            height: size,
            left: x,
            top: y,
            position: 'absolute',
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.3)',
            transform: 'scale(0)',
            animation: 'ripple 0.6s linear',
            pointerEvents: 'none'
        });

        setTimeout(() => ripple.remove(), 600);
    });
}

function animateOnScroll() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                $(entry.target).addClass('animate-in');
            }
        });
    }, { threshold: 0.1 });

    $('.stat-card, .quick-action-card').each(function() {
        observer.observe(this);
    });
}

function refreshDashboard() {
    const $refreshBtn = $('.refresh-btn');
    $refreshBtn.find('i').attr('class', 'fas fa-spinner fa-spin');
    $refreshBtn.prop('disabled', true);
    setTimeout(() => { location.reload(); }, 1000);
}

function refreshDashboardData() {
    console.log('Données du tableau de bord actualisées');
}

function exportReport() {
    const $exportBtn = $('.export-btn');
    const originalText = $exportBtn.find('span').text();

    $exportBtn.find('i').attr('class', 'fas fa-spinner fa-spin');
    $exportBtn.find('span').text('Exportation en cours...');
    $exportBtn.prop('disabled', true);

    setTimeout(() => {
        window.open('/api/dashboard/export', '_blank');
        $exportBtn.find('i').attr('class', 'fas fa-download');
        $exportBtn.find('span').text(originalText);
        $exportBtn.prop('disabled', false);
    }, 2000);
}
</script>
{% endblock %}
