{% extends "base.html" %}

{% block title %}Tableau de Bord Moderne - BANGHALAU{% endblock %}

{% block content %}
<style>
/* Reset and Override Styles */
.main-content .container-fluid {
    padding: 0 !important;
    margin: 0 !important;
    max-width: none !important;
}

/* Modern Dashboard Container */
.modern-dashboard {
    font-family: Arial, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem;
    margin: 0;
    direction: ltr;
    overflow-x: hidden;
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 0 1rem;
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: none;
    border-radius: 12px;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 0.5rem;
    display: none;
}

.suggestion-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.suggestion-item:hover {
    background-color: #f8fafc;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-location {
    color: #6b7280;
    font-size: 0.85rem;
}

.suggestion-action {
    color: #667eea;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.suggestion-item.no-results {
    color: #6b7280;
    text-align: center;
    cursor: default;
}

.suggestion-item.no-results:hover {
    background-color: transparent;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.time-filter {
    display: flex;
    background: white;
    border-radius: 8px;
    padding: 0.25rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.time-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
}

.time-btn.active {
    background: #667eea;
    color: white;
}

.notification-btn {
    position: relative;
    background: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #ef4444;
    border-radius: 50%;
}

/* Stats Cards Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card.purple {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-card.teal {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
}

.stat-card.orange {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #333;
}

.stat-card.green {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.stat-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    background: rgba(255, 255, 255, 0.2);
}

.stat-title {
    font-size: 0.85rem;
    font-weight: 500;
    opacity: 0.9;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-subtitle {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Bonus Section */
.bonus-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.bonus-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bonus-text h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.bonus-text p {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.bonus-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.bonus-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.bonus-illustration {
    position: relative;
}

.coins {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Main Content Grid */
.main-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

/* Chart Section */
.chart-section {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-stats {
    display: flex;
    gap: 2rem;
}

.chart-stat {
    text-align: center;
}

.chart-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
}

.chart-stat-label {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.chart-container {
    height: 300px;
    position: relative;
}

/* Sidebar */
.sidebar-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.sidebar-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.sidebar-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
}

.protection-circle {
    width: 120px;
    height: 120px;
    margin: 0 auto 1rem;
    position: relative;
}

.circle-progress {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(#667eea 0deg 288deg, #e5e7eb 288deg 360deg);
    display: flex;
    align-items: center;
    justify-content: center;
}

.circle-inner {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.issues-list {
    space-y: 0.75rem;
}

.issue-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.issue-label {
    font-size: 0.85rem;
    color: #6b7280;
}

.issue-value {
    font-weight: 600;
    color: #1f2937;
}

.issue-bar {
    width: 60px;
    height: 4px;
    border-radius: 2px;
    margin-left: 0.5rem;
}

.issue-bar.simple { background: #ef4444; }
.issue-bar.medium { background: #f59e0b; }
.issue-bar.complex { background: #10b981; }

/* Responsive Design */
@media (max-width: 1024px) {
    .main-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .modern-dashboard {
        padding: 1rem;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .bonus-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}
</style>

<!-- Modern Dashboard Container -->
<div class="modern-dashboard">
    
    <!-- Header -->
    <div class="dashboard-header">
        <div class="search-container">
            <i class="fas fa-search search-icon"></i>
            <input type="text" class="search-input" id="bungalowSearch" placeholder="Rechercher un Bungalow *">
        </div>
        
        <div class="header-actions">
            <div class="time-filter">
                <button class="time-btn">Semaine Dernière</button>
                <button class="time-btn active">Mois Dernier</button>
            </div>
            
            <button class="notification-btn">
                <i class="fas fa-bell"></i>
                <span class="notification-badge"></span>
            </button>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
        <div class="stat-card purple">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-home"></i>
                </div>
                <div class="stat-title">Total Bungalows</div>
            </div>
            <div class="stat-value">{{ bungalow_count }}</div>
            <div class="stat-subtitle">Unités Disponibles</div>
        </div>

        <div class="stat-card teal">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-title">Personnel Militaire</div>
            </div>
            <div class="stat-value">{{ personnel_count }}</div>
            <div class="stat-subtitle">Membres Enregistrés</div>
        </div>

        <div class="stat-card orange">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="stat-title">Distributions</div>
            </div>
            <div class="stat-value">{{ distribution_count }}</div>
            <div class="stat-subtitle">Total Attributions</div>
        </div>

        <div class="stat-card green">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <div class="stat-title">Taux d'Occupation</div>
            </div>
            <div class="stat-value">{{ occupancy_rate|round(1) }}%</div>
            <div class="stat-subtitle">Bungalows Occupés</div>
        </div>
    </div>

    <!-- Statistics Summary Section -->
    <div class="bonus-section">
        <div class="bonus-content">
            <div class="bonus-text">
                <h2>Résumé des Activités</h2>
                <p>{{ grades_count }} Grades • {{ unites_count }} Unités Militaires<br>{{ sessions_count }} Sessions • {{ users_count }} Utilisateurs</p>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('nouvelle_distribution') }}" class="bonus-btn" style="text-decoration: none;">
                        <i class="fas fa-plus me-2"></i>Nouvelle Distribution
                    </a>
                    <button class="bonus-btn" onclick="window.location.href='{{ url_for('distributions') }}'">
                        Voir Détails
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
            <div class="bonus-illustration">
                <div class="coins">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="main-grid">
        <!-- Chart Section -->
        <div class="chart-section">
            <div class="chart-header">
                <div class="chart-stats">
                    <div class="chart-stat">
                        <div class="chart-stat-value">{{ bungalow_count }}</div>
                        <div class="chart-stat-label">Total Bungalows</div>
                    </div>
                    <div class="chart-stat">
                        <div class="chart-stat-value">{{ distribution_count }}</div>
                        <div class="chart-stat-label">Distributions</div>
                    </div>
                </div>
                
                <div class="time-filter">
                    <button class="time-btn active">Nov 2024</button>
                </div>
            </div>
            
            <div class="chart-container">
                <canvas id="distributionsChart"></canvas>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar-section">
            <!-- Occupancy Status -->
            <div class="sidebar-card">
                <div class="sidebar-header">
                    <div class="sidebar-title">Taux d'Occupation</div>
                </div>

                <div class="protection-circle">
                    <div class="circle-progress">
                        <div class="circle-inner">{{ occupancy_rate|round(0)|int }}%</div>
                    </div>
                </div>

                <div style="text-align: center; margin-bottom: 1rem;">
                    <div style="font-weight: 600; color: #1f2937;">Occupation Actuelle</div>
                    <div style="font-size: 0.8rem; color: #6b7280;">{{ distribution_count }} distributions sur {{ bungalow_count }} bungalows disponibles</div>
                </div>

                <button style="width: 100%; padding: 0.75rem; background: #f3f4f6; border: none; border-radius: 8px; font-weight: 500; cursor: pointer;">
                    Voir Détails <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <!-- System Statistics -->
            <div class="sidebar-card">
                <div class="sidebar-header">
                    <div class="sidebar-title">Statistiques Système</div>
                </div>

                <div class="issues-list">
                    <div class="issue-item">
                        <span class="issue-label">Bungalows</span>
                        <div style="display: flex; align-items: center;">
                            <span class="issue-value">{{ bungalow_count }}</span>
                            <div class="issue-bar simple"></div>
                        </div>
                    </div>

                    <div class="issue-item">
                        <span class="issue-label">Personnel</span>
                        <div style="display: flex; align-items: center;">
                            <span class="issue-value">{{ personnel_count }}</span>
                            <div class="issue-bar medium"></div>
                        </div>
                    </div>

                    <div class="issue-item">
                        <span class="issue-label">Sessions</span>
                        <div style="display: flex; align-items: center;">
                            <span class="issue-value">{{ sessions_count }}</span>
                            <div class="issue-bar complex"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>

<script>
$(document).ready(function() {
    initializeModernDashboard();
    initializeBungalowSearch();
});

function initializeModernDashboard() {
    // Initialize chart
    initializeChart();

    // Add hover effects
    $('.stat-card, .sidebar-card').hover(
        function() {
            $(this).css('transform', 'translateY(-4px)');
        },
        function() {
            $(this).css('transform', 'translateY(0)');
        }
    );
}

function initializeBungalowSearch() {
    const searchInput = $('#bungalowSearch');

    // Handle search input
    searchInput.on('input', function() {
        const searchTerm = $(this).val().trim();

        if (searchTerm.length >= 2) {
            // Show search suggestions
            showBungalowSuggestions(searchTerm);
        } else {
            hideBungalowSuggestions();
        }
    });

    // Handle Enter key press
    searchInput.on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            const searchTerm = $(this).val().trim();
            if (searchTerm) {
                // Redirect to bungalows page with search parameter
                window.location.href = `/bungalows?search=${encodeURIComponent(searchTerm)}`;
            }
        }
    });

    // Handle click outside to hide suggestions
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.search-container').length) {
            hideBungalowSuggestions();
        }
    });
}

function showBungalowSuggestions(searchTerm) {
    // Create suggestions dropdown if it doesn't exist
    let suggestionsContainer = $('.search-suggestions');
    if (suggestionsContainer.length === 0) {
        suggestionsContainer = $('<div class="search-suggestions"></div>');
        $('.search-container').append(suggestionsContainer);
    }

    // Fetch bungalow suggestions via AJAX
    $.ajax({
        url: '/api/bungalows/search',
        method: 'GET',
        data: { q: searchTerm },
        success: function(data) {
            displayBungalowSuggestions(data, suggestionsContainer);
        },
        error: function() {
            // If API fails, show direct search option
            const suggestions = [
                {
                    numero: searchTerm,
                    endroit: 'Rechercher dans tous les bungalows',
                    isDirectSearch: true
                }
            ];
            displayBungalowSuggestions(suggestions, suggestionsContainer);
        }
    });
}

function displayBungalowSuggestions(suggestions, container) {
    container.empty();

    if (suggestions.length === 0) {
        container.append('<div class="suggestion-item no-results">Aucun bungalow trouvé</div>');
    } else {
        suggestions.forEach(function(bungalow) {
            const suggestionItem = $(`
                <div class="suggestion-item" data-numero="${bungalow.numero}">
                    <div class="suggestion-main">
                        <strong>Bungalow ${bungalow.numero}</strong>
                        ${bungalow.isDirectSearch ? '' : `<span class="suggestion-location">${bungalow.endroit}</span>`}
                    </div>
                    ${bungalow.isDirectSearch ? '<div class="suggestion-action">Appuyez sur Entrée pour rechercher</div>' : ''}
                </div>
            `);

            suggestionItem.on('click', function() {
                if (bungalow.isDirectSearch) {
                    window.location.href = `/bungalows?search=${encodeURIComponent(bungalow.numero)}`;
                } else {
                    window.location.href = `/bungalows/view/${bungalow.id}`;
                }
            });

            container.append(suggestionItem);
        });
    }

    container.show();
}

function hideBungalowSuggestions() {
    $('.search-suggestions').hide();
}

function initializeChart() {
    const ctx = document.getElementById('distributionsChart');
    if (ctx) {
        // Calculate monthly distribution data based on current stats
        const totalDistributions = {{ distribution_count }};
        const monthlyData = [];

        // Generate realistic monthly distribution data
        for (let i = 0; i < 11; i++) {
            const baseValue = Math.floor(totalDistributions / 11);
            const variation = Math.floor(Math.random() * (baseValue * 0.5));
            monthlyData.push(baseValue + variation);
        }

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
                datasets: [{
                    label: 'Distributions par Mois',
                    data: monthlyData,
                    backgroundColor: [
                        '#667eea', '#4ecdc4', '#667eea', '#ff6b9d', '#667eea',
                        '#ff6b6b', '#667eea', '#4ecdc4', '#667eea', '#ff6b9d', '#667eea'
                    ],
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Distributions: ' + context.parsed.y;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: { display: false },
                        ticks: { display: false }
                    },
                    x: {
                        grid: { display: false },
                        ticks: {
                            color: '#9ca3af',
                            font: { size: 12 }
                        }
                    }
                },
                elements: {
                    bar: {
                        borderRadius: 8
                    }
                }
            }
        });
    }
}
</script>
{% endblock %}
