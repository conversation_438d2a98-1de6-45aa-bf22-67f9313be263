{% extends "base.html" %}

{% block title %}Distributions - BANGHALAU{% endblock %}

{% block extra_css %}
<!-- استيراد الخطوط العربية المحسنة -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
/* خطوط عربية محسنة لصفحة Distributions */
:root {
    --arabic-font-primary: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --arabic-font-secondary: '<PERSON><PERSON><PERSON>', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --mixed-font: 'Cairo', 'Noto Sans Arabic', 'Inter', 'Segoe UI', sans-serif;
}

/* تحسين عام للنصوص */
body, .card, .btn, .form-control, .modal-content {
    font-family: var(--mixed-font);
    font-feature-settings: 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* العناوين */
.distribution-title, .card-title, .modal-title, h1, h2, h3, h4, h5, h6 {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    line-height: 1.3;
}

/* الجداول */
.table-modern th {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    font-size: 0.95rem;
}

.table-modern td {
    font-family: var(--mixed-font);
    font-weight: 400;
    font-size: 0.9rem;
    line-height: 1.5;
    vertical-align: middle;
}

/* النصوص المخصصة للتوزيعات */
.personnel-name {
    font-family: var(--mixed-font);
    font-weight: 500;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.bungalow-location {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.personnel-grade {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.distribution-details {
    font-family: var(--mixed-font);
}

/* الشارات والتسميات */
.badge {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* التنبيهات */
.alert {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.5;
    direction: auto;
    text-align: start;
}

/* الأزرار */
.btn {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* النماذج */
.form-control, .form-select {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
}

.form-label {
    font-family: var(--mixed-font);
    font-weight: 500;
}

/* تحسين مظهر فلتر الوحدة مع البحث */
#uniteFilter {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m11.25 11.25 3 3m-3-3a6 6 0 1 1-12 0 6 6 0 0 1 12 0Z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
    font-family: Arial, sans-serif;
    font-weight: 500;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

#uniteFilter:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تحسين مظهر القائمة المنسدلة للوحدات */
#uniteDropdown {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    background: white;
}

#uniteDropdown .dropdown-item {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: Arial, sans-serif;
    font-weight: 400;
    min-height: 60px;
    display: flex;
    align-items: center;
}

#uniteDropdown .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

#uniteDropdown .dropdown-item:last-child {
    border-bottom: none;
}

#uniteDropdown .dropdown-item i {
    color: #6c757d;
    width: 1.2rem;
}

#uniteDropdown .dropdown-item:hover i,
#uniteDropdown .dropdown-item.active i {
    color: #0d6efd;
}

#uniteDropdown .dropdown-item.active {
    background-color: #e7f3ff;
    border-left: 4px solid #0d6efd;
    box-shadow: inset 0 0 0 1px rgba(13, 110, 253, 0.2);
}

/* تحسين مظهر النصوص في القائمة الموسعة */
#uniteDropdown .dropdown-item .fw-bold,
#uniteDropdown .dropdown-item .fw-semibold {
    font-family: Arial, sans-serif;
    color: #2c3e50;
    font-size: 0.95rem;
}

#uniteDropdown .dropdown-item small {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: Arial, sans-serif;
}

#uniteDropdown .dropdown-item .badge {
    font-size: 0.7rem;
    padding: 0.25em 0.5em;
    border-radius: 0.375rem;
}

#uniteDropdown .dropdown-item i {
    font-size: 1.1rem;
    width: 1.5rem;
    text-align: center;
}

/* تحسين التفاعل مع العناصر */
#uniteDropdown .dropdown-item:hover .fw-bold,
#uniteDropdown .dropdown-item:hover .fw-semibold,
#uniteDropdown .dropdown-item.active .fw-bold,
#uniteDropdown .dropdown-item.active .fw-semibold {
    color: #0d6efd;
}

#uniteDropdown .dropdown-item:hover .badge,
#uniteDropdown .dropdown-item.active .badge {
    background-color: #0d6efd !important;
    color: white !important;
}

/* تحسين مظهر الفاصل */
#uniteDropdown .dropdown-divider {
    margin: 0.25rem 0;
    border-top: 1px solid #e9ecef;
}

/* تحسين مظهر النص الإضافي في الخيارات */
#uniteDropdown .dropdown-item small {
    font-size: 0.75rem;
    opacity: 0.7;
}

/* تحسين مظهر رسالة عدم وجود نتائج */
#noResultsMessage {
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    margin: 0.5rem;
    border: 2px dashed #dee2e6;
    min-height: 80px;
}

#noResultsMessage .fw-semibold {
    color: #495057;
    font-size: 0.9rem;
}

#noResultsMessage small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* تحسين التفاعل مع البحث */
#uniteFilter:focus {
    background-color: #fff;
}

#uniteFilter.is-valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.38 1.38'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23198754' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m11.25 11.25 3 3m-3-3a6 6 0 1 1-12 0 6 6 0 0 1 12 0Z'/%3e%3c/svg%3e");
    background-position: right calc(0.375em + 0.1875rem) center, right 0.75rem center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem), 1rem;
}

/* تحسين عرض الفلاتر للشاشات الصغيرة */
@media (max-width: 768px) {
    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    #statusFilter, #uniteFilter, #searchInput {
        width: 100% !important;
        margin-bottom: 0.5rem;
    }

    .position-relative {
        width: 100% !important;
    }

    #uniteDropdown {
        min-width: 100% !important;
    }

    #uniteDropdown .dropdown-item {
        min-height: 50px;
        padding: 0.75rem 1rem;
    }

    #uniteDropdown .dropdown-item .fw-bold,
    #uniteDropdown .dropdown-item .fw-semibold {
        font-size: 0.9rem;
    }

    #uniteDropdown .dropdown-item small {
        font-size: 0.75rem;
    }

    .personnel-name, .bungalow-location, .personnel-grade {
        font-size: 0.9rem;
    }
}

/* Checkbox styling */
.form-check-input {
    border: 2px solid #2c5530;
    border-radius: 4px;
    width: 18px;
    height: 18px;
}

.form-check-input:checked {
    background-color: #2c5530;
    border-color: #2c5530;
}

.form-check-input:focus {
    border-color: #2c5530;
    box-shadow: 0 0 0 0.25rem rgba(44, 85, 48, 0.25);
}

.form-check-input:indeterminate {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Bulk actions styling */
#bulkActionsGroup {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Selected row highlighting */
tr.selected {
    background-color: rgba(44, 85, 48, 0.1) !important;
}

/* Checkbox column width */
th:first-child,
td:first-child {
    width: 40px;
    text-align: center;
}

/* Hover effect for checkboxes */
.form-check-input:hover {
    border-color: #3a6b3e;
    cursor: pointer;
}

/* Bulk actions buttons styling */
#bulkActionsGroup .btn {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

#bulkActionsGroup .btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

#bulkActionsGroup .btn-warning:hover {
    background-color: #ffca2c;
    border-color: #ffc720;
}

#bulkActionsGroup .btn-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

#bulkActionsGroup .btn-info:hover {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
}

/* تحسين للطباعة */
@media print {
    body, .table, .card, .btn, .form-control, .modal-content {
        font-family: 'Cairo', 'Noto Sans Arabic', serif;
        color: #000;
    }
}

/* تحسينات إضافية للشاشات الصغيرة */
@media (max-width: 768px) {
    .table-modern th, .table-modern td {
        font-size: 0.85rem;
    }

    .distribution-title, .card-title {
        font-size: 1.1rem;
    }

    .personnel-name, .bungalow-location, .personnel-grade {
        font-size: 0.85rem;
    }

    .badge {
        font-size: 0.7rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gradient distribution-title">
                        <i class="fas fa-exchange-alt me-2"></i>Distribution des Bungalows
                    </h1>
                    <p class="text-muted mb-0">Gérez toutes les distributions de bungalows</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('nouvelle_distribution') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Nouvelle Distribution
                    </a>
                    <a href="{{ url_for('new_distribution') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-1"></i>Ancienne Version
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card-modern bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">Total Bungalows</h6>
                            <h3 class="mb-0">{{ total_bungalows }}</h3>
                            <small class="text-white-75">Nombre total de bungalows</small>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-home fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card-modern bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">Bungalows Distribués</h6>
                            <h3 class="mb-0">{{ distributed_count }}</h3>
                            <small class="text-white-75">Actuellement occupés</small>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-white"
                                 style="width: {{ (distributed_count / total_bungalows * 100) if total_bungalows > 0 else 0 }}%"></div>
                        </div>
                        <small class="text-white-75">
                            {{ "%.1f"|format((distributed_count / total_bungalows * 100) if total_bungalows > 0 else 0) }}% du total
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card-modern bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">Bungalows Disponibles</h6>
                            <h3 class="mb-0">{{ available_count }}</h3>
                            <small class="text-white-75">Prêts pour distribution</small>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-white"
                                 style="width: {{ (available_count / total_bungalows * 100) if total_bungalows > 0 else 0 }}%"></div>
                        </div>
                        <small class="text-white-75">
                            {{ "%.1f"|format((available_count / total_bungalows * 100) if total_bungalows > 0 else 0) }}% du total
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card-modern bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">Distributions Actives</h6>
                            <h3 class="mb-0">{{ active_distributions }}</h3>
                            <small class="text-white-75">En cours actuellement</small>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-white-75">
                            <i class="fas fa-arrow-up me-1"></i>{{ upcoming_distributions }} à venir
                            <span class="mx-2">|</span>
                            <i class="fas fa-history me-1"></i>{{ expired_distributions }} expirées
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Quick Stats Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                        Résumé des Distributions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">État des Bungalows</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-danger">
                                    <i class="fas fa-circle me-2"></i>Distribués
                                </span>
                                <span class="fw-bold">{{ distributed_count }} / {{ total_bungalows }}</span>
                            </div>
                            <div class="progress mb-3" style="height: 8px;">
                                <div class="progress-bar bg-danger"
                                     style="width: {{ (distributed_count / total_bungalows * 100) if total_bungalows > 0 else 0 }}%"></div>
                                <div class="progress-bar bg-success"
                                     style="width: {{ (available_count / total_bungalows * 100) if total_bungalows > 0 else 0 }}%"></div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-success">
                                    <i class="fas fa-circle me-2"></i>Disponibles
                                </span>
                                <span class="fw-bold">{{ available_count }} / {{ total_bungalows }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">État des Distributions</h6>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h4 mb-1 text-success">{{ active_distributions }}</div>
                                        <small class="text-muted">Actives</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h4 mb-1 text-info">{{ upcoming_distributions }}</div>
                                        <small class="text-muted">À venir</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h4 mb-1 text-secondary">{{ expired_distributions }}</div>
                                        <small class="text-muted">Expirées</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card-modern">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Liste des Distributions
                </h5>
                <div class="d-flex gap-2">
                    <select class="form-control" id="statusFilter" style="width: 150px;">
                        <option value="">Tous les statuts</option>
                        <option value="active">Actives</option>
                        <option value="expired">Expirées</option>
                        <option value="upcoming">À venir</option>
                    </select>
                    <div class="position-relative" style="width: 300px;">
                        <input type="text"
                               class="form-control"
                               id="uniteFilter"
                               placeholder="Rechercher une unité..."
                               autocomplete="off">
                        <input type="hidden" id="selectedUniteValue" name="selected_unite_value">
                        <div class="position-absolute top-100 start-0 w-100" style="z-index: 1000;">
                            <div id="uniteDropdown" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto; display: none; min-width: 350px;">
                                <a class="dropdown-item unite-option" href="#" data-value="" data-id="">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-list me-3 text-primary"></i>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold">Toutes les unités</div>
                                            <small class="text-muted">Afficher toutes les distributions</small>
                                        </div>
                                        <span class="badge bg-primary">{{ unites|length }} unité(s)</span>
                                    </div>
                                </a>
                                <div class="dropdown-divider"></div>
                                {% for unite in unites %}
                                <a class="dropdown-item unite-option"
                                   href="#"
                                   data-value="{{ unite.description }}"
                                   data-id="{{ unite.id }}"
                                   title="Cliquez pour filtrer par {{ unite.description }}"
                                   data-bs-toggle="tooltip">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-shield-alt me-3 text-secondary"></i>
                                        <div class="flex-grow-1">
                                            <div class="fw-semibold">{{ unite.description }}</div>
                                            {% if unite.personnel_count is defined %}
                                                <small class="text-muted">{{ unite.personnel_count }} personnel(s)</small>
                                            {% else %}
                                                <small class="text-muted">Unité militaire</small>
                                            {% endif %}
                                        </div>
                                        <span class="badge bg-secondary">ID: {{ unite.id }}</span>
                                    </div>
                                </a>
                                {% endfor %}
                                <div class="dropdown-divider" id="noResultsDivider" style="display: none;"></div>
                                <div class="dropdown-item text-muted text-center" id="noResultsMessage" style="display: none;">
                                    <div class="py-3">
                                        <i class="fas fa-search fa-2x mb-2 text-secondary"></i>
                                        <div class="fw-semibold">Aucune unité trouvée</div>
                                        <small>Essayez un autre terme de recherche</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="text" class="form-control" id="searchInput" placeholder="Rechercher..." style="width: 250px;">

                    <!-- Bulk Actions -->
                    <div class="btn-group me-2" id="bulkActionsGroup" style="display: none;">
                        <button class="btn btn-warning" onclick="bulkPrintSelected()">
                            <i class="fas fa-print me-1"></i>Imprimer sélectionnés
                        </button>
                        <button class="btn btn-danger" onclick="bulkDeleteSelected()">
                            <i class="fas fa-trash me-1"></i>Supprimer sélectionnés
                        </button>
                        <button class="btn btn-info" onclick="bulkExportSelected()">
                            <i class="fas fa-download me-1"></i>Exporter sélectionnés
                        </button>
                    </div>

                    <div class="btn-group">
                        <button class="btn btn-outline-primary" onclick="printDistributions()">
                            <i class="fas fa-print me-1"></i>Imprimer
                        </button>
                        <button class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                            <span class="visually-hidden">Toggle Dropdown</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="printDistributions()">
                                <i class="fas fa-print me-2"></i>Imprimer directement
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="downloadPDF()">
                                <i class="fas fa-file-pdf me-2 text-danger"></i>Télécharger PDF
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                                <i class="fas fa-file-csv me-2"></i>Export CSV
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('print_distributions_table') }}" target="_blank">
                                <i class="fas fa-table me-2"></i>طباعة جدول التوزيعات
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-modern" id="distributionsTable">
                <thead>
                    <tr>
                        <th style="width: 40px;">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                <label class="form-check-label" for="selectAll"></label>
                            </div>
                        </th>
                        <th><i class="fas fa-hashtag me-1"></i>Numéro</th>
                        <th><i class="fas fa-home me-1"></i>Bungalow</th>
                        <th><i class="fas fa-user me-1"></i>Personnel</th>
                        <th><i class="fas fa-shield-alt me-1"></i>Unité</th>
                        <th><i class="fas fa-calendar me-1"></i>Session</th>
                        <th><i class="fas fa-calendar-alt me-1"></i>Période</th>
                        <th><i class="fas fa-info-circle me-1"></i>Statut</th>
                        <th><i class="fas fa-cogs me-1"></i>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for distribution in distributions %}
                    <tr data-status="{{ 'active' if distribution.date_fin and distribution.date_fin >= today else 'expired' if distribution.date_fin else 'upcoming' }}" data-distribution-id="{{ distribution.id }}" data-unite-id="{{ distribution.unite_id or '' }}">
                        <td>
                            <div class="form-check">
                                <input class="form-check-input distribution-checkbox" type="checkbox" value="{{ distribution.id }}" id="dist_{{ distribution.id }}" onchange="updateSelectAllState()">
                                <label class="form-check-label" for="dist_{{ distribution.id }}"></label>
                            </div>
                        </td>
                        <td>
                            <strong class="text-primary">{{ distribution.numero }}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-home text-primary me-2"></i>
                                <div>
                                    <strong>{{ distribution.bungalow_numero or 'Non défini' }}</strong>
                                    {% if distribution.bungalow_endroit %}
                                        <small class="text-muted d-block bungalow-location">{{ distribution.bungalow_endroit }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <strong class="personnel-name">{{ distribution.personnel_nom or 'Non défini' }} {{ distribution.personnel_prenom or '' }}</strong>
                                    {% if distribution.personnel_grade %}
                                        <small class="text-muted d-block personnel-grade">{{ distribution.personnel_grade }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if distribution.unite_description %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-shield-alt me-1"></i>{{ distribution.unite_description }}
                                </span>
                            {% else %}
                                <span class="text-muted">Non définie</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if distribution.session_numero %}
                                <span class="badge bg-info">
                                    <i class="fas fa-calendar me-1"></i>{{ distribution.session_numero }}
                                </span>
                            {% else %}
                                <span class="text-muted">Non définie</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="small">
                                {% if distribution.date_debut %}
                                    <div><i class="fas fa-play text-success me-1"></i>{{ distribution.date_debut }}</div>
                                {% endif %}
                                {% if distribution.date_fin %}
                                    <div><i class="fas fa-stop text-danger me-1"></i>{{ distribution.date_fin }}</div>
                                {% else %}
                                    <div><i class="fas fa-infinity text-warning me-1"></i>Indéterminée</div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if not distribution.date_debut %}
                                <span class="badge bg-secondary">Non planifiée</span>
                            {% elif distribution.date_debut > today %}
                                <span class="badge bg-info">À venir</span>
                            {% elif not distribution.date_fin or distribution.date_fin >= today %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Expirée</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('edit_distribution', distribution_id=distribution.id) }}"
                                   class="btn btn-sm btn-outline-warning"
                                   title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-info"
                                        onclick="viewDetails({{ distribution.id }})"
                                        title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger"
                                        onclick="confirmDelete({{ distribution.id }}, '{{ distribution.numero }}')"
                                        title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-exchange-alt fa-3x mb-3"></i>
                                <h5>Aucune distribution trouvée</h5>
                                <p>Commencez par créer votre première distribution.</p>
                                <a href="{{ url_for('new_distribution') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>Nouvelle Distribution
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Confirmer la Suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la distribution <strong id="distributionName"></strong> ?</p>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    Cette action est irréversible.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>Supprimer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden delete form -->
<form id="deleteForm" method="POST" style="display: none;"></form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips for unite options
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Unite search functionality
    let uniteOptions = [];
    $('.unite-option').each(function() {
        uniteOptions.push({
            value: $(this).data('value'),
            id: $(this).data('id'),
            text: $(this).text().trim(),
            element: $(this)
        });
    });

    $('#uniteFilter').on('input', function() {
        const searchTerm = $(this).val().toLowerCase().trim();
        const dropdown = $('#uniteDropdown');

        console.log('🔍 Unite search:', searchTerm);

        if (searchTerm.length === 0) {
            // Show all options when search is empty
            $('.unite-option').show();
            $('#noResultsMessage').hide();
            $('#noResultsDivider').hide();
            $(this).attr('placeholder', 'Rechercher une unité...');
            dropdown.show();
            return;
        }

        // Filter options based on search term
        let hasResults = false;
        let visibleCount = 0;

        $('.unite-option').each(function() {
            const option = $(this);
            const fullText = option.text().toLowerCase().trim();
            const value = option.data('value') ? option.data('value').toString().toLowerCase() : '';
            const unitName = option.find('.fw-semibold, .fw-bold').first().text().toLowerCase().trim();
            const description = option.find('small').text().toLowerCase().trim();

            // Multiple search criteria
            const matchesText = fullText.includes(searchTerm);
            const matchesValue = value.includes(searchTerm);
            const matchesName = unitName.includes(searchTerm);
            const matchesDescription = description.includes(searchTerm);

            const isMatch = matchesText || matchesValue || matchesName || matchesDescription;

            if (isMatch) {
                option.show();
                hasResults = true;
                visibleCount++;
            } else {
                option.hide();
            }
        });

        // Update UI based on results
        if (hasResults && visibleCount > 0) {
            $('#noResultsMessage').hide();
            $('#noResultsDivider').hide();
            $(this).attr('placeholder', `${visibleCount} unité(s) trouvée(s)`);
            dropdown.show();
        } else {
            $('#noResultsMessage').show();
            $('#noResultsDivider').show();
            $(this).attr('placeholder', 'Aucune unité trouvée');
            dropdown.show();
        }

        console.log(`📊 Unite search results: ${visibleCount} matches`);
    });

    // Handle unite selection
    $(document).on('click', '.unite-option', function(e) {
        e.preventDefault();
        const option = $(this);
        const value = option.data('value') || '';
        const id = option.data('id') || '';
        const text = option.text().trim();

        console.log('🎯 Unite selected:', { value, id, text });

        if (value === '' && id === '') {
            // Clear selection
            $('#uniteFilter').val('').attr('placeholder', 'Rechercher une unité...');
            $('#selectedUniteValue').val('');
        } else {
            // Extract unit name from the option
            let unitName = option.find('.fw-semibold, .fw-bold').first().text().trim();

            // Fallback to value if no name found
            if (!unitName) {
                unitName = value;
            }

            $('#uniteFilter').val(unitName);
            $('#selectedUniteValue').val(id || value);

            console.log('✅ Unite filter set:', { name: unitName, selectedValue: id || value });
        }

        $('#uniteDropdown').hide();

        // Apply filters immediately
        applyFilters();

        // Show success feedback
        $('#uniteFilter').removeClass('is-invalid').addClass('is-valid');
        setTimeout(() => {
            $('#uniteFilter').removeClass('is-valid');
        }, 2000);
    });

    // Show dropdown when focusing on unite filter
    $('#uniteFilter').on('focus', function() {
        $('.unite-option').show();
        $('#noResultsMessage').hide();
        $('#noResultsDivider').hide();
        $('#uniteDropdown').show();

        // Reset placeholder
        if ($(this).val() === '') {
            $(this).attr('placeholder', 'Rechercher une unité...');
        }
    });

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.position-relative').length) {
            $('#uniteDropdown').hide();
        }
    });

    // Clear selection when search is cleared and handle keyboard navigation
    $('#uniteFilter').on('keyup', function(e) {
        const currentValue = $(this).val().trim();

        if (currentValue === '') {
            $('#selectedUniteValue').val('');
            $(this).removeClass('is-valid is-invalid').attr('placeholder', 'Rechercher une unité...');
            applyFilters();
            console.log('🧹 Unite filter cleared');
        }

        // Handle keyboard navigation
        const dropdown = $('#uniteDropdown');
        const visibleOptions = $('.unite-option:visible');

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            if (visibleOptions.length > 0) {
                const current = $('.unite-option.active');
                current.removeClass('active');
                const next = current.length === 0 ? visibleOptions.first() : current.next('.unite-option:visible');
                if (next.length > 0) {
                    next.addClass('active');
                } else {
                    visibleOptions.first().addClass('active');
                }
            }
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            if (visibleOptions.length > 0) {
                const current = $('.unite-option.active');
                current.removeClass('active');
                const prev = current.length === 0 ? visibleOptions.last() : current.prev('.unite-option:visible');
                if (prev.length > 0) {
                    prev.addClass('active');
                } else {
                    visibleOptions.last().addClass('active');
                }
            }
        } else if (e.key === 'Enter') {
            e.preventDefault();
            const active = $('.unite-option.active');
            if (active.length > 0) {
                active.click();
            }
        } else if (e.key === 'Escape') {
            dropdown.hide();
            $(this).blur();
        }
    });

    // Combined filter functionality
    function applyFilters() {
        const searchValue = $('#searchInput').val().toLowerCase().trim();
        const statusValue = $('#statusFilter').val();
        const uniteValue = $('#selectedUniteValue').val();

        console.log('🔍 Applying filters:', { search: searchValue, status: statusValue, unite: uniteValue });

        let visibleCount = 0;
        const totalRows = $('#distributionsTable tbody tr').length;

        $('#distributionsTable tbody tr').each(function() {
            const row = $(this);

            // Skip if this is an empty row or header
            if (row.find('td').length === 0) {
                return;
            }

            const rowText = row.text().toLowerCase();
            let shouldShow = true;

            // Search filter - check all text content
            if (searchValue !== '') {
                const numero = row.find('td:nth-child(2)').text().toLowerCase().trim(); // Numéro
                const bungalowInfo = row.find('td:nth-child(3)').text().toLowerCase().trim(); // Bungalow
                const personnelName = row.find('td:nth-child(4)').text().toLowerCase().trim(); // Personnel
                const uniteInfo = row.find('td:nth-child(5)').text().toLowerCase().trim(); // Unité
                const sessionInfo = row.find('td:nth-child(6)').text().toLowerCase().trim(); // Session
                const dateInfo = row.find('td:nth-child(7)').text().toLowerCase().trim(); // Dates

                const matchesSearch = numero.includes(searchValue) ||
                                    bungalowInfo.includes(searchValue) ||
                                    personnelName.includes(searchValue) ||
                                    uniteInfo.includes(searchValue) ||
                                    sessionInfo.includes(searchValue) ||
                                    dateInfo.includes(searchValue) ||
                                    rowText.includes(searchValue);

                if (!matchesSearch) {
                    shouldShow = false;
                }
            }

            // Status filter
            if (statusValue !== '' && shouldShow) {
                const rowStatus = row.data('status') || '';
                if (rowStatus !== statusValue) {
                    shouldShow = false;
                }
            }

            // Unite filter
            if (uniteValue !== '' && shouldShow) {
                const rowUniteId = row.data('unite-id');
                const uniteText = row.find('td:nth-child(5)').text().toLowerCase().trim();

                let matchesUnite = false;

                // Try to match by ID first (if both are numeric)
                if (rowUniteId && !isNaN(uniteValue) && !isNaN(rowUniteId)) {
                    matchesUnite = rowUniteId.toString() === uniteValue.toString();
                }

                // If no ID match, try text matching
                if (!matchesUnite) {
                    matchesUnite = uniteText.includes(uniteValue.toLowerCase()) ||
                                 uniteValue.toLowerCase().includes(uniteText);
                }

                if (!matchesUnite) {
                    shouldShow = false;
                }
            }

            // Show/hide row
            if (shouldShow) {
                row.show();
                visibleCount++;
            } else {
                row.hide();
            }
        });

        // Update visible count display
        updateVisibleCount();

        // Update search statistics
        updateSearchStats(visibleCount, totalRows, searchValue, statusValue, uniteValue);

        console.log(`📊 Filter results: ${visibleCount}/${totalRows} rows visible`);
    }

    function updateVisibleCount() {
        const visibleRows = $('#distributionsTable tbody tr:visible').length;
        const totalRows = $('#distributionsTable tbody tr').length;

        // Add or update count display
        let countDisplay = $('#rowCount');
        if (countDisplay.length === 0) {
            countDisplay = $('<small id="rowCount" class="text-muted ms-2"></small>');
            $('.card-title').append(countDisplay);
        }

        // Update count text with better formatting
        if (visibleRows === totalRows) {
            countDisplay.text(`(${totalRows} distributions)`);
        } else {
            countDisplay.html(`(<span class="text-primary fw-bold">${visibleRows}</span>/${totalRows} distributions)`);
        }

        // Show/hide empty state message
        const emptyMessage = $('#emptyStateMessage');
        if (visibleRows === 0 && totalRows > 0) {
            if (emptyMessage.length === 0) {
                // Determine which filters are active
                const activeFilters = [];
                if ($('#searchInput').val().trim()) activeFilters.push('recherche');
                if ($('#statusFilter').val()) activeFilters.push('statut');
                if ($('#selectedUniteValue').val()) activeFilters.push('unité');

                const filterText = activeFilters.length > 0 ?
                    `Aucun résultat pour les filtres: ${activeFilters.join(', ')}` :
                    'Aucune distribution trouvée';

                const message = $(`
                    <tr id="emptyStateMessage">
                        <td colspan="8" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-search fa-3x mb-3 text-secondary"></i>
                                <h5>${filterText}</h5>
                                <p>Essayez de modifier vos critères de recherche ou d'effacer les filtres</p>
                                <div class="mt-3">
                                    <button class="btn btn-outline-primary btn-sm me-2" onclick="clearAllFilters()">
                                        <i class="fas fa-times me-1"></i>Effacer tous les filtres
                                    </button>
                                    <a href="{{ url_for('new_distribution') }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i>Nouvelle Distribution
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                `);
                $('#distributionsTable tbody').append(message);
            }
        } else {
            emptyMessage.remove();
        }
    }

    // Update search statistics
    function updateSearchStats(visibleCount, totalRows, searchValue, statusValue, uniteValue) {
        let statsDisplay = $('#searchStats');
        if (statsDisplay.length === 0) {
            statsDisplay = $('<div id="searchStats" class="small text-muted mt-2"></div>');
            $('.card-title').parent().append(statsDisplay);
        }

        if (searchValue || statusValue || uniteValue) {
            const activeFilters = [];
            if (searchValue) activeFilters.push(`recherche: "${searchValue}"`);
            if (statusValue) activeFilters.push(`statut: ${statusValue}`);
            if (uniteValue) {
                const uniteName = $('#uniteFilter').val() || uniteValue;
                activeFilters.push(`unité: ${uniteName}`);
            }

            const filterText = activeFilters.join(', ');
            const percentage = totalRows > 0 ? Math.round((visibleCount / totalRows) * 100) : 0;

            statsDisplay.html(`
                <i class="fas fa-filter me-1"></i>
                Filtres actifs: ${filterText}
                <span class="badge bg-light text-dark ms-2">${percentage}% des résultats</span>
            `);
        } else {
            statsDisplay.empty();
        }
    }

    // Clear all filters function
    function clearAllFilters() {
        $('#searchInput').val('');
        $('#statusFilter').val('');
        $('#uniteFilter').val('').attr('placeholder', 'Rechercher une unité...');
        $('#selectedUniteValue').val('');
        $('#searchStats').empty(); // Clear stats
        applyFilters();
        console.log('🧹 All filters cleared');
    }

    // Search functionality with debounce for better performance
    let searchTimeout;
    $('#searchInput').on('keyup', function() {
        clearTimeout(searchTimeout);

        // Show loading indicator
        const searchIcon = $(this).siblings('.search-icon');
        if (searchIcon.length === 0) {
            $(this).after('<i class="fas fa-spinner fa-spin search-icon position-absolute" style="right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d;"></i>');
        }

        searchTimeout = setTimeout(() => {
            applyFilters();
            // Remove loading indicator
            $('.search-icon').remove();
        }, 300); // 300ms delay
    });

    // Immediate search on Enter key
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            clearTimeout(searchTimeout);
            applyFilters();
        }
    });

    // Status filter
    $('#statusFilter').on('change', applyFilters);

    // Unite filter
    $('#uniteFilter').on('change', applyFilters);

    // Add search shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl+F to focus search
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            $('#searchInput').focus().select();
        }
        // Escape to clear search
        if (e.key === 'Escape' && $('#searchInput').is(':focus')) {
            $('#searchInput').val('');
            applyFilters();
        }
    });

    // Table row hover effects
    $('#distributionsTable tbody tr').hover(
        function() {
            $(this).addClass('table-hover-effect');
        },
        function() {
            $(this).removeClass('table-hover-effect');
        }
    );

    // Initialize count
    updateVisibleCount();

    // Initialize checkbox functionality
    updateSelectAllState();
});

// Checkbox functionality
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const distributionCheckboxes = document.querySelectorAll('.distribution-checkbox');

    distributionCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBulkActionsVisibility();
}

function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const distributionCheckboxes = document.querySelectorAll('.distribution-checkbox');
    const checkedCheckboxes = document.querySelectorAll('.distribution-checkbox:checked');

    if (checkedCheckboxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedCheckboxes.length === distributionCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }

    // Update row highlighting
    distributionCheckboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        if (checkbox.checked) {
            row.classList.add('selected');
        } else {
            row.classList.remove('selected');
        }
    });

    updateBulkActionsVisibility();
}

function updateBulkActionsVisibility() {
    const checkedCheckboxes = document.querySelectorAll('.distribution-checkbox:checked');
    const bulkActionsGroup = document.getElementById('bulkActionsGroup');

    if (checkedCheckboxes.length > 0) {
        bulkActionsGroup.style.display = 'block';
        // Update button text with count
        const printBtn = bulkActionsGroup.querySelector('button[onclick="bulkPrintSelected()"]');
        const deleteBtn = bulkActionsGroup.querySelector('button[onclick="bulkDeleteSelected()"]');
        const exportBtn = bulkActionsGroup.querySelector('button[onclick="bulkExportSelected()"]');

        printBtn.innerHTML = `<i class="fas fa-print me-1"></i>Imprimer sélectionnés (${checkedCheckboxes.length})`;
        deleteBtn.innerHTML = `<i class="fas fa-trash me-1"></i>Supprimer sélectionnés (${checkedCheckboxes.length})`;
        exportBtn.innerHTML = `<i class="fas fa-download me-1"></i>Exporter sélectionnés (${checkedCheckboxes.length})`;
    } else {
        bulkActionsGroup.style.display = 'none';
    }
}

function getSelectedDistributionIds() {
    const checkedCheckboxes = document.querySelectorAll('.distribution-checkbox:checked');
    return Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
}

// Bulk actions
function bulkPrintSelected() {
    const selectedIds = getSelectedDistributionIds();
    if (selectedIds.length === 0) {
        alert('Veuillez sélectionner au moins une distribution');
        return;
    }

    // Open print page for selected distributions
    const printUrl = `/print/distributions/bulk?ids=${selectedIds.join(',')}`;
    const printWindow = window.open(printUrl, '_blank', 'width=1200,height=800');
    if (printWindow) {
        printWindow.focus();
    } else {
        alert('Veuillez autoriser les pop-ups pour imprimer');
    }
}

function bulkDeleteSelected() {
    const selectedIds = getSelectedDistributionIds();
    if (selectedIds.length === 0) {
        alert('Veuillez sélectionner au moins une distribution');
        return;
    }

    if (confirm(`Êtes-vous sûr de vouloir supprimer ${selectedIds.length} distribution(s) sélectionnée(s) ?\n\nCette action est irréversible.`)) {
        // Create form for bulk delete
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/distributions/bulk-delete';

        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'distribution_ids';
            input.value = id;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
    }
}

function bulkExportSelected() {
    const selectedIds = getSelectedDistributionIds();
    if (selectedIds.length === 0) {
        alert('Veuillez sélectionner au moins une distribution');
        return;
    }

    // Export selected distributions to CSV
    const exportUrl = `/api/distributions/export/csv?ids=${selectedIds.join(',')}`;
    window.location.href = exportUrl;
}

function viewDetails(distributionId) {
    // Implement view details functionality
    window.location.href = `/distributions/view/${distributionId}`;
}

function confirmDelete(distributionId, distributionName) {
    $('#distributionName').text(distributionName);
    $('#deleteForm').attr('action', `/distributions/delete/${distributionId}`);
    $('#deleteModal').modal('show');
}

$('#confirmDeleteBtn').on('click', function() {
    $('#deleteForm').submit();
});

function exportData() {
    // Implement export functionality
    window.location.href = '/api/distributions/export';
}

// Print and Export Functions
function printDistributions() {
    // Get current filters
    const searchTerm = $('#searchInput').val();
    const statusFilter = $('#statusFilter').val();
    const uniteFilter = $('#selectedUniteValue').val();

    // Build URL with filters
    let printUrl = '/print/distributions';
    const params = new URLSearchParams();

    if (searchTerm) params.append('search', searchTerm);
    if (statusFilter) params.append('status', statusFilter);
    if (uniteFilter) params.append('unite', uniteFilter);

    if (params.toString()) {
        printUrl += '?' + params.toString();
    }

    // Open print page in new window
    const printWindow = window.open(printUrl, '_blank', 'width=1200,height=800');
    if (printWindow) {
        printWindow.focus();
    } else {
        alert('Veuillez autoriser les pop-ups pour imprimer');
    }
}





function exportToPDF() {
    // Get current filters
    const searchTerm = $('#searchInput').val();
    const statusFilter = $('#statusFilter').val();
    const uniteFilter = $('#selectedUniteValue').val();

    // Build URL with filters
    let exportUrl = '/print/distributions/export?format=pdf';

    if (searchTerm) exportUrl += '&search=' + encodeURIComponent(searchTerm);
    if (statusFilter) exportUrl += '&status=' + encodeURIComponent(statusFilter);
    if (uniteFilter) exportUrl += '&unite=' + encodeURIComponent(uniteFilter);

    // Open export page
    window.open(exportUrl, '_blank');
}



// Function to download PDF from Imprimer-Globale.html
function downloadPDF() {
    // Show loading indicator
    const loadingToast = showToast('Génération du PDF en cours...', 'info', 0);

    try {
        // Open the print page in a new window
        const printWindow = window.open('/imprimer-globale', '_blank');

        if (printWindow) {
            // Wait for the page to load, then trigger PDF download
            printWindow.onload = function() {
                setTimeout(() => {
                    // Use the browser's print to PDF functionality
                    printWindow.print();

                    // Close loading toast
                    if (loadingToast) {
                        loadingToast.hide();
                    }

                    showToast('PDF généré avec succès! Utilisez Ctrl+P pour imprimer ou sauvegarder en PDF.', 'success');
                }, 1000);
            };
        } else {
            // Fallback if popup is blocked
            if (loadingToast) {
                loadingToast.hide();
            }
            showToast('Popup bloqué. Redirection vers la page d\'impression...', 'warning');
            window.location.href = '/imprimer-globale';
        }
    } catch (error) {
        console.error('Error generating PDF:', error);
        if (loadingToast) {
            loadingToast.hide();
        }
        showToast('Erreur lors de la génération du PDF', 'error');
    }
}

// Helper function to show toast notifications
function showToast(message, type = 'info', duration = 5000) {
    // Create toast element
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'primary'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    // Add toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // Add toast to container
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Initialize and show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: duration > 0,
        delay: duration
    });

    toast.show();

    // Return toast object for manual control
    return toast;
}

function exportToCSV() {
    // Get current filters
    const searchTerm = $('#searchInput').val();
    const statusFilter = $('#statusFilter').val();
    const uniteFilter = $('#selectedUniteValue').val();

    // Build URL with filters
    let exportUrl = '/print/distributions/export?format=csv';

    if (searchTerm) exportUrl += '&search=' + encodeURIComponent(searchTerm);
    if (statusFilter) exportUrl += '&status=' + encodeURIComponent(statusFilter);
    if (uniteFilter) exportUrl += '&unite=' + encodeURIComponent(uniteFilter);

    // Redirect to export
    window.location.href = exportUrl;
}


</script>
{% endblock %}
