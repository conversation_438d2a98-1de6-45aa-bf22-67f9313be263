{% extends "base.html" %}

{% block title %}Modifier le Bungalow {{ bungalow.numero }} - BANGHALAU{% endblock %}

{% block extra_css %}
<!-- استيراد الخطوط العربية المحسنة -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
/* خطوط عربية محسنة لصفحة تعديل Bungalow */
:root {
    --arabic-font-primary: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --mixed-font: 'Cairo', 'Noto Sans Arabic', 'Inter', 'Segoe UI', sans-serif;
}

/* تحسين عام للنصوص */
body, .card, .btn, .form-control, .form-select, .form-check-label {
    font-family: var(--mixed-font);
    font-feature-settings: 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* العناوين */
.card-title, h1, h2, h3, h4, h5, h6 {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    line-height: 1.3;
}

/* النماذج */
.form-control, .form-select {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
    line-height: 1.5;
}

.form-label {
    font-family: var(--mixed-font);
    font-weight: 500;
}

/* الأزرار */
.btn {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* التنبيهات */
.alert {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.5;
}

/* النصوص العربية */
.arabic-text {
    font-family: var(--arabic-font-primary);
    font-weight: 500;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .card-title, h1, h2, h3 {
        font-size: 1.1rem;
    }

    .form-control, .form-select {
        font-size: 0.9rem;
    }
}

/* تحسين حقل الرقم */
#numero {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.5;
    direction: auto;
    text-align: start;
    font-size: 1rem;
}

/* تحسين حقل الموقع */
#endroit {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.5;
    direction: auto;
    text-align: start;
    font-size: 1rem;
}

/* تحسين حقل السعة */
#capacite {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.5;
    direction: auto;
    text-align: start;
    font-size: 1rem;
}

/* تحسين حقل الخصائص */
#caracteristiques {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.6;
    direction: auto;
    text-align: start;
    font-size: 1rem;
    resize: vertical;
    min-height: 100px;
}

/* تحسين النماذج العربية */
.form-control.arabic-input {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين التسميات */
.form-label {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين النصوص المساعدة */
.form-text {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين العناوين */
.card-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

/* تحسين التنبيهات */
.alert {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين الأزرار */
.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

.modal-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.modal-body {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين رسائل التحقق */
.invalid-feedback, .valid-feedback {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين مظهر حقل الحالة */
#statut {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

#statut:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

#statut option {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    padding: 0.5rem;
}

/* ألوان مختلفة للحالات */
#statut.status-disponible {
    border-color: #198754;
    background-color: #f8fff9;
}

#statut.status-occupe {
    border-color: #dc3545;
    background-color: #fff8f8;
}

#statut.status-maintenance {
    border-color: #fd7e14;
    background-color: #fff9f5;
}

#statut.status-reserve {
    border-color: #0dcaf0;
    background-color: #f0fcff;
}

#statut.status-hors-service {
    border-color: #6c757d;
    background-color: #f8f9fa;
}

#statut.status-renovation {
    border-color: #6f42c1;
    background-color: #faf8ff;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    #numero, #endroit, #capacite, #caracteristiques, #notes_statut {
        font-size: 0.95rem;
    }

    #statut {
        font-size: 0.9rem;
    }
}

/* تحسين مظهر البحث للجلسات */
.session-search-container {
    position: relative;
}

#session_dropdown {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    background: white;
}

#session_dropdown .dropdown-item {
    padding: 1rem;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.2s ease;
    cursor: pointer;
}

#session_dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

#session_dropdown .dropdown-item:last-child {
    border-bottom: none;
}

#session_search {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m11.25 11.25 3 3m-3-3a6 6 0 1 1-12 0 6 6 0 0 1 12 0Z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
}

.session-option .badge {
    font-size: 0.7rem;
}

.session-option small {
    font-size: 0.75rem;
    opacity: 0.8;
}

.session-option .fw-bold {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
    color: #2c5530;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="card-title">
                            <i class="fas fa-edit me-2 text-warning"></i>
                            Modifier le Bungalow {{ bungalow.numero }}
                        </h2>
                        <a href="{{ url_for('bungalows') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="numero" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>Numéro du Bungalow *
                                </label>
                                <input type="text"
                                       class="form-control arabic-input"
                                       id="numero"
                                       name="numero"
                                       value="{{ bungalow.numero }}"
                                       placeholder="Ex: B001"
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir un numéro de bungalow.
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="endroit" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>Emplacement *
                                </label>
                                <input type="text"
                                       class="form-control arabic-input"
                                       id="endroit"
                                       name="endroit"
                                       value="{{ bungalow.endroit }}"
                                       placeholder="Ex: Zone Nord"
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir l'emplacement du bungalow.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="capacite" class="form-label">
                                    <i class="fas fa-users me-1"></i>Capacité (personnes) *
                                </label>
                                <input type="number"
                                       class="form-control arabic-input"
                                       id="capacite"
                                       name="capacite"
                                       value="{{ bungalow.capacite }}"
                                       min="1"
                                       max="20"
                                       placeholder="Ex: 4"
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir une capacité valide (1-20 personnes).
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="caracteristiques" class="form-label">
                                    <i class="fas fa-list-ul me-1"></i>Caractéristiques
                                </label>
                                <textarea class="form-control arabic-input"
                                          id="caracteristiques"
                                          name="caracteristiques"
                                          rows="3"
                                          placeholder="Ex: Climatisé, avec jardin, vue sur mer...">{{ bungalow.caracteristiques or '' }}</textarea>
                                <small class="form-text text-muted">
                                    Décrivez les caractéristiques spéciales du bungalow (optionnel)
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="statut" class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>Statut du Bungalow *
                                </label>
                                <select class="form-control" id="statut" name="statut" required>
                                    <option value="">Sélectionnez un statut</option>
                                    <option value="Disponible" {{ 'selected' if bungalow.statut == 'Disponible' else '' }}>
                                        Disponible
                                    </option>
                                    <option value="Occupé" {{ 'selected' if bungalow.statut == 'Occupé' else '' }}>
                                        Occupé
                                    </option>
                                    <option value="Maintenance" {{ 'selected' if bungalow.statut == 'Maintenance' else '' }}>
                                        En Maintenance
                                    </option>
                                    <option value="Réservé" {{ 'selected' if bungalow.statut == 'Réservé' else '' }}>
                                        Réservé
                                    </option>
                                    <option value="Hors Service" {{ 'selected' if bungalow.statut == 'Hors Service' else '' }}>
                                        Hors Service
                                    </option>
                                    <option value="En Rénovation" {{ 'selected' if bungalow.statut == 'En Rénovation' else '' }}>
                                        En Rénovation
                                    </option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner un statut pour le bungalow.
                                </div>
                                <small class="form-text text-muted">
                                    <i class="fas fa-info me-1"></i>Modifiez l'état actuel du bungalow
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="notes_statut" class="form-label">
                                    <i class="fas fa-sticky-note me-1"></i>Notes sur le Statut
                                </label>
                                <textarea class="form-control arabic-input"
                                          id="notes_statut"
                                          name="notes_statut"
                                          rows="3"
                                          placeholder="Ex: Maintenance programmée, problème de plomberie...">{{ bungalow.notes_statut or '' }}</textarea>
                                <small class="form-text text-muted">
                                    Ajoutez des détails sur le statut actuel (optionnel)
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="session_search" class="form-label">
                                    <i class="fas fa-calendar-alt me-1"></i>Session Associée
                                </label>
                                <div class="position-relative">
                                    <input type="text"
                                           class="form-control arabic-input"
                                           id="session_search"
                                           placeholder="Rechercher une session..."
                                           autocomplete="off"
                                           value="{% if current_session %}{{ current_session.numero }} - {{ current_session.description }} ({{ current_session.etat_session }}){% endif %}">
                                    <input type="hidden" id="session_id" name="session_id" value="{{ current_session.id if current_session else '' }}">
                                    <div class="position-absolute top-100 start-0 w-100" style="z-index: 1000;">
                                        <div id="session_dropdown" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto; display: none;">
                                            {% for session in sessions %}
                                            <a class="dropdown-item session-option"
                                               href="#"
                                               data-id="{{ session.id }}"
                                               data-numero="{{ session.numero }}"
                                               data-description="{{ session.description }}"
                                               data-date-debut="{{ session.date_debut }}"
                                               data-date-fin="{{ session.date_fin }}"
                                               data-etat="{{ session.etat_session }}">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <span class="fw-bold">{{ session.numero }}</span>
                                                        <br>
                                                        <small class="text-muted">{{ session.description }}</small>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="badge bg-{{ 'success' if session.etat_session == 'Active' else 'warning' if session.etat_session == 'Planifiée' else 'secondary' }}">
                                                            {{ session.etat_session }}
                                                        </span>
                                                        <br>
                                                        <small class="text-muted">{{ session.date_debut }} - {{ session.date_fin or 'En cours' }}</small>
                                                    </div>
                                                </div>
                                            </a>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                <small class="form-text text-muted">
                                    <i class="fas fa-search me-1"></i>Tapez pour rechercher et modifier l'association à une session (optionnel)
                                </small>
                                {% if current_session %}
                                <div class="mt-2">
                                    <span class="badge bg-info">
                                        <i class="fas fa-link me-1"></i>Actuellement associé à: {{ current_session.numero }}
                                    </span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                    <i class="fas fa-trash me-1"></i>Supprimer ce Bungalow
                                </button>

                                <div class="d-flex gap-2">
                                    <a href="{{ url_for('bungalows') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>Annuler
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Enregistrer les Modifications
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Delete Form (hidden) -->
                <form id="deleteForm" method="POST" action="{{ url_for('delete_bungalow', bungalow_id=bungalow.id) }}" style="display: none;">
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Confirmer la Suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le bungalow <strong>{{ bungalow.numero }}</strong> ?</p>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    Cette action est irréversible et supprimera toutes les données associées.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" onclick="deleteBungalow()">
                    <i class="fas fa-trash me-1"></i>Supprimer Définitivement
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Capacity validation
    $('#capacite').on('input', function() {
        const value = parseInt($(this).val());
        if (value < 1 || value > 20) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid').addClass('is-valid');
        }
    });

    // Real-time validation feedback
    $('input[required], textarea[required], select[required]').on('input change', function() {
        if ($(this).val().trim() !== '') {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });

    // Status field styling based on selection
    $('#statut').on('change', function() {
        const value = $(this).val();
        const statusClasses = ['status-disponible', 'status-occupe', 'status-maintenance', 'status-reserve', 'status-hors-service', 'status-renovation'];

        // Remove all status classes
        $(this).removeClass(statusClasses.join(' '));

        // Add appropriate class based on selection
        switch(value) {
            case 'Disponible':
                $(this).addClass('status-disponible');
                break;
            case 'Occupé':
                $(this).addClass('status-occupe');
                break;
            case 'Maintenance':
                $(this).addClass('status-maintenance');
                break;
            case 'Réservé':
                $(this).addClass('status-reserve');
                break;
            case 'Hors Service':
                $(this).addClass('status-hors-service');
                break;
            case 'En Rénovation':
                $(this).addClass('status-renovation');
                break;
        }

        // Show/hide notes field based on status
        const notesField = $('#notes_statut').closest('.form-group');
        if (['Maintenance', 'Hors Service', 'En Rénovation'].includes(value)) {
            notesField.show();
            $('#notes_statut').attr('placeholder', `Détails sur ${value.toLowerCase()}...`);
        } else if (value === '') {
            notesField.show();
        } else {
            notesField.show(); // Always show for now, but could be hidden for simple statuses
        }
    });

    // Initialize status styling on page load
    $('#statut').trigger('change');

    // Session search functionality
    let sessionOptions = [];
    $('.session-option').each(function() {
        sessionOptions.push({
            id: $(this).data('id'),
            numero: $(this).data('numero'),
            description: $(this).data('description'),
            dateDebut: $(this).data('date-debut'),
            dateFin: $(this).data('date-fin'),
            etat: $(this).data('etat'),
            element: $(this)
        });
    });

    $('#session_search').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        const dropdown = $('#session_dropdown');

        if (searchTerm.length === 0) {
            dropdown.hide();
            $('#session_id').val('');
            return;
        }

        // Filter options
        let hasResults = false;
        $('.session-option').each(function() {
            const numero = $(this).data('numero').toLowerCase();
            const description = $(this).data('description') ? $(this).data('description').toLowerCase() : '';
            const etat = $(this).data('etat') ? $(this).data('etat').toLowerCase() : '';

            if (numero.includes(searchTerm) || description.includes(searchTerm) || etat.includes(searchTerm)) {
                $(this).show();
                hasResults = true;
            } else {
                $(this).hide();
            }
        });

        if (hasResults) {
            dropdown.show();
        } else {
            dropdown.hide();
        }
    });

    // Handle session selection
    $(document).on('click', '.session-option', function(e) {
        e.preventDefault();
        const id = $(this).data('id');
        const numero = $(this).data('numero');
        const description = $(this).data('description');
        const etat = $(this).data('etat');

        $('#session_search').val(`${numero} - ${description} (${etat})`);
        $('#session_id').val(id);
        $('#session_dropdown').hide();

        // Show success feedback
        $('#session_search').removeClass('is-invalid').addClass('is-valid');
    });

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.position-relative').length) {
            $('#session_dropdown').hide();
        }
    });

    // Show all options when focusing on empty search
    $('#session_search').on('focus', function() {
        if ($(this).val() === '') {
            $('.session-option').show();
            $('#session_dropdown').show();
        }
    });

    // Clear selection when search is cleared
    $('#session_search').on('keyup', function() {
        if ($(this).val() === '') {
            $('#session_id').val('');
            $(this).removeClass('is-valid is-invalid');
        }
    });
});

function confirmDelete() {
    $('#deleteModal').modal('show');
}

function deleteBungalow() {
    $('#deleteForm').submit();
}
</script>
{% endblock %}
