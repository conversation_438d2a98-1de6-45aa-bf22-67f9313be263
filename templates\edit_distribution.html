{% extends "base.html" %}

{% block title %}Modifier la Distribution {{ distribution.numero }} - BANGHALAU{% endblock %}

{% block extra_css %}
<style>
/* خطوط عربية محسنة لتعديل التوزيعات */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* تحسين حقول النماذج */
.form-control, .form-select {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.5;
    direction: auto;
    text-align: start;
    font-size: 1rem;
}

/* تحسين التسميات */
.form-label {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين النصوص المساعدة */
.form-text {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين العناوين */
.card-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

/* تحسين التنبيهات */
.alert {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين الأزرار */
.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين رسائل التحقق */
.invalid-feedback, .valid-feedback {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    .form-control, .form-select {
        font-size: 0.95rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gradient">
                        <i class="fas fa-edit me-2"></i>Modifier la Distribution {{ distribution.numero }}
                    </h1>
                    <p class="text-muted mb-0">Modifiez les informations de la distribution</p>
                </div>
                <a href="{{ url_for('distributions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                </a>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card-modern">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2 text-primary"></i>
                                    Informations de la Distribution
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="numero" class="form-label">
                                                <i class="fas fa-hashtag me-1"></i>Numéro de Distribution *
                                            </label>
                                            <input type="text"
                                                   class="form-control"
                                                   id="numero"
                                                   name="numero"
                                                   value="{{ distribution.numero }}"
                                                   placeholder="Ex: DIST001"
                                                   required>
                                            <div class="invalid-feedback">
                                                Veuillez saisir un numéro de distribution.
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="bungalow_id" class="form-label">
                                                <i class="fas fa-home me-1"></i>Bungalow *
                                            </label>
                                            <select class="form-control" id="bungalow_id" name="bungalow_id" required>
                                                <option value="">Sélectionnez un bungalow</option>
                                                {% for bungalow in bungalows %}
                                                    <option value="{{ bungalow.id }}" {% if bungalow.id == distribution.bungalow_id %}selected{% endif %}>
                                                        {{ bungalow.numero }} - {{ bungalow.endroit }} ({{ bungalow.capacite }} pers.)
                                                    </option>
                                                {% endfor %}
                                            </select>
                                            <div class="invalid-feedback">
                                                Veuillez sélectionner un bungalow.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="personnel_id" class="form-label">
                                                <i class="fas fa-user me-1"></i>Personnel Militaire
                                            </label>
                                            <select class="form-control" id="personnel_id" name="personnel_id">
                                                <option value="">Sélectionnez un personnel</option>
                                                {% for person in personnel %}
                                                    <option value="{{ person.id }}" {% if person.id == distribution.personnel_id %}selected{% endif %}>
                                                        {{ person.nom }} {{ person.prenom }}
                                                    </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="session_id" class="form-label">
                                                <i class="fas fa-calendar me-1"></i>Session
                                            </label>
                                            <select class="form-control" id="session_id" name="session_id">
                                                <option value="">Sélectionnez une session</option>
                                                {% for session in sessions %}
                                                    <option value="{{ session.id }}" {% if session.id == distribution.session_id %}selected{% endif %}>
                                                        {{ session.numero }} - {{ session.description or 'Sans description' }}
                                                    </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="date_debut" class="form-label">
                                                <i class="fas fa-calendar-alt me-1"></i>Date de Début *
                                            </label>
                                            <input type="date"
                                                   class="form-control"
                                                   id="date_debut"
                                                   name="date_debut"
                                                   value="{{ distribution.date_debut }}"
                                                   required>
                                            <div class="invalid-feedback">
                                                Veuillez saisir une date de début.
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="date_fin" class="form-label">
                                                <i class="fas fa-calendar-alt me-1"></i>Date de Fin
                                            </label>
                                            <input type="date"
                                                   class="form-control"
                                                   id="date_fin"
                                                   name="date_fin"
                                                   value="{{ distribution.date_fin or '' }}">
                                            <small class="form-text text-muted">
                                                Laissez vide pour une durée indéterminée
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="notes" class="form-label">
                                                <i class="fas fa-sticky-note me-1"></i>Notes
                                            </label>
                                            <textarea class="form-control"
                                                      id="notes"
                                                      name="notes"
                                                      rows="3"
                                                      placeholder="Ajoutez des notes ou commentaires...">{{ distribution.notes or '' }}</textarea>
                                            <small class="form-text text-muted">
                                                Notes additionnelles sur cette distribution (optionnel)
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card-modern">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info me-2 text-info"></i>
                                    Informations Actuelles
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-hashtag me-2"></i>{{ distribution.numero }}</h6>
                                    {% if distribution.bungalow_numero %}
                                    <p class="mb-1"><strong>Bungalow:</strong> {{ distribution.bungalow_numero }}</p>
                                    {% endif %}
                                    {% if distribution.personnel_nom %}
                                    <p class="mb-1"><strong>Personnel:</strong> {{ distribution.personnel_nom }} {{ distribution.personnel_prenom or '' }}</p>
                                    {% endif %}
                                    {% if distribution.date_debut %}
                                    <p class="mb-1"><strong>Début:</strong> {{ distribution.date_debut }}</p>
                                    {% endif %}
                                    {% if distribution.date_fin %}
                                    <p class="mb-0"><strong>Fin:</strong> {{ distribution.date_fin }}</p>
                                    {% else %}
                                    <p class="mb-0"><strong>Fin:</strong> Indéterminée</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="fas fa-trash me-1"></i>Supprimer cette Distribution
                            </button>

                            <div class="d-flex gap-2">
                                <a href="{{ url_for('distributions') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Enregistrer les Modifications
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Delete Form (hidden) -->
            <form id="deleteForm" method="POST" action="{{ url_for('delete_distribution', distribution_id=distribution.id) }}" style="display: none;">
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Confirmer la Suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la distribution <strong>{{ distribution.numero }}</strong> ?</p>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    Cette action est irréversible et supprimera toutes les données associées.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" onclick="deleteDistribution()">
                    <i class="fas fa-trash me-1"></i>Supprimer Définitivement
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Date validation
    $('#date_debut, #date_fin').change(function() {
        const startDate = $('#date_debut').val();
        const endDate = $('#date_fin').val();

        if (startDate && endDate && endDate < startDate) {
            $('#date_fin')[0].setCustomValidity('La date de fin doit être postérieure à la date de début');
        } else {
            $('#date_fin')[0].setCustomValidity('');
        }
    });

    // Auto-generate numero if empty
    $('#bungalow_id, #date_debut').change(function() {
        const numero = $('#numero').val();
        if (!numero) {
            const bungalowText = $('#bungalow_id option:selected').text();
            const dateDebut = $('#date_debut').val();

            if (bungalowText && dateDebut) {
                const bungalowNum = bungalowText.split(' ')[0];
                const dateFormatted = dateDebut.replace(/-/g, '');
                const suggestedNumero = `DIST-${bungalowNum}-${dateFormatted}`;
                $('#numero').val(suggestedNumero);
            }
        }
    });

    // Show bungalow details when selected
    $('#bungalow_id').change(function() {
        const selectedOption = $(this).find('option:selected');
        if (selectedOption.val()) {
            const text = selectedOption.text();
            console.log('Bungalow sélectionné:', text);
        }
    });

    // Show personnel details when selected
    $('#personnel_id').change(function() {
        const selectedOption = $(this).find('option:selected');
        if (selectedOption.val()) {
            const text = selectedOption.text();
            console.log('Personnel sélectionné:', text);
        }
    });
});

function confirmDelete() {
    $('#deleteModal').modal('show');
}

function deleteDistribution() {
    $('#deleteForm').submit();
}
</script>
{% endblock %}
