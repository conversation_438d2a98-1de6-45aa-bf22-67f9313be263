{% extends "base.html" %}

{% block title %}Modifier le Grade {{ grade.grade }} - BANGHALAU{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="card-title">
                            <i class="fas fa-edit me-2 text-warning"></i>
                            Modifier le Grade "{{ grade.grade }}"
                        </h2>
                        <a href="{{ url_for('grades') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="grade" class="form-label">
                                    <i class="fas fa-star me-1"></i>Nom du Grade *
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="grade"
                                       name="grade"
                                       value="{{ grade.grade }}"
                                       placeholder="Ex: Capitaine, Sergent, Colonel..."
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir le nom du grade.
                                </div>
                                <small class="form-text text-muted">
                                    Nom officiel du grade militaire
                                </small>
                            </div>
                        </div>


                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>Description
                                </label>
                                <textarea class="form-control"
                                          id="description"
                                          name="description"
                                          rows="3"
                                          placeholder="Description détaillée du grade, responsabilités, etc.">{{ grade.description or '' }}</textarea>
                                <small class="form-text text-muted">
                                    Description optionnelle du grade et de ses responsabilités
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Informations sur l'utilisation -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-info bg-opacity-10 border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Informations sur le Grade
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-users text-info me-2"></i>
                                                <span>Personnel assigné: <strong id="personnelCount">Chargement...</strong></span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-hashtag text-info me-2"></i>
                                                <span>Numéro: <strong>{{ grade.numero }}</strong></span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-calendar text-info me-2"></i>
                                                <span>Créé le: <strong>{{ grade.created_at or 'Non disponible' }}</strong></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hiérarchie des grades -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-sitemap text-primary me-2"></i>
                                        Hiérarchie des Grades Militaires
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-2">
                                            <div class="hierarchy-level level-1">
                                                <h6 class="text-secondary">
                                                    <i class="fas fa-user me-1"></i>Niveau 1
                                                </h6>
                                                <small>Soldats et Caporaux</small>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="hierarchy-level level-2">
                                                <h6 class="text-info">
                                                    <i class="fas fa-user-tie me-1"></i>Niveau 2
                                                </h6>
                                                <small>Sous-officiers</small>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="hierarchy-level level-3">
                                                <h6 class="text-primary">
                                                    <i class="fas fa-user-graduate me-1"></i>Niveau 3
                                                </h6>
                                                <small>Officiers subalternes</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="hierarchy-level level-4">
                                                <h6 class="text-warning">
                                                    <i class="fas fa-user-shield me-1"></i>Niveau 4
                                                </h6>
                                                <small>Officiers supérieurs</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="hierarchy-level level-5">
                                                <h6 class="text-danger">
                                                    <i class="fas fa-crown me-1"></i>Niveau 5
                                                </h6>
                                                <small>Officiers généraux</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                    <i class="fas fa-trash me-1"></i>Supprimer ce Grade
                                </button>

                                <div class="d-flex gap-2">
                                    <a href="{{ url_for('grades') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>Annuler
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Enregistrer les Modifications
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Delete Form (hidden) -->
                <form id="deleteForm" method="POST" action="{{ url_for('delete_grade', grade_id=grade.id) }}" style="display: none;">
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Confirmer la Suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le grade <strong>"{{ grade.grade }}"</strong> ?</p>
                <div id="personnelWarning" class="alert alert-warning" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Attention :</strong> Ce grade est assigné à du personnel.
                    Vous devez d'abord réassigner ou supprimer ce personnel.
                </div>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    Cette action est irréversible et supprimera toutes les données associées.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>Supprimer Définitivement
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.hierarchy-level {
    text-align: center;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.hierarchy-level:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.level-1 { background: rgba(108, 117, 125, 0.1); }
.level-2 { background: rgba(13, 202, 240, 0.1); }
.level-3 { background: rgba(13, 110, 253, 0.1); }
.level-4 { background: rgba(255, 193, 7, 0.1); }
.level-5 { background: rgba(220, 53, 69, 0.1); }

{% if grade.niveau %}
.level-{{ grade.niveau }} {
    border: 2px solid;
    {% if grade.niveau == 1 %}border-color: #6c757d;{% endif %}
    {% if grade.niveau == 2 %}border-color: #0dcaf0;{% endif %}
    {% if grade.niveau == 3 %}border-color: #0d6efd;{% endif %}
    {% if grade.niveau == 4 %}border-color: #ffc107;{% endif %}
    {% if grade.niveau == 5 %}border-color: #dc3545;{% endif %}
}
{% endif %}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Real-time validation feedback
    $('input[required], textarea[required]').on('input', function() {
        if ($(this).val().trim() !== '') {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });

    // Load personnel count
    loadPersonnelCount();

    // Character counter for description
    $('#description').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;

        if (!$('#charCounter').length) {
            $(this).after(`<small id="charCounter" class="form-text text-muted"></small>`);
        }

        $('#charCounter').text(`${currentLength} caractères`);

        if (currentLength > maxLength * 0.9) {
            $('#charCounter').removeClass('text-muted').addClass('text-warning');
        } else {
            $('#charCounter').removeClass('text-warning').addClass('text-muted');
        }
    });

    // Grade name formatting
    $('#grade').on('input', function() {
        let value = $(this).val();
        // Capitalize first letter of each word
        value = value.replace(/\b\w/g, l => l.toUpperCase());
        $(this).val(value);
    });
});

function loadPersonnelCount() {
    $.get(`/api/grades/{{ grade.id }}/personnel`)
        .done(function(data) {
            $('#personnelCount').text(`${data.count} personne(s)`);

            // Update delete button state
            if (data.count > 0) {
                $('#confirmDeleteBtn').prop('disabled', true).addClass('disabled');
                $('#personnelWarning').show();
            } else {
                $('#confirmDeleteBtn').prop('disabled', false).removeClass('disabled');
                $('#personnelWarning').hide();
            }
        })
        .fail(function() {
            $('#personnelCount').text('Erreur de chargement');
        });
}

function confirmDelete() {
    $('#deleteModal').modal('show');
}

function deleteGrade() {
    if (!$('#confirmDeleteBtn').prop('disabled')) {
        $('#deleteForm').submit();
    }
}

$('#confirmDeleteBtn').on('click', function() {
    deleteGrade();
});
</script>
{% endblock %}
