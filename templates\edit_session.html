{% extends "base.html" %}

{% block title %}Modifier la Session {{ session.numero }} - BANGHALAU{% endblock %}

{% block extra_css %}
<!-- استيراد الخطوط العربية المحسنة -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
/* خطوط عربية محسنة لصفحة تعديل Session */
:root {
    --arabic-font-primary: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --mixed-font: 'Cairo', 'Noto Sans Arabic', 'Inter', 'Segoe UI', sans-serif;
}

/* تحسين عام للنصوص */
body, .card, .btn, .form-control, .form-select {
    font-family: var(--mixed-font);
    font-feature-settings: 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* العناوين */
.card-title, h1, h2, h3, h4, h5, h6 {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    line-height: 1.3;
}

/* النماذج */
.form-control, .form-select {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
    line-height: 1.5;
}

.form-label {
    font-family: var(--mixed-font);
    font-weight: 500;
}

/* الأزرار */
.btn {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* التنبيهات */
.alert {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.5;
}

/* النصوص العربية */
.arabic-text {
    font-family: var(--arabic-font-primary);
    font-weight: 500;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
}

/* Status option styling */
.status-option {
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.status-option:hover {
    background-color: #f8fafc;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="card-title">
                            <i class="fas fa-edit me-2 text-warning"></i>
                            Modifier la Session "{{ session.numero }}"
                        </h2>
                        <a href="{{ url_for('sessions') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="numero" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>Numéro de Session *
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="numero" 
                                       name="numero" 
                                       value="{{ session.numero }}"
                                       placeholder="Ex: S001, S002, SESSION-2024-01..."
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir le numéro de session.
                                </div>
                                <small class="form-text text-muted">
                                    Numéro unique d'identification de la session
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="etat_session" class="form-label">
                                    <i class="fas fa-chart-pie me-1"></i>Statut de la Session
                                </label>
                                <select class="form-control" id="etat_session" name="etat_session">
                                    <option value="">Sélectionnez un statut</option>
                                    <option value="Planifiée" {% if session.etat_session == 'Planifiée' %}selected{% endif %}>Planifiée</option>
                                    <option value="En cours" {% if session.etat_session == 'En cours' %}selected{% endif %}>En cours</option>
                                    <option value="Terminée" {% if session.etat_session == 'Terminée' %}selected{% endif %}>Terminée</option>
                                    <option value="Annulée" {% if session.etat_session == 'Annulée' %}selected{% endif %}>Annulée</option>
                                </select>
                                <small class="form-text text-muted">
                                    Statut actuel de la session
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>Description *
                                </label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="3"
                                          placeholder="Description détaillée de la session de formation..."
                                          required>{{ session.description or '' }}</textarea>
                                <div class="invalid-feedback">
                                    Veuillez saisir une description.
                                </div>
                                <small class="form-text text-muted">
                                    Description détaillée de la session et de ses objectifs
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_debut" class="form-label">
                                    <i class="fas fa-calendar-start me-1"></i>Date de Début *
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="date_debut" 
                                       name="date_debut" 
                                       value="{{ session.date_debut }}"
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner la date de début.
                                </div>
                                <small class="form-text text-muted">
                                    Date de commencement de la session
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_fin" class="form-label">
                                    <i class="fas fa-calendar-end me-1"></i>Date de Fin
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="date_fin" 
                                       name="date_fin"
                                       value="{{ session.date_fin or '' }}">
                                <small class="form-text text-muted">
                                    Date de fin prévue (optionnel)
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Informations sur l'utilisation -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-info bg-opacity-10 border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Informations sur la Session
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-home text-info me-2"></i>
                                                <span>Distributions: <strong id="distributionCount">Chargement...</strong></span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-chart-pie text-info me-2"></i>
                                                <span>Statut: <strong>{{ session.etat_session or 'Non défini' }}</strong></span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-calendar text-info me-2"></i>
                                                <span>Créée le: <strong>{{ session.created_at or 'Non disponible' }}</strong></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statuts disponibles -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-chart-pie text-primary me-2"></i>
                                        Statuts de Session Disponibles
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="status-option" onclick="setStatus('Planifiée')">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-primary me-2">
                                                        <i class="fas fa-clock"></i>
                                                    </span>
                                                    <div>
                                                        <strong>Planifiée</strong>
                                                        <small class="d-block text-muted">Session programmée</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="status-option" onclick="setStatus('En cours')">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-success me-2">
                                                        <i class="fas fa-play"></i>
                                                    </span>
                                                    <div>
                                                        <strong>En cours</strong>
                                                        <small class="d-block text-muted">Session active</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="status-option" onclick="setStatus('Terminée')">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-secondary me-2">
                                                        <i class="fas fa-check"></i>
                                                    </span>
                                                    <div>
                                                        <strong>Terminée</strong>
                                                        <small class="d-block text-muted">Session complétée</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="status-option" onclick="setStatus('Annulée')">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-danger me-2">
                                                        <i class="fas fa-times"></i>
                                                    </span>
                                                    <div>
                                                        <strong>Annulée</strong>
                                                        <small class="d-block text-muted">Session annulée</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <small class="text-muted">
                                        <i class="fas fa-mouse-pointer me-1"></i>
                                        Cliquez sur un statut pour le sélectionner
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                    <i class="fas fa-trash me-1"></i>Supprimer cette Session
                                </button>
                                
                                <div class="d-flex gap-2">
                                    <a href="{{ url_for('sessions') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>Annuler
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Enregistrer les Modifications
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Delete Form (hidden) -->
                <form id="deleteForm" method="POST" action="{{ url_for('delete_session', session_id=session.id) }}" style="display: none;">
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Confirmer la Suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la session <strong>"{{ session.numero }}"</strong> ?</p>
                <div id="distributionWarning" class="alert alert-warning" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Attention :</strong> Cette session est utilisée dans des distributions. 
                    Vous devez d'abord supprimer ces distributions.
                </div>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    Cette action est irréversible et supprimera toutes les données associées.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>Supprimer Définitivement
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}



{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Real-time validation feedback
    $('input[required], textarea[required]').on('input', function() {
        if ($(this).val().trim() !== '') {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    // Date validation
    $('#date_debut, #date_fin').on('change', function() {
        const dateDebut = $('#date_debut').val();
        const dateFin = $('#date_fin').val();
        
        if (dateDebut && dateFin && dateFin < dateDebut) {
            $('#date_fin')[0].setCustomValidity('La date de fin doit être postérieure à la date de début');
            $('#date_fin').addClass('is-invalid');
        } else {
            $('#date_fin')[0].setCustomValidity('');
            $('#date_fin').removeClass('is-invalid');
        }
    });
    
    // Load distribution count
    loadDistributionCount();
    
    // Character counter for description
    $('#description').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;
        
        if (!$('#charCounter').length) {
            $(this).after(`<small id="charCounter" class="form-text text-muted"></small>`);
        }
        
        $('#charCounter').text(`${currentLength} caractères`);
        
        if (currentLength > maxLength * 0.9) {
            $('#charCounter').removeClass('text-muted').addClass('text-warning');
        } else {
            $('#charCounter').removeClass('text-warning').addClass('text-muted');
        }
    });
    
    // Session number formatting
    $('#numero').on('input', function() {
        let value = $(this).val();
        // Convert to uppercase
        value = value.toUpperCase();
        $(this).val(value);
    });
});

function loadDistributionCount() {
    $.get(`/api/sessions/{{ session.id }}/distributions`)
        .done(function(data) {
            $('#distributionCount').text(`${data.count} distribution(s)`);
            
            // Update delete button state
            if (data.count > 0) {
                $('#confirmDeleteBtn').prop('disabled', true).addClass('disabled');
                $('#distributionWarning').show();
            } else {
                $('#confirmDeleteBtn').prop('disabled', false).removeClass('disabled');
                $('#distributionWarning').hide();
            }
        })
        .fail(function() {
            $('#distributionCount').text('Erreur de chargement');
        });
}

function setStatus(status) {
    $('#etat_session').val(status);
    showToast(`Statut "${status}" sélectionné`, 'success');
}

function confirmDelete() {
    $('#deleteModal').modal('show');
}

function deleteSession() {
    if (!$('#confirmDeleteBtn').prop('disabled')) {
        $('#deleteForm').submit();
    }
}

$('#confirmDeleteBtn').on('click', function() {
    deleteSession();
});

function showToast(message, type = 'info') {
    const toast = $(`
        <div class="toast-notification ${type}">
            <i class="fas fa-check-circle me-2"></i>
            ${message}
        </div>
    `);
    
    $('body').append(toast);
    
    setTimeout(() => {
        toast.fadeOut(() => toast.remove());
    }, 3000);
}
</script>

<style>
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    animation: slideInRight 0.3s ease;
}

.toast-notification.success {
    background: #10b981;
}

.toast-notification.error {
    background: #ef4444;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
{% endblock %}
