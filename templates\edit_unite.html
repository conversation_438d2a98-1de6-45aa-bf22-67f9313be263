{% extends "base.html" %}

{% block title %}Modifier l'Unité {{ unite.numero }} - BANGHALAU{% endblock %}

{% block extra_css %}
<style>
/* خطوط عربية محسنة */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* تحسين حقل الوصف */
#description {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.6;
    direction: auto;
    text-align: start;
    font-size: 1rem;
    resize: vertical;
    min-height: 120px;
}

/* تحسين النماذج العربية */
.form-control.arabic-input {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين التسميات */
.form-label {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين النصوص المساعدة */
.form-text {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين العناوين */
.card-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

/* تحسين التنبيهات */
.alert {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين الأزرار */
.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

/* تحسين النص في البطاقات */
.card-body {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

/* تحسين النص في النوافذ المنبثقة */
.modal-content {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

.modal-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.modal-body {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    #description {
        font-size: 0.95rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="card-title">
                            <i class="fas fa-edit me-2 text-warning"></i>
                            Modifier l'Unité {{ unite.numero }}
                        </h2>
                        <a href="{{ url_for('unites') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="numero" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>Numéro de l'Unité *
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="numero"
                                       name="numero"
                                       value="{{ unite.numero }}"
                                       placeholder="Ex: U001"
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir un numéro d'unité.
                                </div>
                                <small class="form-text text-muted">
                                    Numéro unique pour identifier l'unité
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="raccourci" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Raccourci
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="raccourci"
                                       name="raccourci"
                                       value="{{ unite.raccourci or '' }}"
                                       placeholder="Ex: INF, CAV, ART"
                                       maxlength="10">
                                <small class="form-text text-muted">
                                    Abréviation de l'unité (optionnel, max 10 caractères)
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>Description de l'Unité *
                                </label>
                                <textarea class="form-control arabic-input"
                                          id="description"
                                          name="description"
                                          rows="4"
                                          placeholder="Ex: 1er Régiment d'Infanterie"
                                          required>{{ unite.description }}</textarea>
                                <div class="invalid-feedback">
                                    Veuillez saisir une description de l'unité.
                                </div>
                                <small class="form-text text-muted">
                                    Description complète de l'unité militaire
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Informations sur l'utilisation -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-info bg-opacity-10 border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Informations sur l'Unité
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-users text-info me-2"></i>
                                                <span>Personnel assigné: <strong id="personnelCount">Chargement...</strong></span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-calendar text-info me-2"></i>
                                                <span>Créée le: <strong>{{ unite.created_at or 'Non disponible' }}</strong></span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-edit text-info me-2"></i>
                                                <span>Dernière modification: <strong>{{ unite.updated_at or 'Non disponible' }}</strong></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                    <i class="fas fa-trash me-1"></i>Supprimer cette Unité
                                </button>

                                <div class="d-flex gap-2">
                                    <a href="{{ url_for('unites') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>Annuler
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Enregistrer les Modifications
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Delete Form (hidden) -->
                <form id="deleteForm" method="POST" action="{{ url_for('delete_unite', unite_id=unite.id) }}" style="display: none;">
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Confirmer la Suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer l'unité <strong>{{ unite.description }}</strong> ?</p>
                <div id="personnelWarning" class="alert alert-warning" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Attention :</strong> Cette unité contient du personnel assigné.
                    Vous devez d'abord réassigner ou supprimer ce personnel.
                </div>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    Cette action est irréversible et supprimera toutes les données associées.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>Supprimer Définitivement
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Raccourci validation and formatting
    $('#raccourci').on('input', function() {
        let value = $(this).val().toUpperCase();
        // Remove special characters and numbers
        value = value.replace(/[^A-Z]/g, '');
        // Limit to 10 characters
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        $(this).val(value);
    });

    // Real-time validation feedback
    $('input[required], textarea[required]').on('input', function() {
        if ($(this).val().trim() !== '') {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });

    // Load personnel count
    loadPersonnelCount();

    // Character counter for description
    $('#description').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;

        if (!$('#charCounter').length) {
            $(this).after(`<small id="charCounter" class="form-text text-muted"></small>`);
        }

        $('#charCounter').text(`${currentLength} caractères`);

        if (currentLength > maxLength * 0.9) {
            $('#charCounter').removeClass('text-muted').addClass('text-warning');
        } else {
            $('#charCounter').removeClass('text-warning').addClass('text-muted');
        }
    });
});

function loadPersonnelCount() {
    $.get(`/api/unites/{{ unite.id }}/personnel`)
        .done(function(data) {
            $('#personnelCount').text(`${data.count} personne(s)`);

            // Update delete button state
            if (data.count > 0) {
                $('#confirmDeleteBtn').prop('disabled', true).addClass('disabled');
                $('#personnelWarning').show();
            } else {
                $('#confirmDeleteBtn').prop('disabled', false).removeClass('disabled');
                $('#personnelWarning').hide();
            }
        })
        .fail(function() {
            $('#personnelCount').text('Erreur de chargement');
        });
}

function confirmDelete() {
    $('#deleteModal').modal('show');
}

function deleteUnite() {
    if (!$('#confirmDeleteBtn').prop('disabled')) {
        $('#deleteForm').submit();
    }
}

$('#confirmDeleteBtn').on('click', function() {
    deleteUnite();
});
</script>
{% endblock %}
