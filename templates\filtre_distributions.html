{% extends "base.html" %}

{% block title %}Filtre des Distributions - BANGHALAU{% endblock %}

{% block extra_css %}
<style>
/* Modern Design System */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

:root {
    /* Color Palette */
    --primary: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --secondary: #64748b;
    --accent: #06b6d4;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;

    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 0.75rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}

/* Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
}

/* Modern Components */
.page-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    padding: var(--space-2xl) 0;
    margin-bottom: var(--space-xl);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white" opacity="0.1"/></svg>');
    background-size: 50px 50px;
}

.page-title {
    font-family: 'Space Grotesk', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--space-sm);
    position: relative;
    z-index: 2;
}

.page-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

.card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    padding: var(--space-xl);
    margin-bottom: var(--space-lg);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-md);
    border-bottom: 2px solid var(--gray-100);
}

.card-title {
    font-family: 'Space Grotesk', sans-serif;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.card-title i {
    color: var(--primary);
}

/* Form Controls */
.form-group {
    margin-bottom: var(--space-lg);
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--space-xs);
    font-size: 0.875rem;
}

.form-control {
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-sm) var(--space-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-secondary {
    background: var(--gray-500);
    color: white;
}

.btn-secondary:hover {
    background: var(--gray-600);
    color: white;
}

.btn-success {
    background: var(--success);
    color: white;
}

.btn-success:hover {
    background: #059669;
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
}

.btn-outline-primary:hover {
    background: var(--primary);
    color: white;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.table {
    margin: 0;
    width: 100%;
    border-collapse: collapse;
}

.table thead th {
    background: var(--gray-50);
    color: var(--gray-700);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: var(--space-md);
    border-bottom: 2px solid var(--gray-200);
    text-align: left;
}

.table tbody td {
    padding: var(--space-md);
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
    font-size: 0.875rem;
}

.table tbody tr:hover {
    background: var(--gray-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Column Widths */
.table th:nth-child(1) { width: 40px; }      /* Checkbox */
.table th:nth-child(2) { width: 120px; }     /* Numéro */
.table th:nth-child(3) { width: 180px; }     /* Bungalow */
.table th:nth-child(4) { width: 250px; }     /* Personnel - توسيع */
.table th:nth-child(5) { width: 220px; }     /* Unité - توسيع */
.table th:nth-child(6) { width: 140px; }     /* Session */
.table th:nth-child(7) { width: 160px; }     /* Période */
.table th:nth-child(8) { width: 100px; }     /* Statut */
.table th:nth-child(9) { width: 120px; }     /* Actions */

/* Arabic Text Styling */
.arabic-text,
.personnel-name,
.personnel-grade,
.bungalow-location,
.unit-name {
    font-family: 'Cairo', 'IBM Plex Sans Arabic', 'Tajawal', sans-serif;
    font-weight: 500;
    line-height: 1.6;
    direction: rtl;
    text-align: right;
    font-size: 1rem;
    letter-spacing: 0.3px;
}

.personnel-name {
    font-family: 'Cairo', 'IBM Plex Sans Arabic', sans-serif;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 4px;
    font-size: 1.1rem;
    line-height: 1.5;
}

.personnel-grade {
    font-family: 'Cairo', 'IBM Plex Sans Arabic', sans-serif;
    font-size: 0.9rem;
    color: var(--gray-600);
    font-weight: 600;
    line-height: 1.4;
}

.bungalow-info {
    font-family: 'Cairo', 'IBM Plex Sans Arabic', sans-serif;
    font-weight: 700;
}

.bungalow-numero {
    font-weight: 900;
    color: var(--gray-800);
    font-size: 1.1rem;
    display: block;
    margin-bottom: 4px;
    letter-spacing: 0.5px;
}

.bungalow-location {
    font-family: 'Cairo', 'IBM Plex Sans Arabic', sans-serif;
    font-size: 0.9rem;
    color: var(--gray-700);
    font-weight: 800;
    line-height: 1.4;
    display: block;
    letter-spacing: 0.3px;
}

.unit-name {
    font-family: 'Cairo', 'IBM Plex Sans Arabic', sans-serif;
    font-weight: 800;
    color: var(--primary);
    font-size: 1rem;
    line-height: 1.5;
    padding: 6px 12px;
    background: rgba(99, 102, 241, 0.1);
    border-radius: var(--radius-md);
    display: inline-block;
    min-width: 180px;
    text-align: center;
    border: 1px solid rgba(99, 102, 241, 0.2);
}

/* Status Badges */
.badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-success {
    background: #dcfce7;
    color: #166534;
}

.badge-warning {
    background: #fef3c7;
    color: #92400e;
}

.badge-danger {
    background: #fee2e2;
    color: #991b1b;
}

.badge-info {
    background: #dbeafe;
    color: #1e40af;
}

.badge-secondary {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.align-items-center { align-items: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-center { justify-content: center; }
.gap-2 { gap: var(--space-sm); }
.gap-3 { gap: var(--space-md); }

.mb-0 { margin-bottom: 0; }
.mb-2 { margin-bottom: var(--space-sm); }
.mb-3 { margin-bottom: var(--space-md); }
.mb-4 { margin-bottom: var(--space-lg); }

.fw-bold { font-weight: 700; }
.fw-semibold { font-weight: 600; }

.text-primary { color: var(--primary); }
.text-secondary { color: var(--secondary); }
.text-muted { color: var(--gray-400); }

/* Responsive */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }

    .card {
        padding: var(--space-md);
    }

    .d-flex {
        flex-direction: column;
    }

    .gap-2, .gap-3 {
        gap: var(--space-sm);
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .page-header {
        background: none !important;
        color: black !important;
        box-shadow: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}



/* Status badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-active {
    background-color: #dcfce7;
    color: #166534;
}

.status-expired {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-upcoming {
    background-color: #fef3c7;
    color: #92400e;
}

/* Summary cards */
.summary-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.1);
    text-align: center;
}

.summary-number {
    font-size: 2rem;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 5px;
}

.summary-label {
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
}

/* Action buttons */
.action-btn {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: translateY(-1px);
    text-decoration: none;
}

.btn-view {
    background-color: #3b82f6;
    color: white;
}

.btn-view:hover {
    background-color: #2563eb;
    color: white;
}

.btn-print {
    background-color: #10b981;
    color: white;
}

.btn-print:hover {
    background-color: #059669;
    color: white;
}

.btn-edit {
    background-color: #f59e0b;
    color: white;
}

.btn-edit:hover {
    background-color: #d97706;
    color: white;
}



/* Responsive design - matching Liste des Distributions */
@media (max-width: 768px) {
    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    #statusFilter, #uniteFilter, #searchInput {
        width: 100% !important;
        margin-bottom: 0.5rem;
    }

    .position-relative {
        width: 100% !important;
    }

    #uniteDropdown {
        min-width: 100% !important;
    }

    #uniteDropdown .dropdown-item {
        min-height: 50px;
        padding: 0.75rem 1rem;
    }

    #uniteDropdown .dropdown-item .fw-bold,
    #uniteDropdown .dropdown-item .fw-semibold {
        font-size: 0.9rem;
    }

    #uniteDropdown .dropdown-item small {
        font-size: 0.75rem;
    }

    .personnel-name, .bungalow-location, .personnel-grade {
        font-size: 0.9rem;
    }

    .filter-section {
        padding: 15px;
    }

    .table-modern {
        font-size: 12px;
    }

    .table-modern thead th,
    .table-modern tbody td {
        padding: 8px 6px;
    }

    .action-btn {
        padding: 4px 8px;
        font-size: 11px;
    }
}

/* Unite dropdown styles - matching Liste des Distributions */
#uniteFilter {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m11.25 11.25 3 3m-3-3a6 6 0 1 1-12 0 6 6 0 0 1 12 0Z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

#uniteFilter:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    background-color: #fff;
}

#uniteDropdown {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    background: white;
}

#uniteDropdown .dropdown-item {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    min-height: 60px;
    display: flex;
    align-items: center;
}

#uniteDropdown .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

#uniteDropdown .dropdown-item:last-child {
    border-bottom: none;
}

#uniteDropdown .dropdown-item i {
    color: #6c757d;
    width: 1.2rem;
    font-size: 1.1rem;
    width: 1.5rem;
    text-align: center;
}

#uniteDropdown .dropdown-item:hover i,
#uniteDropdown .dropdown-item.active i {
    color: #0d6efd;
}

#uniteDropdown .dropdown-item.active {
    background-color: #e7f3ff;
    border-left: 4px solid #0d6efd;
    box-shadow: inset 0 0 0 1px rgba(13, 110, 253, 0.2);
}

#uniteDropdown .dropdown-item .fw-bold,
#uniteDropdown .dropdown-item .fw-semibold {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    color: #2c3e50;
    font-size: 0.95rem;
}

#uniteDropdown .dropdown-item small {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    opacity: 0.7;
}

#uniteDropdown .dropdown-item .badge {
    font-size: 0.7rem;
    padding: 0.25em 0.5em;
    border-radius: 0.375rem;
}

#uniteDropdown .dropdown-item:hover .fw-bold,
#uniteDropdown .dropdown-item:hover .fw-semibold,
#uniteDropdown .dropdown-item.active .fw-bold,
#uniteDropdown .dropdown-item.active .fw-semibold {
    color: #0d6efd;
}

#uniteDropdown .dropdown-item:hover .badge,
#uniteDropdown .dropdown-item.active .badge {
    background-color: #0d6efd !important;
    color: white !important;
}

#uniteDropdown .dropdown-divider {
    margin: 0.25rem 0;
    border-top: 1px solid #e9ecef;
}

#noResultsMessage {
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    margin: 0.5rem;
    border: 2px dashed #dee2e6;
    min-height: 80px;
}

#noResultsMessage .fw-semibold {
    color: #495057;
    font-size: 0.9rem;
}

#noResultsMessage small {
    color: #6c757d;
    font-size: 0.8rem;
}

#uniteFilter.is-valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.38 1.38'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23198754' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m11.25 11.25 3 3m-3-3a6 6 0 1 1-12 0 6 6 0 0 1 12 0Z'/%3e%3c/svg%3e");
    background-position: right calc(0.375em + 0.1875rem) center, right 0.75rem center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem), 1rem;
}

.form-control.is-valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.38 1.38 3.22-3.22.94.94-4.16 4.16z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* User avatar styles */
.user-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 500;
}

/* Row selection styles - matching Liste des Distributions */
.table-modern tbody tr.selected {
    background-color: rgba(44, 85, 48, 0.1) !important;
    border-left: 3px solid #2c5530;
}

.table-modern tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

/* Checkbox styles - matching Liste des Distributions */
.form-check-input {
    border: 2px solid #2c5530;
    border-radius: 4px;
    width: 18px;
    height: 18px;
}

.form-check-input:checked {
    background-color: #2c5530;
    border-color: #2c5530;
}

.form-check-input:focus {
    border-color: #2c5530;
    box-shadow: 0 0 0 0.25rem rgba(44, 85, 48, 0.25);
}

.form-check-input:indeterminate {
    background-color: #6c757d;
    border-color: #6c757d;
}

.form-check-input:hover {
    border-color: #3a6b3e;
    cursor: pointer;
}

/* Button group improvements */
.btn-group .btn {
    border-radius: 0.375rem !important;
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Badge improvements */
.badge {
    font-size: 0.75em;
    padding: 0.35em 0.65em;
}

/* Bulk actions styling - matching Liste des Distributions */
#bulkActionsGroup {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Checkbox column width */
th:first-child,
td:first-child {
    width: 40px;
    text-align: center;
}

/* Bulk actions buttons styling */
#bulkActionsGroup .btn {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

#bulkActionsGroup .btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

#bulkActionsGroup .btn-warning:hover {
    background-color: #ffca2c;
    border-color: #ffc720;
}

#bulkActionsGroup .btn-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

#bulkActionsGroup .btn-info:hover {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
}

/* Print styles */
@media print {
    .filter-section,
    .no-print {
        display: none !important;
    }

    .table-responsive {
        box-shadow: none;
        border: 1px solid #000;
    }

    .table-modern thead th {
        background: #f8f9fa !important;
        color: #000 !important;
        border: 1px solid #000;
    }

    .table-modern tbody td {
        border: 1px solid #000;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Modern Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="page-title">
                        <i class="fas fa-filter"></i>
                        Filtre des Distributions
                    </h1>
                    <p class="page-subtitle mb-0">
                        Recherchez et filtrez les distributions selon vos critères
                    </p>
                </div>
                <div class="d-flex gap-3 no-print">
                    <button onclick="printFilteredDistributions()" class="btn btn-success">
                        <i class="fas fa-file-pdf"></i>
                        Imprimer PDF
                    </button>
                    <button onclick="printSelectedDistributions()" class="btn btn-outline-primary" id="printSelectedBtn" disabled>
                        <i class="fas fa-print"></i>
                        Sélectionnés
                    </button>
                    <a href="{{ url_for('distributions') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Retour
                    </a>
                </div>
            </div>
        </div>
    </div>



    <!-- Modern Filters Section -->
    <div class="card no-print">
        <div class="card-header">
            <h2 class="card-title">
                <i class="fas fa-search"></i>
                Filtres de Recherche
            </h2>
        </div>
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="regionFilter" class="form-label">
                        <i class="fas fa-map-marker-alt me-1"></i>Région Militaire
                    </label>
                    <select class="form-select" id="regionFilter" name="region">
                        <option value="">Toutes les régions</option>
                        {% for region in regions %}
                        <option value="{{ region }}">{{ region }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="uniteFilter" class="form-label">
                        <i class="fas fa-flag me-1"></i>Unité Militaire
                    </label>
                    <div class="position-relative">
                        <input type="text"
                               class="form-control"
                               id="uniteFilter"
                               placeholder="Rechercher une unité..."
                               autocomplete="off">
                        <input type="hidden" id="selectedUniteValue" name="selected_unite_value">
                        <div class="position-absolute top-100 start-0 w-100" style="z-index: 1000;">
                            <div id="uniteDropdown" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto; display: none; min-width: 350px;">
                                <a class="dropdown-item unite-option" href="#" data-value="" data-id="">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-list me-3 text-primary"></i>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold">Toutes les unités</div>
                                            <small class="text-muted">Afficher toutes les distributions</small>
                                        </div>
                                        <span class="badge bg-primary">{{ unites|length }} unité(s)</span>
                                    </div>
                                </a>
                                <div class="dropdown-divider"></div>
                                {% for unite in unites %}
                                <a class="dropdown-item unite-option"
                                   href="#"
                                   data-value="{{ unite.description }}"
                                   data-id="{{ unite.id }}"
                                   title="Cliquez pour filtrer par {{ unite.description }}"
                                   data-bs-toggle="tooltip">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-shield-alt me-3 text-secondary"></i>
                                        <div class="flex-grow-1">
                                            <div class="fw-semibold">{{ unite.description }}</div>
                                            <small class="text-muted">Unité militaire</small>
                                        </div>
                                        <span class="badge bg-secondary">ID: {{ unite.id }}</span>
                                    </div>
                                </a>
                                {% endfor %}
                                <div class="dropdown-divider" id="noResultsDivider" style="display: none;"></div>
                                <div class="dropdown-item text-muted text-center" id="noResultsMessage" style="display: none;">
                                    <div class="py-3">
                                        <i class="fas fa-search fa-2x mb-2 text-secondary"></i>
                                        <div class="fw-semibold">Aucune unité trouvée</div>
                                        <small>Essayez un autre terme de recherche</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">
                        <i class="fas fa-info-circle me-1"></i>Statut
                    </label>
                    <select class="form-select" id="statusFilter" name="status">
                        <option value="">Tous les statuts</option>
                        <option value="active">Actives</option>
                        <option value="expired">Expirées</option>
                        <option value="upcoming">À venir</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="searchInput" class="form-label">
                        <i class="fas fa-search me-1"></i>Recherche
                    </label>
                    <input type="text" class="form-control" id="searchInput" placeholder="Nom, numéro, bungalow...">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" onclick="applyFilters()" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>Filtrer
                    </button>
                    <button type="button" onclick="resetFilters()" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>Réinitialiser
                    </button>
                </div>
            </div>
        </form>
    </div>



    <!-- Modern Table Container -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-table"></i>
                Résultats des Distributions
            </h3>
            <div class="badge badge-info">
                <i class="fas fa-list"></i>
                <span id="resultsCount">0</span> résultat(s)
            </div>
        </div>
        <div class="table-container">
            <table class="table" id="distributionsTable">
                <thead>
                    <tr>
                        <th style="width: 40px;">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                <label class="form-check-label" for="selectAll"></label>
                            </div>
                        </th>
                        <th><i class="fas fa-hashtag"></i> Numéro</th>
                        <th><i class="fas fa-home"></i> Bungalow</th>
                        <th><i class="fas fa-user"></i> Personnel</th>
                        <th><i class="fas fa-shield-alt"></i> Unité</th>
                        <th><i class="fas fa-calendar"></i> Session</th>
                        <th><i class="fas fa-calendar-alt"></i> Période</th>
                        <th><i class="fas fa-info-circle"></i> Statut</th>
                        <th><i class="fas fa-cogs"></i> Actions</th>
                    </tr>
                </thead>
                <tbody id="distributionsTableBody">
                    <!-- Data will be populated here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allDistributions = [];
let filteredDistributions = [];
let currentDate = new Date().toISOString().split('T')[0];

$(document).ready(function() {
    loadDistributions();

    // Initialize tooltips for unite options
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Unite search functionality
    let uniteOptions = [];
    $('.unite-option').each(function() {
        uniteOptions.push({
            value: $(this).data('value'),
            id: $(this).data('id'),
            text: $(this).text().trim(),
            element: $(this)
        });
    });

    $('#uniteFilter').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        const dropdown = $('#uniteDropdown');

        if (searchTerm.length === 0) {
            // Show all options when search is empty
            $('.unite-option').show();
            dropdown.show();
            return;
        }

        // Filter options based on search term
        let hasResults = false;
        $('.unite-option').each(function() {
            const text = $(this).text().toLowerCase();
            const value = $(this).data('value') ? $(this).data('value').toLowerCase() : '';
            const unitName = $(this).find('.fw-semibold, .fw-bold').text().toLowerCase();

            if (text.includes(searchTerm) || value.includes(searchTerm) || unitName.includes(searchTerm)) {
                $(this).show();
                hasResults = true;
            } else {
                $(this).hide();
            }
        });

        // Count visible results
        const visibleCount = $('.unite-option:visible').length;

        // Show/hide no results message
        if (hasResults) {
            $('#noResultsMessage').hide();
            $('#noResultsDivider').hide();
            dropdown.show();

            // Update search placeholder with result count
            if (searchTerm.length > 0) {
                $('#uniteFilter').attr('placeholder', `${visibleCount} unité(s) trouvée(s)`);
            }
        } else {
            $('#noResultsMessage').show();
            $('#noResultsDivider').show();
            dropdown.show();
            $('#uniteFilter').attr('placeholder', 'Aucune unité trouvée');
        }
    });

    // Handle unite selection
    $(document).on('click', '.unite-option', function(e) {
        e.preventDefault();
        const value = $(this).data('value');
        const id = $(this).data('id');
        const text = $(this).text().trim();

        if (value === '' && id === '') {
            $('#uniteFilter').val('');
            $('#selectedUniteValue').val('');
        } else {
            // Extract just the unit name from the complex structure
            const unitName = $(this).find('.fw-semibold').text().trim() || value;
            $('#uniteFilter').val(unitName);
            $('#selectedUniteValue').val(id); // Use ID for filtering
        }

        $('#uniteDropdown').hide();

        // Apply filters immediately
        applyFilters();

        // Show success feedback
        $('#uniteFilter').removeClass('is-invalid').addClass('is-valid');
        setTimeout(() => {
            $('#uniteFilter').removeClass('is-valid');
        }, 2000);
    });

    // Show dropdown when focusing on unite filter
    $('#uniteFilter').on('focus', function() {
        $('.unite-option').show();
        $('#noResultsMessage').hide();
        $('#noResultsDivider').hide();
        $('#uniteDropdown').show();

        // Reset placeholder
        if ($(this).val() === '') {
            $(this).attr('placeholder', 'Rechercher une unité...');
        }
    });

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.position-relative').length) {
            $('#uniteDropdown').hide();
        }
    });

    // Clear selection when search is cleared and handle keyboard navigation
    $('#uniteFilter').on('keyup', function(e) {
        if ($(this).val() === '') {
            $('#selectedUniteValue').val('');
            $(this).removeClass('is-valid is-invalid');
            applyFilters();
        }

        // Handle keyboard navigation
        const dropdown = $('#uniteDropdown');
        const visibleOptions = $('.unite-option:visible');

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            if (visibleOptions.length > 0) {
                const current = $('.unite-option.active');
                current.removeClass('active');
                const next = current.length === 0 ? visibleOptions.first() : current.next('.unite-option:visible');
                if (next.length > 0) {
                    next.addClass('active');
                } else {
                    visibleOptions.first().addClass('active');
                }
            }
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            if (visibleOptions.length > 0) {
                const current = $('.unite-option.active');
                current.removeClass('active');
                const prev = current.length === 0 ? visibleOptions.last() : current.prev('.unite-option:visible');
                if (prev.length > 0) {
                    prev.addClass('active');
                } else {
                    visibleOptions.last().addClass('active');
                }
            }
        } else if (e.key === 'Enter') {
            e.preventDefault();
            const active = $('.unite-option.active');
            if (active.length > 0) {
                active.click();
            }
        } else if (e.key === 'Escape') {
            dropdown.hide();
            $(this).blur();
        }
    });

    // Filter event handlers
    $('#regionFilter, #statusFilter').on('change', function() {
        applyFilters();
    });

    $('#searchInput').on('input', function() {
        applyFilters();
    });

    // Form submission
    $('#filterForm').on('submit', function(e) {
        e.preventDefault();
        applyFilters();
    });
});

// Load distributions data
function loadDistributions() {
    // Convert server data to JavaScript
    allDistributions = {{ distributions|tojson }};

    console.log('Loaded distributions:', allDistributions);

    // Process distributions to add status (region is now set server-side)
    allDistributions.forEach(function(dist) {
        // Determine status
        if (dist.date_fin) {
            const endDate = new Date(dist.date_fin);
            const today = new Date(currentDate);

            if (endDate < today) {
                dist.status = 'expired';
            } else {
                dist.status = 'active';
            }
        } else {
            dist.status = 'upcoming';
        }

        // Region is now set server-side, no need to set it here
        console.log('Distribution:', dist.id, 'Region:', dist.region, 'Unite ID:', dist.unite_id);
    });

    filteredDistributions = allDistributions;
    updateTable();
    updateResultsCount();
}

// Apply filters
function applyFilters() {
    const region = $('#regionFilter').val();
    const unite = $('#selectedUniteValue').val(); // Use the hidden field with ID
    const status = $('#statusFilter').val();
    const search = $('#searchInput').val().toLowerCase();

    console.log('Applying filters:', { region, unite, status, search });

    filteredDistributions = allDistributions.filter(function(dist) {
        // Region filter
        if (region && dist.region !== region) {
            console.log('Region filter failed for dist:', dist.id, 'region:', dist.region, 'filter:', region);
            return false;
        }

        // Unite filter
        if (unite && dist.unite_id != unite) {
            console.log('Unite filter failed for dist:', dist.id, 'unite_id:', dist.unite_id, 'filter:', unite);
            return false;
        }

        // Status filter
        if (status && dist.status !== status) return false;

        // Search filter
        if (search) {
            const searchFields = [
                dist.numero,
                dist.personnel_nom,
                dist.personnel_prenom,
                dist.personnel_matricule,
                dist.personnel_grade,
                dist.unite_description,
                dist.bungalow_numero,
                dist.bungalow_endroit
            ].join(' ').toLowerCase();

            if (!searchFields.includes(search)) return false;
        }

        return true;
    });

    updateTable();
    updateResultsCount();
}

// Update table
function updateTable() {
    const tbody = $('#distributionsTableBody');
    tbody.empty();

    if (filteredDistributions.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-exchange-alt fa-3x mb-3"></i>
                        <h5>Aucune distribution trouvée</h5>
                        <p>Aucune distribution ne correspond aux critères de filtrage.</p>
                    </div>
                </td>
            </tr>
        `);
        return;
    }

    filteredDistributions.forEach(function(dist, index) {
        let statusBadge = '';

        if (!dist.date_debut) {
            statusBadge = '<span class="badge bg-secondary">Non planifiée</span>';
        } else if (dist.date_debut > currentDate) {
            statusBadge = '<span class="badge bg-info">À venir</span>';
        } else if (!dist.date_fin || dist.date_fin >= currentDate) {
            statusBadge = '<span class="badge bg-success">Active</span>';
        } else {
            statusBadge = '<span class="badge bg-danger">Expirée</span>';
        }

        const statusData = dist.date_fin && dist.date_fin < currentDate ? 'expired' :
                          dist.date_debut && dist.date_debut > currentDate ? 'upcoming' : 'active';

        tbody.append(`
            <tr data-status="${statusData}" data-distribution-id="${dist.id}" data-unite-id="${dist.unite_id || ''}">
                <td>
                    <div class="form-check">
                        <input class="form-check-input distribution-checkbox" type="checkbox" value="${dist.id}" id="dist_${dist.id}" onchange="updateSelectAllState()">
                        <label class="form-check-label" for="dist_${dist.id}"></label>
                    </div>
                </td>
                <td>
                    <strong class="text-primary">${dist.numero || '-'}</strong>
                </td>
                <td>
                    <div class="bungalow-info">
                        <strong class="bungalow-numero">${dist.bungalow_numero || 'غير محدد'}</strong>
                        ${dist.bungalow_endroit ? `<strong class="bungalow-location">${dist.bungalow_endroit}</strong>` : ''}
                    </div>
                </td>
                <td style="min-width: 250px; max-width: 300px;">
                    <div class="personnel-info">
                        <div class="personnel-name">${dist.personnel_nom || 'غير محدد'} ${dist.personnel_prenom || ''}</div>
                        ${dist.personnel_grade ? `<div class="personnel-grade">${dist.personnel_grade}</div>` : ''}
                    </div>
                </td>

                <td style="min-width: 220px; max-width: 250px;">
                    ${dist.unite_description ?
                        `<div class="unit-name">${dist.unite_description}</div>` :
                        '<span class="text-muted">غير محددة</span>'
                    }
                </td>
                <td>
                    ${dist.session_numero ?
                        `<span class="badge bg-info"><i class="fas fa-calendar me-1"></i>${dist.session_numero}</span>` :
                        '<span class="text-muted">Non définie</span>'
                    }
                </td>
                <td>
                    <div class="small">
                        ${dist.date_debut ? `<div><i class="fas fa-play text-success me-1"></i>${dist.date_debut}</div>` : ''}
                        ${dist.date_fin ?
                            `<div><i class="fas fa-stop text-danger me-1"></i>${dist.date_fin}</div>` :
                            '<div><i class="fas fa-infinity text-warning me-1"></i>Indéterminée</div>'
                        }
                    </div>
                </td>
                <td>
                    ${statusBadge}
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary"
                                onclick="printSingleDistribution(${dist.id})"
                                title="Imprimer">
                            <i class="fas fa-print"></i>
                        </button>
                        <a href="/distributions/edit/${dist.id}"
                           class="btn btn-sm btn-outline-warning"
                           title="Modifier">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button class="btn btn-sm btn-outline-info"
                                onclick="viewDetails(${dist.id})"
                                title="Voir détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger"
                                onclick="confirmDelete(${dist.id}, '${dist.numero}')"
                                title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
    });
}

// Update results count
function updateResultsCount() {
    const count = filteredDistributions.length;
    $('#resultsCount').text(count);

    // Add animation effect
    $('#resultsCount').parent().addClass('animate__animated animate__pulse');
    setTimeout(() => {
        $('#resultsCount').parent().removeClass('animate__animated animate__pulse');
    }, 1000);
}

// Reset filters
function resetFilters() {
    $('#filterForm')[0].reset();
    filteredDistributions = allDistributions;
    updateTable();
    updateSummary();
    updateBungalowStats();
}

// Print table
function printTable() {
    window.print();
}

// Print filtered distributions using Imprimer-Globale.html
function printFilteredDistributions() {
    // Show loading state
    const btn = $('#printPdfBtn');
    const originalText = btn.html();
    btn.html('<i class="fas fa-spinner fa-spin me-1"></i>Préparation...');
    btn.prop('disabled', true);

    // Get current filter values
    const region = $('#regionFilter').val();
    const unite = $('#selectedUniteValue').val();
    const status = $('#statusFilter').val();
    const search = $('#searchInput').val();

    // Build URL with filters
    let printUrl = '/distributions/print-preview';
    const params = new URLSearchParams();

    if (region) params.append('region', region);
    if (unite) params.append('unite', unite);
    if (status) params.append('status', status);
    if (search) params.append('search', search);

    if (params.toString()) {
        printUrl += '?' + params.toString();
    }

    // Try to open in new tab first
    try {
        const printWindow = window.open(printUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

        if (printWindow) {
            printWindow.focus();

            // Wait for the page to load completely
            printWindow.addEventListener('load', function() {
                setTimeout(() => {
                    printWindow.print();
                }, 2000);
            });

            // Fallback for older browsers
            setTimeout(() => {
                if (printWindow.document.readyState === 'complete') {
                    printWindow.print();
                }
            }, 3000);

        } else {
            // If popup blocked, redirect to the print page
            console.log('Popup blocked, redirecting...');
            window.location.href = printUrl + '&auto_print=true';
        }
    } catch (error) {
        console.error('Error opening print window:', error);
        // Fallback: redirect to print page
        window.location.href = printUrl + '&auto_print=true';
    }

    // Reset button state
    setTimeout(() => {
        btn.html(originalText);
        btn.prop('disabled', false);
    }, 2000);
}

// Print selected distributions
function printSelectedDistributions() {
    const selectedIds = getSelectedDistributionIds();

    if (selectedIds.length === 0) {
        alert('Veuillez sélectionner au moins une distribution à imprimer.');
        return;
    }

    // Show loading state
    const btn = $('#printSelectedBtn');
    const originalText = btn.html();
    btn.html('<i class="fas fa-spinner fa-spin me-1"></i>Préparation...');
    btn.prop('disabled', true);

    // Build URL for bulk print
    const printUrl = `/print/distributions/bulk?ids=${selectedIds.join(',')}`;

    try {
        const printWindow = window.open(printUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

        if (printWindow) {
            printWindow.focus();

            // Wait for the page to load completely
            printWindow.addEventListener('load', function() {
                setTimeout(() => {
                    printWindow.print();
                }, 2000);
            });

            // Fallback for older browsers
            setTimeout(() => {
                if (printWindow.document.readyState === 'complete') {
                    printWindow.print();
                }
            }, 3000);

        } else {
            // If popup blocked, redirect to the print page
            console.log('Popup blocked, redirecting...');
            window.location.href = printUrl + '&auto_print=true';
        }
    } catch (error) {
        console.error('Error opening print window:', error);
        // Fallback: redirect to print page
        window.location.href = printUrl + '&auto_print=true';
    }

    // Reset button state
    setTimeout(() => {
        btn.html(originalText);
        btn.prop('disabled', selectedIds.length === 0);
    }, 2000);
}

// Checkbox functionality
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const distributionCheckboxes = document.querySelectorAll('.distribution-checkbox');

    distributionCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectAllState();
}

function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const distributionCheckboxes = document.querySelectorAll('.distribution-checkbox');
    const checkedCheckboxes = document.querySelectorAll('.distribution-checkbox:checked');

    if (checkedCheckboxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedCheckboxes.length === distributionCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }

    // Update row highlighting
    distributionCheckboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        if (checkbox.checked) {
            row.classList.add('selected');
        } else {
            row.classList.remove('selected');
        }
    });

    // Update print selected button state
    const printSelectedBtn = document.getElementById('printSelectedBtn');
    if (printSelectedBtn) {
        if (checkedCheckboxes.length > 0) {
            printSelectedBtn.disabled = false;
            printSelectedBtn.innerHTML = `<i class="fas fa-print me-1"></i>Imprimer Sélectionnés (${checkedCheckboxes.length})`;
        } else {
            printSelectedBtn.disabled = true;
            printSelectedBtn.innerHTML = '<i class="fas fa-print me-1"></i>Imprimer Sélectionnés';
        }
    }
}

function getSelectedDistributionIds() {
    const checkedCheckboxes = document.querySelectorAll('.distribution-checkbox:checked');
    return Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
}

// Action functions
function printSingleDistribution(distributionId) {
    window.open(`/print/distribution/${distributionId}`, '_blank');
}

function viewDetails(distributionId) {
    window.location.href = `/distributions/view/${distributionId}`;
}

function confirmDelete(distributionId, distributionName) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la distribution "${distributionName}" ?\n\nCette action est irréversible.`)) {
        // Create and submit delete form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/distributions/delete/${distributionId}`;

        // Add CSRF token if needed
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}