{% extends "base.html" %}

{% block title %}Grades Militaires - BANGHALAU{% endblock %}

{% block extra_css %}
<!-- Arabic Font Import -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

<style>
    /* Arabic Font Styling */
    body, .table, .modal, .btn, .form-control, .card {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .arabic-text, .grade-description, .modal-body, .table td {
        font-family: 'Cairo', '<PERSON><PERSON>', Arial, sans-serif;
        font-weight: 500;
        line-height: 1.6;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .page-header h1, .modal-title, .table thead th {
        font-family: 'Cairo', 'Segoe UI', sans-serif;
        font-weight: 600;
    }

    .page-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
    }
    
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-left: 4px solid #f093fb;
        transition: transform 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
    }
    
    .table-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .table-modern {
        border: none;
    }
    
    .table-modern thead th {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }
    
    .table-modern tbody tr {
        transition: all 0.3s ease;
    }
    
    .table-modern tbody tr:hover {
        background-color: rgba(240, 147, 251, 0.1);
        transform: scale(1.01);
    }
    
    .btn-action {
        padding: 0.25rem 0.5rem;
        margin: 0 0.1rem;
        border-radius: 5px;
        font-size: 0.8rem;
    }
    
    .grade-badge {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .personnel-count {
        background: #fff3e0;
        color: #f57c00;
        padding: 0.25rem 0.5rem;
        border-radius: 10px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .search-container {
        position: relative;
        margin-bottom: 1rem;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .grade-hierarchy {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .grade-stars {
        color: #ffd700;
    }

    /* Enhanced Arabic Typography */
    .grade-description {
        font-size: 1rem;
        font-weight: 600;
        color: #2c3e50;
        letter-spacing: 0.3px;
    }

    .arabic-text {
        direction: ltr;
        text-align: left;
        font-feature-settings: "liga" 1, "kern" 1;
    }

    .table td.arabic-text {
        vertical-align: middle;
    }

    /* Improved readability for Arabic content */
    .modal-body.arabic-text p {
        font-size: 1.1rem;
        line-height: 1.8;
    }

    .search-container input::placeholder {
        font-family: 'Cairo', sans-serif;
        font-weight: 400;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-star me-2"></i>Grades Militaires
                    </h1>
                    <p class="mb-0 opacity-75">Gestion des grades militaires et hiérarchie</p>
                </div>
                <div>
                    <a href="{{ url_for('add_grade') }}" class="btn btn-light">
                        <i class="fas fa-plus me-1"></i>Nouveau Grade
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-star fa-2x text-warning"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold h4 mb-0">{{ grades|length }}</div>
                        <div class="text-muted">Total Grades</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users fa-2x text-success"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold h4 mb-0">
                            {% set total_personnel = grades|sum(attribute='personnel_count') %}
                            {{ total_personnel }}
                        </div>
                        <div class="text-muted">Total Personnel</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line fa-2x text-info"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold h4 mb-0">
                            {% if grades|length > 0 %}
                                {{ "%.1f"|format(total_personnel / grades|length) }}
                            {% else %}
                                0
                            {% endif %}
                        </div>
                        <div class="text-muted">Moyenne/Grade</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-medal fa-2x text-primary"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold h4 mb-0">
                            {% set grades_actifs = grades|selectattr('personnel_count', 'greaterthan', 0)|list|length %}
                            {{ grades_actifs }}
                        </div>
                        <div class="text-muted">Grades Actifs</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="search-container">
                <input type="text" class="form-control" id="searchInput" placeholder="Rechercher un grade..." value="{{ search or '' }}">
                <i class="fas fa-search position-absolute top-50 end-0 translate-middle-y me-3"></i>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary" onclick="filterGrades('all')">
                    <i class="fas fa-list me-1"></i>Tous
                </button>
                <button type="button" class="btn btn-outline-success" onclick="filterGrades('active')">
                    <i class="fas fa-check me-1"></i>Actifs
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="filterGrades('empty')">
                    <i class="fas fa-minus me-1"></i>Vides
                </button>
            </div>
        </div>
    </div>

    <!-- Grades Table -->
    <div class="table-container">
        {% if grades %}
        <div class="table-responsive">
            <table class="table table-modern table-hover">
                <thead>
                    <tr>
                        <th>Grade</th>
                        <th>Description</th>
                        <th>Personnel</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="gradesTableBody">
                    {% for grade in grades %}
                    <tr data-personnel-count="{{ grade.personnel_count }}">
                        <td>
                            <div class="grade-hierarchy">
                                <span class="grade-badge">{{ grade.grade }}</span>
                                <div class="grade-stars">
                                    {% for i in range(5) %}
                                        {% if i < (grade.id % 5 + 1) %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="fw-bold arabic-text grade-description">{{ grade.description or 'Aucune description' }}</div>
                            <small class="text-muted">ID: {{ grade.id }}</small>
                        </td>
                        <td>
                            <span class="personnel-count">
                                <i class="fas fa-users me-1"></i>{{ grade.personnel_count }}
                            </span>
                        </td>
                        <td>
                            {% if grade.personnel_count > 0 %}
                                <span class="badge bg-success">Actif</span>
                            {% else %}
                                <span class="badge bg-secondary">Vide</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('edit_grade', grade_id=grade.id) }}" 
                                   class="btn btn-outline-primary btn-action" 
                                   title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-outline-info btn-action" 
                                        onclick="viewGradeDetails({{ grade.id }})"
                                        title="Détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                                {% if grade.personnel_count == 0 %}
                                <button type="button" 
                                        class="btn btn-outline-danger btn-action" 
                                        onclick="deleteGrade({{ grade.id }}, '{{ grade.grade }}')"
                                        title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% else %}
                                <button type="button" 
                                        class="btn btn-outline-secondary btn-action" 
                                        disabled
                                        title="Impossible de supprimer - Personnel assigné">
                                    <i class="fas fa-lock"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-state arabic-text">
            <i class="fas fa-star"></i>
            <h5 class="arabic-text">Aucun grade trouvé</h5>
            <p class="arabic-text">Commencez par ajouter votre premier grade militaire.</p>
            <a href="{{ url_for('add_grade') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Ajouter un Grade
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body arabic-text">
                <p>Êtes-vous sûr de vouloir supprimer le grade <strong id="deleteGradeName" class="arabic-text"></strong> ?</p>
                <p class="text-danger"><small>Cette action est irréversible.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Grade Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails du Grade</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        $('#gradesTableBody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
    
    // If there's a search term, trigger search
    if ($('#searchInput').val()) {
        $('#searchInput').trigger('keyup');
    }
});

function filterGrades(type) {
    const rows = $('#gradesTableBody tr');
    
    // Remove active class from all buttons
    $('.btn-group .btn').removeClass('active');
    
    switch(type) {
        case 'all':
            rows.show();
            $('button[onclick="filterGrades(\'all\')"]').addClass('active');
            break;
        case 'active':
            rows.hide();
            rows.filter('[data-personnel-count!="0"]').show();
            $('button[onclick="filterGrades(\'active\')"]').addClass('active');
            break;
        case 'empty':
            rows.hide();
            rows.filter('[data-personnel-count="0"]').show();
            $('button[onclick="filterGrades(\'empty\')"]').addClass('active');
            break;
    }
}

function deleteGrade(id, name) {
    $('#deleteGradeName').text(name);
    $('#deleteForm').attr('action', `/grades/delete/${id}`);
    $('#deleteModal').modal('show');
}

function viewGradeDetails(id) {
    $('#detailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Chargement...</div>');
    $('#detailsModal').modal('show');
    
    $.get(`/api/grades/${id}`)
        .done(function(data) {
            const html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Informations Générales</h6>
                        <table class="table table-sm">
                            <tr><td><strong>ID:</strong></td><td>${data.id}</td></tr>
                            <tr><td><strong>Grade:</strong></td><td>${data.grade}</td></tr>
                            <tr><td><strong>Description:</strong></td><td>${data.description || 'Non définie'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Statistiques</h6>
                        <div class="text-center">
                            <div class="h2 text-warning" id="personnelCount">-</div>
                            <div class="text-muted">Personnel avec ce grade</div>
                        </div>
                    </div>
                </div>
            `;
            $('#detailsContent').html(html);
            
            // Load personnel count
            $.get(`/api/grades/${id}/personnel`)
                .done(function(personnelData) {
                    $('#personnelCount').text(personnelData.count);
                });
        })
        .fail(function() {
            $('#detailsContent').html('<div class="alert alert-danger">Erreur lors du chargement des détails.</div>');
        });
}

// Initialize filter
filterGrades('all');
</script>
{% endblock %}
