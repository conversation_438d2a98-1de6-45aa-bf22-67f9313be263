{% extends "base.html" %}

{% block title %}Importer des Unités - BANGHALAU{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="card-title">
                            <i class="fas fa-file-excel me-2 text-success"></i>
                            Importer des Unités depuis Excel
                        </h2>
                        <a href="{{ url_for('unites') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'exclamation-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Instructions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card bg-info bg-opacity-10 border-info">
                            <div class="card-body">
                                <h5 class="card-title text-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Instructions d'Importation
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Format du Fichier Excel :</h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>Format : .xlsx ou .xls</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Première ligne : en-têtes de colonnes</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Encodage : UTF-8 recommandé</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Colonnes Requises :</h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-star text-warning me-2"></i><strong>numero</strong> - Numéro de l'unité (obligatoire)</li>
                                            <li><i class="fas fa-star text-warning me-2"></i><strong>description</strong> - Description de l'unité (obligatoire)</li>
                                            <li><i class="fas fa-circle text-muted me-2"></i><strong>raccourci</strong> - Raccourci de l'unité (optionnel)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Example Table -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    Exemple de Structure Excel
                                </h6>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>numero</th>
                                            <th>description</th>
                                            <th>raccourci</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>U001</td>
                                            <td>1er Régiment d'Infanterie</td>
                                            <td>INF</td>
                                        </tr>
                                        <tr>
                                            <td>U002</td>
                                            <td>2ème Escadron de Cavalerie</td>
                                            <td>CAV</td>
                                        </tr>
                                        <tr>
                                            <td>U003</td>
                                            <td>3ème Groupe d'Artillerie</td>
                                            <td>ART</td>
                                        </tr>
                                        <tr>
                                            <td>U004</td>
                                            <td>Bataillon du Génie</td>
                                            <td>GEN</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="card-footer">
                                <button class="btn btn-outline-success btn-sm" onclick="downloadTemplate()">
                                    <i class="fas fa-download me-1"></i>
                                    Télécharger le Modèle Excel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upload Form -->
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="file" class="form-label">
                                    <i class="fas fa-file-excel me-1"></i>
                                    Sélectionner le Fichier Excel *
                                </label>
                                <div class="file-upload-area" id="fileUploadArea">
                                    <input type="file" 
                                           class="form-control" 
                                           id="file" 
                                           name="file" 
                                           accept=".xlsx,.xls"
                                           required>
                                    <div class="file-upload-text">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h5>Glissez-déposez votre fichier Excel ici</h5>
                                        <p class="text-muted">ou cliquez pour sélectionner un fichier</p>
                                        <small class="text-muted">Formats supportés: .xlsx, .xls (Max: 10MB)</small>
                                    </div>
                                </div>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner un fichier Excel.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File Info -->
                    <div class="row" id="fileInfo" style="display: none;">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-file-alt me-2"></i>
                                        Informations du Fichier
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>Nom :</strong> <span id="fileName"></span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Taille :</strong> <span id="fileSize"></span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Type :</strong> <span id="fileType"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Import Options -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-cogs me-2"></i>
                                        Options d'Importation
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="skipDuplicates" checked>
                                                <label class="form-check-label" for="skipDuplicates">
                                                    <i class="fas fa-copy me-1"></i>
                                                    Ignorer les doublons
                                                </label>
                                                <small class="form-text text-muted d-block">
                                                    Les unités avec des numéros existants seront ignorées
                                                </small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="validateData" checked>
                                                <label class="form-check-label" for="validateData">
                                                    <i class="fas fa-shield-alt me-1"></i>
                                                    Validation stricte
                                                </label>
                                                <small class="form-text text-muted d-block">
                                                    Vérifier la validité de toutes les données avant importation
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('unites') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-success" id="importBtn">
                                    <i class="fas fa-upload me-1"></i>Importer les Unités
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.file-upload-area {
    position: relative;
    border: 2px dashed #e2e8f0;
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.file-upload-area:hover {
    border-color: #3b82f6;
    background: #f0f9ff;
}

.file-upload-area.dragover {
    border-color: #10b981;
    background: #f0fdf4;
}

.file-upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-upload-text {
    pointer-events: none;
}

.file-selected {
    border-color: #10b981;
    background: #f0fdf4;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    const fileInput = $('#file');
    const fileUploadArea = $('#fileUploadArea');
    const fileInfo = $('#fileInfo');
    const importBtn = $('#importBtn');

    // File input change event
    fileInput.on('change', function() {
        const file = this.files[0];
        if (file) {
            displayFileInfo(file);
            fileUploadArea.addClass('file-selected');
            fileInfo.show();
        } else {
            fileUploadArea.removeClass('file-selected');
            fileInfo.hide();
        }
    });

    // Drag and drop events
    fileUploadArea.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    fileUploadArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    fileUploadArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            fileInput[0].files = files;
            fileInput.trigger('change');
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        if (!fileInput[0].files.length) {
            e.preventDefault();
            showToast('Veuillez sélectionner un fichier Excel', 'error');
            return false;
        }

        // Show loading state
        importBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Importation en cours...');
    });
});

function displayFileInfo(file) {
    $('#fileName').text(file.name);
    $('#fileSize').text(formatFileSize(file.size));
    $('#fileType').text(file.type || 'Excel');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function downloadTemplate() {
    // Create a simple Excel template
    const data = [
        ['numero', 'description', 'raccourci'],
        ['U001', '1er Régiment d\'Infanterie', 'INF'],
        ['U002', '2ème Escadron de Cavalerie', 'CAV'],
        ['U003', '3ème Groupe d\'Artillerie', 'ART'],
        ['U004', 'Bataillon du Génie', 'GEN']
    ];
    
    let csvContent = "data:text/csv;charset=utf-8,";
    data.forEach(function(rowArray) {
        let row = rowArray.join(",");
        csvContent += row + "\r\n";
    });
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "modele_unites.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showToast('Modèle téléchargé avec succès', 'success');
}

function showToast(message, type = 'info') {
    const toast = $(`
        <div class="toast-notification ${type}">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
        </div>
    `);
    
    $('body').append(toast);
    
    setTimeout(() => {
        toast.fadeOut(() => toast.remove());
    }, 3000);
}
</script>

<style>
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    animation: slideInRight 0.3s ease;
}

.toast-notification.success {
    background: #10b981;
}

.toast-notification.error {
    background: #ef4444;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
{% endblock %}
