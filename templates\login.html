<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BANGHALAU - Connexion</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-radius: 20px;
            --box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --box-shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }



        /* Login Container */
        .login-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(226, 232, 240, 0.5);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(0, 0, 0, 0.04);
            padding: 3rem 2.5rem;
            max-width: 450px;
            width: 100%;
            position: relative;
            z-index: 10;
            animation: slideInUp 0.8s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Logo and Header */
        .logo-container {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
        }



        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            animation: logoGlow 2s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        .logo::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: logoShine 3s ease-in-out infinite;
        }

        @keyframes logoGlow {
            0% {
                transform: scale(1) rotate(0deg);
                box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
                filter: brightness(1);
            }
            50% {
                transform: scale(1.1) rotate(2deg);
                box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5), 0 0 20px rgba(251, 191, 36, 0.3);
                filter: brightness(1.2);
            }
            100% {
                transform: scale(1) rotate(0deg);
                box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
                filter: brightness(1);
            }
        }

        @keyframes logoShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(0%) translateY(0%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .logo i {
            font-size: 2rem;
            color: white;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            animation: iconFloat 3s ease-in-out infinite;
            z-index: 2;
            position: relative;
        }

        @keyframes iconFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                color: white;
            }
            25% {
                transform: translateY(-3px) rotate(-2deg);
                color: #fbbf24;
            }
            50% {
                transform: translateY(0px) rotate(0deg);
                color: #f59e0b;
            }
            75% {
                transform: translateY(-2px) rotate(2deg);
                color: #fbbf24;
            }
        }

        .brand-title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%, #fbbf24 200%);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            animation: titleGradient 4s ease-in-out infinite, titleBounce 2s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
        }

        @keyframes titleGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes titleBounce {
            0%, 100% {
                transform: translateY(0px) scale(1);
            }
            50% {
                transform: translateY(-2px) scale(1.02);
            }
        }

        .brand-subtitle {
            color: var(--secondary-color);
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 2rem;
            animation: subtitleFade 3s ease-in-out infinite;
            opacity: 0.8;
        }

        @keyframes subtitleFade {
            0%, 100% {
                opacity: 0.8;
                transform: translateX(0px);
            }
            50% {
                opacity: 1;
                transform: translateX(2px);
                color: #667eea;
            }
        }

        .brand-organization {
            color: #fbbf24;
            font-size: 0.9rem;
            font-weight: 800;
            margin-bottom: 2rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            animation: organizationGlow 4s ease-in-out infinite;
            background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 50%, #fcd34d 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            text-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
            line-height: 1.4;
            display: block;
        }

        @keyframes organizationGlow {
            0%, 100% {
                opacity: 0.8;
                transform: translateY(0px);
                text-shadow: 0 2px 8px rgba(251, 191, 36, 0.3), 0 0 15px rgba(251, 191, 36, 0.2);
            }
            50% {
                opacity: 1;
                transform: translateY(-1px);
                text-shadow: 0 2px 12px rgba(251, 191, 36, 0.5), 0 0 25px rgba(251, 191, 36, 0.4);
            }
        }

        /* Developer Credit */
        .developer-credit {
            margin-top: 1.5rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(236, 72, 153, 0.1) 50%, rgba(59, 130, 246, 0.1) 100%);
            border-radius: 15px;
            border: 1px solid rgba(251, 191, 36, 0.3);
            text-align: center;
            font-size: 0.8rem;
            background: linear-gradient(135deg, #fbbf24 0%, #ec4899 50%, #3b82f6 100%);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            line-height: 1.4;
            animation: creditRainbow 5s ease-in-out infinite;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 25px rgba(251, 191, 36, 0.2);
            position: relative;
            overflow: hidden;
        }

        .developer-credit::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: creditShine 3s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes creditRainbow {
            0%, 100% {
                background-position: 0% 50%;
                transform: translateY(0px) scale(1);
                box-shadow: 0 8px 25px rgba(251, 191, 36, 0.2);
                border-color: rgba(251, 191, 36, 0.3);
            }
            25% {
                background-position: 25% 50%;
                transform: translateY(-1px) scale(1.01);
                box-shadow: 0 10px 30px rgba(236, 72, 153, 0.3);
                border-color: rgba(236, 72, 153, 0.4);
            }
            50% {
                background-position: 50% 50%;
                transform: translateY(-2px) scale(1.02);
                box-shadow: 0 12px 35px rgba(59, 130, 246, 0.3);
                border-color: rgba(59, 130, 246, 0.4);
            }
            75% {
                background-position: 75% 50%;
                transform: translateY(-1px) scale(1.01);
                box-shadow: 0 10px 30px rgba(236, 72, 153, 0.3);
                border-color: rgba(236, 72, 153, 0.4);
            }
        }

        @keyframes creditShine {
            0% { left: -100%; }
            50% { left: 0%; }
            100% { left: 100%; }
        }

        /* Form Styles */
        .form-container {
            margin-top: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
            font-weight: 600;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .form-control::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.5s ease;
            z-index: 1;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25), 0 10px 25px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px) scale(1.02);
            background: white;
            animation: inputGlow 0.3s ease;
        }

        .form-control:focus::before {
            left: 100%;
        }

        @keyframes inputGlow {
            0% { box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25); }
            50% { box-shadow: 0 0 0 0.4rem rgba(37, 99, 235, 0.4), 0 15px 30px rgba(102, 126, 234, 0.3); }
            100% { box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25), 0 10px 25px rgba(102, 126, 234, 0.2); }
        }

        .input-group {
            position: relative;
        }

        .input-group-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--secondary-color);
            font-size: 1.1rem;
            z-index: 5;
            transition: all 0.3s ease;
            animation: iconPulse 2s ease-in-out infinite;
        }

        .form-control:focus + .input-group-icon {
            color: var(--primary-color);
            transform: translateY(-50%) scale(1.2);
            animation: iconSpin 0.5s ease;
        }

        @keyframes iconPulse {
            0%, 100% {
                opacity: 0.7;
                transform: translateY(-50%) scale(1);
            }
            50% {
                opacity: 1;
                transform: translateY(-50%) scale(1.05);
            }
        }

        @keyframes iconSpin {
            0% { transform: translateY(-50%) scale(1) rotate(0deg); }
            50% { transform: translateY(-50%) scale(1.2) rotate(180deg); }
            100% { transform: translateY(-50%) scale(1.2) rotate(360deg); }
        }

        .form-control.with-icon {
            padding-left: 3rem;
        }

        /* Button Styles */
        .btn-login {
            width: 100%;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-size: 200% 200%;
            border: none;
            border-radius: 15px;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            margin-top: 1rem;
            animation: buttonGradient 3s ease-in-out infinite, buttonFloat 2s ease-in-out infinite;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        @keyframes buttonGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes buttonFloat {
            0%, 100% {
                transform: translateY(0px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            }
            50% {
                transform: translateY(-2px);
                box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
            }
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.5);
            animation-duration: 1s;
            background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 50%, #fbbf24 100%);
        }

        .btn-login:active {
            transform: translateY(-1px);
        }



        /* Flash Messages */
        .flash-messages {
            margin-bottom: 1.5rem;
        }

        .alert {
            padding: 1rem 1.25rem;
            border-radius: 15px;
            margin-bottom: 1rem;
            border: none;
            font-weight: 500;
            animation: slideInDown 0.5s ease-out;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .alert-danger {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .alert-success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }



        /* Responsive */
        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }

            .brand-title {
                font-size: 1.75rem;
            }

            .logo {
                width: 70px;
                height: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Particles -->
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>

    <div class="login-container">
        <!-- Logo and Header -->
        <div class="logo-container">

            <div class="logo">
                <i class="fas fa-building"></i>
            </div>
            <h1 class="brand-title">BANGHALAU</h1>
            <p class="brand-subtitle">Système de Gestion des Bungalows Militaires</p>
            <p class="brand-organization">
                Direction Regionale<br>
                Service Social /6RM
            </p>

            <!-- Developer Credit -->
            <div class="developer-credit">
                Ce logiciel Développé Par<br>
                MAMMERI-WAHID 2025
            </div>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                <div class="flash-messages">
                    {% for message in messages %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Login Form -->
        <form method="POST" class="form-container">
            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="fas fa-user me-2"></i>Nom d'utilisateur
                </label>
                <div class="input-group">
                    <input type="text"
                           id="username"
                           name="username"
                           class="form-control with-icon"
                           placeholder="Entrez votre nom d'utilisateur"
                           required
                           autocomplete="username">
                    <i class="fas fa-user input-group-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock me-2"></i>Mot de passe
                </label>
                <div class="input-group">
                    <input type="password"
                           id="password"
                           name="password"
                           class="form-control with-icon"
                           placeholder="Entrez votre mot de passe"
                           required
                           autocomplete="current-password">
                    <i class="fas fa-lock input-group-icon"></i>
                </div>
            </div>

            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>Se connecter
            </button>
        </form>

    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Simple form submission without loading animation
        document.querySelector('form').addEventListener('submit', function(e) {
            const btn = document.querySelector('.btn-login');
            // Simple submission without loading effects
        });



        // Add focus animations
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.3s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // Add typing animation to inputs
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('input', function() {
                const icon = this.parentElement.querySelector('.input-group-icon');
                if (icon) {
                    icon.style.animation = 'pulse 0.3s ease';
                    setTimeout(() => {
                        icon.style.animation = '';
                    }, 300);
                }
            });
        });

        // Add enter key support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                const form = document.querySelector('form');
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                if (username && password) {
                    form.submit();
                }
            }
        });



        // Auto-focus first input
        window.addEventListener('load', function() {
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
