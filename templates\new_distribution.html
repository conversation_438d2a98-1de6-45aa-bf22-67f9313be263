{% extends "base.html" %}

{% block title %}Nouvelle Distribution - BANGHALAU{% endblock %}

{% block extra_css %}
<!-- Arabic Font Import -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

<style>
    /* Font Styling */
    body, .form-control, .btn, .modal {
        font-family: Arial, sans-serif;
    }

    .arabic-text {
        font-family: Arial, sans-serif;
        font-weight: 500;
        line-height: 1.6;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* Clean Modern Background */
    body {
        background: #f8f9fa;
        min-height: 100vh;
    }

    .page-header {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
        border-left: 5px solid #007bff;
    }

    .page-header h1 {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .page-header p {
        color: #6c757d;
        margin-bottom: 0;
    }

    .form-container {
        max-width: 1000px;
        margin: 0 auto;
    }

    .form-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-section:hover {
        box-shadow: 0 4px 25px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    .section-title {
        color: #2c3e50;
        font-weight: 600;
        font-size: 1.2rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #e9ecef;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title i {
        color: #007bff;
        font-size: 1.1rem;
        width: 24px;
        text-align: center;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .form-control, .form-select {
        border: 1px solid #ced4da;
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 0.9rem;
        transition: all 0.2s ease;
        background: #fff;
    }

    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.1);
        background: #fff;
    }

    .btn-primary {
        background: #007bff;
        border: 1px solid #007bff;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.95rem;
        transition: all 0.2s ease;
    }

    .btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .btn-secondary {
        background: #6c757d;
        border: 1px solid #6c757d;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.95rem;
        transition: all 0.2s ease;
        color: white;
    }

    .btn-secondary:hover {
        background: #545b62;
        border-color: #545b62;
        color: white;
        transform: translateY(-1px);
    }
    
    .required {
        color: #e74c3c;
    }
    
    .search-container {
        position: relative;
        margin-bottom: 1rem;
    }

    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 8px 8px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .search-result-item {
        padding: 0.75rem;
        cursor: pointer;
        border-bottom: 1px solid #f8f9fa;
        transition: all 0.2s ease;
        font-family: Arial, sans-serif;
    }

    .search-result-item:hover {
        background: #f8f9fa;
        border-left: 3px solid #007bff;
    }

    .search-result-item:last-child {
        border-bottom: none;
    }

    .selected-item {
        background: #e7f3ff;
        border: 1px solid #b3d7ff;
        padding: 0.75rem;
        border-radius: 8px;
        margin-top: 0.75rem;
        display: none;
        position: relative;
        font-family: Arial, sans-serif;
        font-weight: 500;
        min-height: 60px;
        padding-right: 3rem;
    }

    .selected-item .btn {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        z-index: 10;
    }

    .selected-item span {
        display: block;
        margin-top: 0.25rem;
        line-height: 1.4;
    }

    /* Responsive adjustments for selected items */
    @media (max-width: 768px) {
        .selected-item {
            padding-right: 2.5rem;
            min-height: 50px;
        }

        .selected-item .btn {
            padding: 0.2rem 0.4rem;
            font-size: 0.7rem;
        }
    }

    .form-text {
        color: #6c757d;
        font-size: 0.8rem;
        margin-top: 0.25rem;
    }

    .required {
        color: #dc3545;
        font-weight: 600;
    }

    /* Custom scrollbar for search results */
    .search-results::-webkit-scrollbar {
        width: 4px;
    }

    .search-results::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .search-results::-webkit-scrollbar-thumb {
        background: #007bff;
        border-radius: 2px;
    }

    .search-results::-webkit-scrollbar-thumb:hover {
        background: #0056b3;
    }

    /* Submit button container */
    .submit-container {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
        margin-top: 1.5rem;
    }

    /* Input styling */
    .form-control::placeholder {
        color: #adb5bd;
        font-style: italic;
    }

    .form-control:invalid {
        border-color: #dc3545;
    }

    .form-control:valid {
        border-color: #28a745;
    }

    /* Alert styling */
    .alert {
        border-radius: 8px;
        border: none;
        font-weight: 500;
    }

    .alert-info {
        background: #e7f3ff;
        color: #0c5460;
    }

    .alert-success {
        background: #d1f2eb;
        color: #0f5132;
    }

    .alert-danger {
        background: #f8d7da;
        color: #721c24;
    }

    /* Additional responsive improvements */
    @media (max-width: 768px) {
        .form-container {
            max-width: 100%;
            padding: 0 1rem;
        }

        .form-section {
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .page-header {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .submit-container {
            padding: 1.5rem;
        }

        .btn {
            padding: 0.6rem 1.5rem;
            font-size: 0.9rem;
        }
    }

    /* Enhanced form field spacing */
    .row .col-md-6 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* Better visual separation between sections */
    .form-section + .form-section {
        margin-top: 2rem;
    }

    /* Statistics animation */
    .stat-update {
        animation: statPulse 0.5s ease-in-out;
    }

    @keyframes statPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }



    /* Disabled bungalow items */
    .search-result-item.disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background-color: #f8f9fa;
    }

    .search-result-item.disabled:hover {
        background-color: #f8f9fa;
        border-left: 3px solid #dc3545;
    }

    /* Enhanced selected bungalow info */
    #selected_bungalow_info .card-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
    }

    #selected_bungalow_info .bg-warning {
        background: linear-gradient(135deg, #ffc107, #e0a800) !important;
    }

    #selected_bungalow_info .bg-info {
        background: linear-gradient(135deg, #17a2b8, #138496) !important;
    }

    #selected_bungalow_info .bg-success {
        background: linear-gradient(135deg, #28a745, #1e7e34) !important;
    }

    #selected_bungalow_info .bg-danger {
        background: linear-gradient(135deg, #dc3545, #c82333) !important;
    }

    /* Auto-filled input animation */
    .auto-filled {
        animation: autoFillGlow 2s ease-in-out;
        border-color: #17a2b8 !important;
        box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25) !important;
    }

    @keyframes autoFillGlow {
        0% {
            background-color: rgba(23, 162, 184, 0.1);
            transform: scale(1);
        }
        50% {
            background-color: rgba(23, 162, 184, 0.2);
            transform: scale(1.02);
        }
        100% {
            background-color: transparent;
            transform: scale(1);
        }
    }

    /* Auto-fill notification styling */
    #auto-fill-notification {
        border-left: 4px solid #17a2b8;
        background: linear-gradient(135deg, #d1ecf1, #bee5eb);
        animation: slideInFromTop 0.5s ease-out;
    }

    @keyframes slideInFromTop {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    #auto-fill-notification .fas {
        color: #17a2b8;
    }

    /* Availability notification styling */
    .availability-notification {
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        animation: slideInFromTop 0.5s ease-out;
    }

    .availability-notification h6 {
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .availability-notification ul {
        margin-bottom: 0.5rem;
        padding-left: 1.2rem;
    }

    .availability-notification li {
        margin-bottom: 0.25rem;
    }

    /* Notification container */
    #notification-container {
        position: relative;
        z-index: 1000;
    }

    /* Enhanced alert styling */
    .alert-danger {
        border-left: 4px solid #dc3545;
        background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    }

    .alert-warning {
        border-left: 4px solid #ffc107;
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    }

    .alert-success {
        border-left: 4px solid #28a745;
        background: linear-gradient(135deg, #d4edda, #c3e6cb);
    }


</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-2 arabic-text">
                    <i class="fas fa-plus-circle me-2"></i>Nouvelle Distribution
                </h1>
                <p class="arabic-text">Créer une nouvelle distribution de bungalow pour le personnel militaire</p>
            </div>
            <a href="{{ url_for('distributions') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour
            </a>
        </div>
    </div>

    <!-- Form Container -->
    <div class="form-container">
        <form method="POST" id="distributionForm">
            <div class="form-section">
                <!-- Première ligne: Numéro et Dates -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <label for="numero" class="form-label arabic-text">
                            <i class="fas fa-hashtag me-1"></i>Numéro de Distribution <span class="required">*</span>
                        </label>
                        <input type="text" class="form-control" id="numero" name="numero" required placeholder="Ex: DIST-2025-001">
                        <div class="form-text">Numéro unique pour identifier la distribution</div>
                    </div>
                    <div class="col-md-4">
                        <label for="date_debut" class="form-label arabic-text">
                            <i class="fas fa-calendar-plus me-1"></i>Date de Début <span class="required">*</span>
                        </label>
                        <input type="date" class="form-control" id="date_debut" name="date_debut" required>
                        <div class="form-text">Date de début de la période de distribution</div>
                    </div>
                    <div class="col-md-4">
                        <label for="date_fin" class="form-label arabic-text">
                            <i class="fas fa-calendar-minus me-1"></i>Date de Fin
                        </label>
                        <input type="date" class="form-control" id="date_fin" name="date_fin">
                        <div class="form-text">Laisser vide si la date de fin n'est pas encore déterminée</div>
                    </div>
                </div>

                <!-- Deuxième ligne: Recherche Bungalow et Personnel -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label for="bungalow_search" class="form-label arabic-text">
                            <i class="fas fa-home me-1"></i>Rechercher un Bungalow <span class="required">*</span>
                        </label>
                        <div class="search-container">
                            <div class="input-group">
                                <input type="text" class="form-control" id="bungalow_search" placeholder="Tapez le nom, numéro ou emplacement...">
                                <button class="btn btn-outline-secondary" type="button" id="showAllBungalows" title="Afficher tous les bungalows">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                            <div class="search-results" id="bungalow_results"></div>
                        </div>
                        <div class="form-text">Recherchez par nom, numéro ou emplacement du bungalow</div>

                        <!-- Selected Bungalow Info -->
                        <div class="mt-3" id="selected_bungalow_info" style="display: none;">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-home me-2"></i>Bungalow Sélectionné</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>Numéro:</strong> <span id="selected_numero"></span><br>
                                            <strong>Emplacement:</strong> <span id="selected_endroit"></span>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="row g-2">
                                                <div class="col-6">
                                                    <div class="text-center p-2 bg-info text-white rounded">
                                                        <small>Capacité Totale</small><br>
                                                        <strong id="selected_capacite">0</strong>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="text-center p-2 bg-warning text-white rounded">
                                                        <small>Places Restantes</small><br>
                                                        <strong id="selected_remaining">0</strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <input type="hidden" id="bungalow_id" name="bungalow_id" required>
                        <div class="selected-item" id="selected_bungalow">
                            <i class="fas fa-home me-2 text-primary"></i>
                            <strong class="arabic-text fw-bold text-dark">Bungalow sélectionné:</strong>
                            <span id="bungalow_info" class="arabic-text"></span>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearBungalowSelection()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="personnel_search" class="form-label arabic-text">
                            <i class="fas fa-user me-1"></i>Rechercher un Personnel <span class="required">*</span>
                        </label>
                        <div class="search-container">
                            <input type="text" class="form-control" id="personnel_search" placeholder="Tapez le nom, prénom, matricule, grade...">
                            <div class="search-results" id="personnel_results"></div>
                        </div>
                        <div class="form-text">Recherchez par nom, prénom, matricule, grade ou unité</div>

                        <input type="hidden" id="personnel_id" name="personnel_id" required>
                        <div class="selected-item" id="selected_personnel">
                            <i class="fas fa-user-tie me-2 text-success"></i>
                            <strong class="arabic-text fw-bold text-dark">Personnel sélectionné:</strong>
                            <span id="personnel_info" class="arabic-text"></span>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearPersonnelSelection()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Troisième ligne: Session et Notes -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label for="session_id" class="form-label arabic-text">
                            <i class="fas fa-calendar-alt me-1"></i>Session Associée
                            <span id="session_filter_indicator" class="badge bg-info ms-2" style="display: none;">
                                <i class="fas fa-filter me-1"></i>Filtrées par bungalow
                            </span>
                        </label>
                        <div class="search-container">
                            <input type="text" class="form-control" id="session_search" placeholder="Rechercher une session...">
                            <div class="search-results" id="session_results"></div>
                        </div>
                        <div class="form-text">Tapez pour rechercher et associer une session au bungalow (optionnel)</div>

                        <input type="hidden" id="session_id" name="session_id">
                        <div class="selected-item" id="selected_session">
                            <i class="fas fa-calendar-alt me-2 text-info"></i>
                            <strong class="arabic-text fw-bold text-dark">Session sélectionnée:</strong>
                            <span id="session_info" class="arabic-text"></span>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearSessionSelection()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="notes" class="form-label arabic-text">
                            <i class="fas fa-sticky-note me-1"></i>Notes sur la Distribution
                        </label>
                        <textarea class="form-control arabic-text" id="notes" name="notes" rows="4" placeholder="Ex: Maintenance programmée, problème de plomberie..."></textarea>
                        <div class="form-text">Ajoutez des détails sur la distribution actuel (optionnel)</div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Submit Buttons -->
    <div class="submit-container">
        <div class="d-flex justify-content-end gap-3">
            <button type="button" class="btn btn-secondary" onclick="window.location.href='{{ url_for('distributions') }}'">
                <i class="fas fa-times me-2"></i>Annuler
            </button>
            <button type="submit" form="distributionForm" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>Enregistrer la Distribution
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    console.log('🔥 DEBUG: Page loaded, initializing...');

    // Data arrays - Initialize with server data
    window.bungalowsData = {{ bungalows|tojson }};
    window.personnelData = {{ personnel|tojson }};
    window.sessionsData = {{ sessions|tojson }};

    let bungalows = window.bungalowsData || [];
    let personnel = window.personnelData || [];
    let sessions = window.sessionsData || [];

    console.log('✅ Data loaded:');
    console.log('🏠 Bungalows:', bungalows.length);
    console.log('👤 Personnel:', personnel.length);
    console.log('📅 Sessions:', sessions.length);

    if (bungalows.length > 0) {
        console.log('Sample bungalow:', bungalows[0]);
    }
    if (personnel.length > 0) {
        console.log('Sample personnel:', personnel[0]);
    }
    if (sessions.length > 0) {
        console.log('Sample session:', sessions[0]);
    }

    // Test if jQuery and elements are working
    console.log('🔍 Testing search elements...');
    console.log('Bungalow search element:', $('#bungalow_search').length);
    console.log('Personnel search element:', $('#personnel_search').length);
    console.log('Session search element:', $('#session_search').length);

    // Enhanced bungalow search
    $('#bungalow_search').on('input', function() {
        console.log('🔥 BUNGALOW SEARCH TRIGGERED!');
        const query = $(this).val().toLowerCase().trim();
        const results = $('#bungalow_results');

        console.log('🏠 Bungalow search query:', query);
        console.log('🏠 Available bungalows:', bungalows.length);

        if (query.length < 1) {
            results.hide();
            return;
        }

        // Check if bungalows array exists and has data
        if (!bungalows || bungalows.length === 0) {
            results.html('<div class="search-result-item text-muted">Aucun bungalow disponible dans la base de données</div>').show();
            return;
        }

        const filtered = bungalows.filter(b => {
            if (!b) return false;

            const nom = (b.nom || '').toString().toLowerCase();
            const numero = (b.numero || '').toString().toLowerCase();
            const endroit = (b.endroit || '').toString().toLowerCase();

            const matches = nom.includes(query) ||
                          numero.includes(query) ||
                          endroit.includes(query);

            console.log(`🔍 Checking bungalow ${b.id}: ${nom} ${numero} ${endroit} - matches: ${matches}`);
            return matches;
        });

        console.log('🎯 Filtered bungalows:', filtered.length);

        if (filtered.length > 0) {
            let html = '';
            filtered.slice(0, 10).forEach(bungalow => {
                const safeName = (bungalow.nom || 'Bungalow').replace(/'/g, "\\'");
                const safeEndroit = (bungalow.endroit || '').replace(/'/g, "\\'");
                const safeNumero = (bungalow.numero || '').toString();

                html += `<div class="search-result-item" onclick="selectBungalow(${bungalow.id}, '${safeName}', '${safeNumero}', '${safeEndroit}', ${bungalow.capacite || 0}, ${bungalow.capacite || 0})">
                    <strong>${bungalow.nom || 'Bungalow'} N° ${bungalow.numero || 'N/A'}</strong><br>
                    <small class="text-muted">${bungalow.endroit || 'Emplacement non spécifié'}</small>
                </div>`;
            });
            results.html(html).show();
        } else {
            results.html('<div class="search-result-item text-muted">Aucun bungalow trouvé pour "' + query + '"</div>').show();
        }
    });

    // Enhanced personnel search
    $('#personnel_search').on('input', function() {
        console.log('🔥 PERSONNEL SEARCH TRIGGERED!');
        const query = $(this).val().toLowerCase().trim();
        const results = $('#personnel_results');

        console.log('👤 Personnel search query:', query);
        console.log('👤 Available personnel:', personnel.length);

        if (query.length < 1) {
            results.hide();
            return;
        }

        // Check if personnel array exists and has data
        if (!personnel || personnel.length === 0) {
            results.html('<div class="search-result-item text-muted">Aucun personnel disponible dans la base de données</div>').show();
            return;
        }

        const filtered = personnel.filter(p => {
            if (!p) return false;

            const nom = (p.nom || '').toString().toLowerCase();
            const prenom = (p.prenom || '').toString().toLowerCase();
            const matricule = (p.matricule || '').toString().toLowerCase();
            const grade = (p.grade_name || '').toString().toLowerCase();
            const unite = (p.unite_description || '').toString().toLowerCase();

            const matches = nom.includes(query) ||
                          prenom.includes(query) ||
                          matricule.includes(query) ||
                          grade.includes(query) ||
                          unite.includes(query);

            console.log(`🔍 Checking personnel ${p.id}: ${nom} ${prenom} ${matricule} - matches: ${matches}`);
            return matches;
        });

        console.log('🎯 Filtered personnel:', filtered.length);

        if (filtered.length > 0) {
            let html = '';
            filtered.slice(0, 10).forEach(person => {
                const safeNom = (person.nom || '').replace(/'/g, "\\'");
                const safePrenom = (person.prenom || '').replace(/'/g, "\\'");
                const safeMatricule = (person.matricule || '').replace(/'/g, "\\'");
                const safeGrade = (person.grade_name || '').replace(/'/g, "\\'");
                const safeUnite = (person.unite_description || '').replace(/'/g, "\\'");

                html += `<div class="search-result-item" onclick="selectPersonnel(${person.id}, '${safeNom}', '${safePrenom}', '${safeMatricule}', '${safeGrade}', '${safeUnite}')">
                    <strong>${person.nom || 'N/A'} ${person.prenom || ''}</strong> (${person.matricule || 'N/A'})<br>
                    <small class="text-muted">${person.grade_name || 'Grade non spécifié'} - ${person.unite_description || 'Unité non spécifiée'}</small>
                </div>`;
            });
            results.html(html).show();
        } else {
            results.html('<div class="search-result-item text-muted">Aucun personnel trouvé pour "' + query + '"</div>').show();
        }
    });

    // Enhanced session search
    $('#session_search').on('input', function() {
        const query = $(this).val().toLowerCase().trim();
        const results = $('#session_results');

        console.log('📅 Session search query:', query);
        console.log('📅 Available sessions:', sessions.length);

        if (query.length < 1) {
            results.hide();
            return;
        }

        // Check if sessions array exists and has data
        if (!sessions || sessions.length === 0) {
            results.html('<div class="search-result-item text-muted">Aucune session disponible dans la base de données</div>').show();
            return;
        }

        const filtered = sessions.filter(s => {
            if (!s) return false;

            const numero = (s.numero || '').toString().toLowerCase();
            const description = (s.description || '').toString().toLowerCase();
            const dateDebut = (s.date_debut || '').toString().toLowerCase();
            const dateFin = (s.date_fin || '').toString().toLowerCase();

            const matches = numero.includes(query) ||
                          description.includes(query) ||
                          dateDebut.includes(query) ||
                          dateFin.includes(query);

            console.log(`🔍 Checking session ${s.id}: ${numero} ${description} - matches: ${matches}`);
            return matches;
        });

        console.log('🎯 Filtered sessions:', filtered.length);

        if (filtered.length > 0) {
            let html = '';
            filtered.slice(0, 10).forEach(session => {
                const safeNumero = (session.numero || '').replace(/'/g, "\\'");
                const safeDescription = (session.description || '').replace(/'/g, "\\'");
                const safeDateDebut = (session.date_debut || '').replace(/'/g, "\\'");
                const safeDateFin = (session.date_fin || '').replace(/'/g, "\\'");

                html += `<div class="search-result-item" onclick="selectSession(${session.id}, '${safeNumero}', '${safeDescription}', '${safeDateDebut}', '${safeDateFin}')">
                    <strong>${session.numero || 'N/A'}</strong><br>
                    <small class="text-muted">${session.description || 'Aucune description'}</small><br>
                    <small class="text-muted">${session.date_debut || 'N/A'} - ${session.date_fin || 'Non définie'}</small>
                </div>`;
            });
            results.html(html).show();
        } else {
            results.html('<div class="search-result-item text-muted">Aucune session trouvée pour "' + query + '"</div>').show();
        }
    });



    // Show all bungalows button
    $('#showAllBungalows').on('click', function() {
        const results = $('#bungalow_results');
        let html = '';

        console.log('📋 Showing all bungalows:', bungalows.length);

        // Check if bungalows array exists and has data
        if (!bungalows || bungalows.length === 0) {
            results.html('<div class="search-result-item text-muted">Aucun bungalow disponible dans la base de données</div>').show();
            return;
        }

        if (bungalows.length > 0) {
            bungalows.slice(0, 20).forEach(bungalow => {
                const safeName = (bungalow.nom || 'Bungalow').replace(/'/g, "\\'");
                const safeEndroit = (bungalow.endroit || '').replace(/'/g, "\\'");
                const safeNumero = (bungalow.numero || '').toString();

                html += `<div class="search-result-item" onclick="selectBungalow(${bungalow.id}, '${safeName}', '${safeNumero}', '${safeEndroit}', ${bungalow.capacite || 0}, ${bungalow.capacite || 0})">
                    <strong>${bungalow.nom || 'Bungalow'} N° ${bungalow.numero || 'N/A'}</strong><br>
                    <small class="text-muted">${bungalow.endroit || 'Emplacement non spécifié'}</small>
                </div>`;
            });

            if (bungalows.length > 20) {
                html += `<div class="search-result-item text-muted text-center">
                    <small>... et ${bungalows.length - 20} autres bungalows</small>
                </div>`;
            }
        } else {
            html = '<div class="search-result-item text-muted">Aucun bungalow disponible</div>';
        }

        results.html(html).show();
    });

    // Hide search results when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.search-container').length) {
            $('.search-results').hide();
        }
    });

    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    $('#date_debut').attr('min', today);

        // Update end date minimum when start date changes
        $('#date_debut').on('change', function() {
            $('#date_fin').attr('min', $(this).val());
        });

    // Hide search results when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.search-container').length) {
            $('.search-results').hide();
        }
    });
});



function selectBungalow(id, nom, numero, emplacement, capacity, remaining) {
    $('#bungalow_id').val(id);
    $('#bungalow_search').val(nom);
    $('#bungalow_info').html(`<strong>${nom} (N° ${numero})</strong><br><small class="text-muted">${emplacement || 'Emplacement non spécifié'}</small>`);
    $('#selected_bungalow').slideDown(200);
    $('#bungalow_results').hide();

    // Load sessions associated with this bungalow
    loadBungalowSessions(id);
}

function loadBungalowSessions(bungalowId) {
    // Clear current session selection
    clearSessionSelection();

    // Fetch sessions for this bungalow
    fetch(`/api/bungalow/${bungalowId}/sessions`)
        .then(response => response.json())
        .then(bungalowSessions => {
            // Update global sessions array to only include bungalow sessions
            sessions = bungalowSessions;
            $('#session_search').attr('placeholder', `${bungalowSessions.length} session(s) associée(s)`);
        })
        .catch(error => {
            console.error('Error loading bungalow sessions:', error);
        });
}

function selectPersonnel(id, nom, prenom, matricule, grade, unite) {
    $('#personnel_id').val(id);
    $('#personnel_search').val(`${nom} ${prenom}`);
    $('#personnel_info').html(`<strong>${nom} ${prenom}</strong> (${matricule})<br><small class="text-muted">${grade || 'Grade non spécifié'} - ${unite || 'Unité non spécifiée'}</small>`);
    $('#selected_personnel').slideDown(200);
    $('#personnel_results').hide();
}

function clearBungalowSelection() {
    $('#bungalow_id').val('');
    $('#bungalow_search').val('');
    $('#selected_bungalow').slideUp(200);
    $('#selected_bungalow_info').slideUp(200);

    // Reset sessions to all available sessions
    sessions = window.sessionsData || [];
    $('#session_search').attr('placeholder', 'Rechercher une session...');
    $('#session_search').prop('disabled', false);
    $('#session_filter_indicator').hide();

    // Clear session selection
    clearSessionSelection();
}

function clearPersonnelSelection() {
    $('#personnel_id').val('');
    $('#personnel_search').val('');
    $('#selected_personnel').slideUp(200);
}

function selectSession(id, numero, description, date_debut, date_fin) {
    $('#session_id').val(id);
    $('#session_search').val(numero);
    $('#session_info').html(`<strong class="fw-bold text-info">${numero}</strong><br><small class="text-muted">${description || 'Aucune description'}</small><br><small class="text-muted"><strong class="fw-bold text-success">${date_debut} - ${date_fin || 'Non définie'}</strong></small>`);
    $('#selected_session').slideDown(200);
    $('#session_results').hide();

    // Auto-fill distribution dates from session dates
    if (date_debut) {
        $('#date_debut').val(date_debut);
        // Add visual indication that date was auto-filled
        $('#date_debut').addClass('auto-filled');
        setTimeout(() => $('#date_debut').removeClass('auto-filled'), 2000);
    }

    if (date_fin) {
        $('#date_fin').val(date_fin);
        // Add visual indication that date was auto-filled
        $('#date_fin').addClass('auto-filled');
        setTimeout(() => $('#date_fin').removeClass('auto-filled'), 2000);
    }

    // Show notification about auto-fill
    showAutoFillNotification(date_debut, date_fin);
}

// Function to show auto-fill notification
function showAutoFillNotification(dateDebut, dateFin) {
    // Remove existing notification
    $('#auto-fill-notification').remove();

    // Create notification message
    let message = '📅 Dates automatiquement remplies depuis la session';
    if (dateDebut && dateFin) {
        message += ` (${dateDebut} → ${dateFin})`;
    } else if (dateDebut) {
        message += ` (Début: ${dateDebut})`;
    }

    // Create notification element
    const notification = $(`
        <div id="auto-fill-notification" class="alert alert-info alert-dismissible fade show mt-2" role="alert">
            <i class="fas fa-magic me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    // Insert after the session selection
    $('#selected_session').after(notification);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        notification.fadeOut(500, function() {
            $(this).remove();
        });
    }, 5000);
}

function clearSessionSelection() {
    $('#session_id').val('');
    $('#session_search').val('');
    $('#selected_session').slideUp(200);

    // Clear auto-filled dates
    $('#date_debut').val('').removeClass('auto-filled');
    $('#date_fin').val('').removeClass('auto-filled');

    // Remove auto-fill notification
    $('#auto-fill-notification').remove();
}

// Add form validation
$('#distributionForm').on('submit', function(e) {
    e.preventDefault(); // Prevent default submission

    // First check bungalow availability
    checkBungalowAvailability().then(isAvailable => {
        if (isAvailable) {
            submitForm();
        }
    });
});

// Function to check bungalow availability
function checkBungalowAvailability() {
    return new Promise((resolve) => {
        const bungalowId = $('#bungalow_id').val();
        const dateDebut = $('#date_debut').val();
        const dateFin = $('#date_fin').val();

        if (!bungalowId) {
            showValidationError('Veuillez sélectionner un bungalow');
            resolve(false);
            return;
        }

        // Get bungalow occupancy information
        fetch(`/api/bungalow/${bungalowId}/occupancy`)
            .then(response => response.json())
            .then(occupancy => {
                console.log('🏠 Bungalow Occupancy Check:', occupancy);

                if (occupancy.is_full || occupancy.remaining <= 0) {
                    showBungalowFullError(occupancy);
                    resolve(false);
                } else {
                    // Check for date conflicts if dates are provided
                    if (dateDebut) {
                        checkDateConflicts(bungalowId, dateDebut, dateFin).then(hasConflict => {
                            if (hasConflict) {
                                resolve(false);
                            } else {
                                showAvailabilityConfirmation(occupancy);
                                resolve(true);
                            }
                        });
                    } else {
                        showAvailabilityConfirmation(occupancy);
                        resolve(true);
                    }
                }
            })
            .catch(error => {
                console.error('Error checking bungalow availability:', error);
                showValidationError('Erreur lors de la vérification de la disponibilité');
                resolve(false);
            });
    });
}

// Function to check date conflicts
function checkDateConflicts(bungalowId, dateDebut, dateFin) {
    return new Promise((resolve) => {
        fetch(`/api/bungalow/${bungalowId}/conflicts?date_debut=${dateDebut}&date_fin=${dateFin || ''}`)
            .then(response => response.json())
            .then(conflicts => {
                if (conflicts.has_conflict) {
                    showDateConflictError(conflicts);
                    resolve(true);
                } else {
                    resolve(false);
                }
            })
            .catch(error => {
                console.error('Error checking date conflicts:', error);
                resolve(false);
            });
    });
}

// Function to show bungalow full error
function showBungalowFullError(occupancy) {
    const message = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Bungalow Complet!</h6>
            <p class="mb-2">Ce bungalow n'a plus de places disponibles:</p>
            <ul class="mb-2">
                <li><strong>Capacité totale:</strong> ${occupancy.capacity} places</li>
                <li><strong>Places occupées:</strong> ${occupancy.current} places</li>
                <li><strong>Places restantes:</strong> ${occupancy.remaining} places</li>
            </ul>
            <small>Veuillez choisir un autre bungalow avec des places disponibles.</small>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    showNotification(message, 'error');
}

// Function to show date conflict error
function showDateConflictError(conflicts) {
    const message = `
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-calendar-times me-2"></i>Conflit de Dates!</h6>
            <p class="mb-2">Il y a des conflits avec les dates sélectionnées:</p>
            <ul class="mb-2">
                ${conflicts.conflicting_distributions.map(dist =>
                    `<li>Distribution du ${dist.date_debut} au ${dist.date_fin || 'Non définie'}</li>`
                ).join('')}
            </ul>
            <small>Veuillez choisir d'autres dates ou un autre bungalow.</small>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    showNotification(message, 'warning');
}

// Function to show availability confirmation
function showAvailabilityConfirmation(occupancy) {
    const message = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-check-circle me-2"></i>Bungalow Disponible!</h6>
            <p class="mb-2">Ce bungalow a des places disponibles:</p>
            <ul class="mb-2">
                <li><strong>Capacité totale:</strong> ${occupancy.capacity} places</li>
                <li><strong>Places occupées:</strong> ${occupancy.current} places</li>
                <li><strong>Places restantes:</strong> ${occupancy.remaining} places</li>
            </ul>
            <small>Vous pouvez procéder à l'ajout de cette distribution.</small>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    showNotification(message, 'success');
}

// Function to show validation error
function showValidationError(message) {
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    showNotification(errorHtml, 'error');
}

// Function to show notifications
function showNotification(message, type) {
    // Remove existing notifications
    $('.availability-notification').remove();

    // Create notification container if it doesn't exist
    let container = $('#notification-container');
    if (container.length === 0) {
        container = $('<div id="notification-container" class="mb-3"></div>');
        $('#distributionForm').prepend(container);
    }

    // Add notification
    const notification = $(message).addClass('availability-notification');
    container.append(notification);

    // Auto-hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            notification.fadeOut(500, function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Scroll to notification
    $('html, body').animate({
        scrollTop: container.offset().top - 100
    }, 500);
}

// Function to submit the form
function submitForm() {
    let isValid = true;
    let errorMessage = '';

    // Check required fields
    if (!$('#numero').val().trim()) {
        isValid = false;
        errorMessage += '• Numéro de distribution requis\n';
    }

    if (!$('#bungalow_id').val()) {
        isValid = false;
        errorMessage += '• Sélection du bungalow requise\n';
    }

    if (!$('#personnel_id').val()) {
        isValid = false;
        errorMessage += '• Sélection du personnel requise\n';
    }

    if (!$('#date_debut').val()) {
        isValid = false;
        errorMessage += '• Date de début requise\n';
    }

    if (!isValid) {
        showValidationError('Veuillez corriger les erreurs suivantes:\n\n' + errorMessage);
        return false;
    }

    // Show loading state
    const submitBtn = $('#distributionForm').find('button[type="submit"]');
    const originalText = submitBtn.html();
    submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement en cours...');
    submitBtn.prop('disabled', true);

    // Submit the form
    $('#distributionForm')[0].submit();

    // Re-enable button after 5 seconds (in case of error)
    setTimeout(() => {
        submitBtn.html(originalText);
        submitBtn.prop('disabled', false);
    }, 5000);
});
</script>
{% endblock %}
