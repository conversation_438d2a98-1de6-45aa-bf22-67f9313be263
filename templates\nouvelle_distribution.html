{% extends "base.html" %}

{% block title %}Nouvelle Distribution - BANGHALAU{% endblock %}

{% block extra_css %}
<!-- خطوط جميلة للعربية والفرنسية -->
<link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
    /* تحسين الخطوط */
    body {
        font-family: Arial, sans-serif;
        background-color: #f8f9fa;
    }

    .arabic-text {
        font-family: '<PERSON><PERSON>wal', 'Cairo', '<PERSON><PERSON>', sans-serif;
        font-weight: 400;
        line-height: 1.8;
        direction: rtl;
        text-align: right;
    }

    /* تصميم بسيط وأنيق */
    .main-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
        margin-top: 20px;
    }

    .card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 10px 10px 0 0;
        border: none;
    }

    .card-title {
        font-family: Arial, sans-serif;
        font-weight: 600;
        font-size: 1.5rem;
        margin: 0;
    }

    .card-subtitle {
        font-family: Arial, sans-serif;
        font-weight: 400;
        opacity: 0.9;
        margin-top: 0.5rem;
        font-size: 0.95rem;
    }

    /* تحسين حاويات البحث */
    .search-container {
        position: relative;
        margin-bottom: 1rem;
    }

    .search-input-group {
        display: flex;
        border: 1px solid #ced4da;
        border-radius: 6px;
        overflow: hidden;
        background: white;
    }

    .search-input-group:focus-within {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .search-input-group .form-control {
        border: none;
        padding: 0.75rem 1rem;
        font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
        font-size: 0.95rem;
        background: transparent;
        box-shadow: none;
        flex: 1;
        line-height: 1.5;
    }

    .search-input-group .form-control:focus {
        box-shadow: none;
        border: none;
        outline: none;
    }

    .search-input-group .form-control::placeholder {
        color: #6c757d;
        font-style: normal;
    }

    .search-input-group .btn {
        border: none;
        background: #007bff;
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 0;
    }

    .search-input-group .btn:hover {
        background: #0056b3;
    }

    /* تحسين نتائج البحث */
    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ced4da;
        border-radius: 0 0 6px 6px;
        max-height: 250px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .search-result-item {
        padding: 0.75rem 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f8f9fa;
        transition: background-color 0.2s ease;
        font-family: Arial, sans-serif;
    }

    .search-result-item:hover {
        background-color: #f8f9fa;
    }

    .search-result-item:last-child {
        border-bottom: none;
    }

    .search-result-title {
        font-family: Arial, sans-serif;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
    }

    .search-result-subtitle {
        font-family: Arial, sans-serif;
        font-size: 0.85rem;
        color: #6c757d;
        font-weight: 400;
    }

    /* تحسين النصوص العربية في النتائج */
    .search-result-item .arabic-content {
        font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
        direction: rtl;
        text-align: right;
        font-size: 1rem;
        line-height: 1.7;
        font-weight: 500;
    }

    /* تحسين الخط العربي في جميع نتائج البحث */
    .search-result-title {
        font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .search-result-subtitle {
        font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
        font-size: 0.85rem;
        color: #6c757d;
        font-weight: 400;
        line-height: 1.4;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .search-result-subtitle .ms-3 {
        color: #007bff;
        font-weight: 500;
    }

    .search-result-subtitle i {
        color: #6c757d;
        width: 14px;
        text-align: center;
    }

    .search-result-subtitle .ms-3 i {
        color: #007bff;
    }

    .search-result-subtitle span {
        display: inline-flex;
        align-items: center;
        white-space: nowrap;
    }

    /* تحسين عرض معلومات الأفراد */
    .search-result-subtitle .fas.fa-star {
        color: #ffc107;
    }

    .search-result-subtitle .fas.fa-users {
        color: #28a745;
    }

    .search-result-subtitle .fas.fa-id-card {
        color: #6f42c1;
    }

    /* تحسين خاص للنصوص المختلطة (عربي/فرنسي) */
    .search-result-item {
        font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
        line-height: 1.6;
    }

    /* تحسين العناصر المحددة */
    .selected-item {
        background: #e7f3ff;
        border: 1px solid #007bff;
        padding: 1rem;
        border-radius: 6px;
        margin-top: 0.75rem;
        display: none;
        position: relative;
        font-family: Arial, sans-serif;
    }

    .selected-item .btn {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: 4px;
        background: #dc3545;
        border: none;
        color: white;
    }

    .selected-item .btn:hover {
        background: #c82333;
    }

    .selected-info {
        padding-right: 2.5rem;
    }

    .selected-title {
        font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .selected-subtitle {
        font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
        font-size: 0.85rem;
        color: #6c757d;
        font-weight: 400;
        line-height: 1.4;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .selected-subtitle .ms-3 {
        color: #007bff;
        font-weight: 500;
    }

    .selected-subtitle i {
        color: #6c757d;
        width: 14px;
        text-align: center;
    }

    .selected-subtitle .ms-3 i {
        color: #007bff;
    }

    .selected-subtitle span {
        display: inline-flex;
        align-items: center;
        white-space: nowrap;
    }

    /* تحسين عرض معلومات الأفراد في العناصر المحددة */
    .selected-subtitle .fas.fa-star {
        color: #ffc107;
    }

    .selected-subtitle .fas.fa-users {
        color: #28a745;
    }

    .selected-subtitle .fas.fa-id-card {
        color: #6f42c1;
    }

    /* تصميم إحصائيات السعة */
    .capacity-stats {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 1rem;
        margin-top: 0.75rem;
        border: 1px solid #e9ecef;
    }

    .capacity-stat {
        padding: 0.5rem;
    }

    .capacity-number {
        font-size: 1.5rem;
        font-weight: 700;
        font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
        line-height: 1.2;
    }

    .capacity-label {
        font-size: 0.75rem;
        font-weight: 500;
        color: #6c757d;
        font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
        margin-top: 0.25rem;
        line-height: 1.2;
    }

    .capacity-stat:not(:last-child) {
        border-right: 1px solid #dee2e6;
    }

    /* تحسين التسميات والحقول */
    .form-label {
        font-family: Arial, sans-serif;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-label i {
        color: #007bff;
        font-size: 1rem;
    }

    .required {
        color: #dc3545;
        font-weight: 600;
    }

    .form-control {
        border: 1px solid #ced4da;
        border-radius: 6px;
        padding: 0.75rem;
        font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
        font-size: 0.95rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        background: white;
        line-height: 1.5;
    }

    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        outline: none;
    }

    .form-text {
        font-family: Arial, sans-serif;
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    /* تحسين الأزرار */
    .btn {
        font-family: Arial, sans-serif;
        font-weight: 500;
        border-radius: 6px;
        padding: 0.75rem 1.5rem;
        transition: all 0.15s ease-in-out;
        border: 1px solid transparent;
    }

    .btn-primary {
        background: #007bff;
        border-color: #007bff;
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
    }

    .btn-secondary {
        background: #6c757d;
        border-color: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #545b62;
        border-color: #545b62;
    }

    /* تحسين المساحات والتخطيط */
    .card-body {
        padding: 2rem;
    }

    .row {
        margin-bottom: 1rem;
    }

    .row:last-child {
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10">
            <div class="main-card">
                <div class="card-header">
                    <h1 class="card-title">
                        <i class="fas fa-plus-circle me-2"></i>
                        Nouvelle Distribution de Bungalow
                    </h1>
                    <p class="card-subtitle">
                        Créer une nouvelle attribution de logement pour le personnel militaire
                    </p>
                </div>
                <div class="card-body">
                    <form id="distributionForm" method="POST" action="{{ url_for('nouvelle_distribution') }}">
                        <div class="row">
                            <!-- Numéro de Distribution -->
                            <div class="col-md-6 mb-3">
                                <label for="numero" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>Numéro de Distribution <span class="required">*</span>
                                </label>
                                <input type="text" class="form-control" id="numero" name="numero" required>
                                <div class="form-text">Numéro unique pour identifier cette distribution</div>
                            </div>
                            
                            <!-- Date de Début -->
                            <div class="col-md-6 mb-3">
                                <label for="date_debut" class="form-label">
                                    <i class="fas fa-calendar-alt me-1"></i>Date de Début <span class="required">*</span>
                                </label>
                                <input type="date" class="form-control" id="date_debut" name="date_debut" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Recherche Bungalow -->
                            <div class="col-md-6 mb-3">
                                <label for="bungalow_search" class="form-label">
                                    <i class="fas fa-home"></i>
                                    Rechercher un Bungalow
                                    <span class="required">*</span>
                                </label>
                                <div class="search-container">
                                    <div class="search-input-group">
                                        <input type="text" class="form-control" id="bungalow_search" placeholder="Tapez le nom, numéro ou emplacement...">
                                        <button class="btn" type="button" id="showAllBungalows" title="Afficher tous les bungalows">
                                            <i class="fas fa-list"></i>
                                        </button>
                                    </div>
                                    <div class="search-results" id="bungalow_results"></div>
                                </div>
                                <input type="hidden" id="bungalow_id" name="bungalow_id" required>

                                <div class="selected-item" id="selected_bungalow">
                                    <button type="button" class="btn btn-sm" onclick="clearBungalowSelection()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <div class="selected-info" id="bungalow_info"></div>
                                </div>
                            </div>
                            
                            <!-- Recherche Personnel -->
                            <div class="col-md-6 mb-3">
                                <label for="personnel_search" class="form-label">
                                    <i class="fas fa-user"></i>
                                    Rechercher un Personnel
                                    <span class="required">*</span>
                                </label>
                                <div class="search-container">
                                    <div class="search-input-group">
                                        <input type="text" class="form-control" id="personnel_search" placeholder="Tapez le nom, prénom, matricule, grade, unité, N°...">
                                    </div>
                                    <div class="search-results" id="personnel_results"></div>
                                </div>
                                <input type="hidden" id="personnel_id" name="personnel_id" required>

                                <div class="selected-item" id="selected_personnel">
                                    <button type="button" class="btn btn-sm" onclick="clearPersonnelSelection()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <div class="selected-info" id="personnel_info"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Date de Fin -->
                            <div class="col-md-6 mb-3">
                                <label for="date_fin" class="form-label">
                                    <i class="fas fa-calendar-check me-1"></i>Date de Fin
                                </label>
                                <input type="date" class="form-control" id="date_fin" name="date_fin">
                                <div class="form-text">Optionnel - laissez vide pour une durée indéterminée</div>
                            </div>
                            
                            <!-- Session -->
                            <div class="col-md-6 mb-3">
                                <label for="session_search" class="form-label">
                                    <i class="fas fa-calendar-week"></i>
                                    Session (Optionnel)
                                </label>
                                <div class="search-container">
                                    <div class="search-input-group">
                                        <input type="text" class="form-control" id="session_search" placeholder="Rechercher une session...">
                                    </div>
                                    <div class="search-results" id="session_results"></div>
                                </div>
                                <input type="hidden" id="session_id" name="session_id">

                                <div class="selected-item" id="selected_session">
                                    <button type="button" class="btn btn-sm" onclick="clearSessionSelection()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <div class="selected-info" id="session_info"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Notes -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="notes" class="form-label">
                                    <i class="fas fa-sticky-note me-1"></i>Notes
                                </label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Notes additionnelles (optionnel)"></textarea>
                            </div>
                        </div>
                        
                        <!-- Boutons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('distributions') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Créer la Distribution
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    console.log('🔥 Nouvelle Distribution - Page chargée');
    
    // Données
    let bungalows = [];
    let personnel = [];
    let sessions = [];
    
    // Charger les données
    loadData();
    
    function loadData() {
        console.log('📡 Chargement des données...');
        
        Promise.all([
            fetch('/api/bungalows').then(r => r.json()),
            fetch('/api/personnel').then(r => r.json()),
            fetch('/api/sessions').then(r => r.json())
        ]).then(([bungalowsData, personnelData, sessionsData]) => {
            bungalows = bungalowsData;
            personnel = personnelData;
            sessions = sessionsData;
            
            console.log('✅ Données chargées:');
            console.log('🏠 Bungalows:', bungalows.length);
            console.log('👤 Personnel:', personnel.length);
            console.log('📅 Sessions:', sessions.length);
            
            initializeSearch();
        }).catch(error => {
            console.error('❌ Erreur chargement:', error);
            alert('Erreur lors du chargement des données');
        });
    }
    
    function initializeSearch() {
        console.log('🔍 Initialisation de la recherche...');
        
        // Recherche Bungalows
        $('#bungalow_search').on('input', function() {
            const query = $(this).val().toLowerCase().trim();
            searchBungalows(query);
        });
        
        // Recherche Personnel
        $('#personnel_search').on('input', function() {
            const query = $(this).val().toLowerCase().trim();
            searchPersonnel(query);
        });
        
        // Recherche Sessions
        $('#session_search').on('input', function() {
            const query = $(this).val().toLowerCase().trim();
            searchSessions(query);
        });
        
        // Bouton Tous les Bungalows
        $('#showAllBungalows').on('click', function() {
            showAllBungalows();
        });
        
        // Cacher résultats en cliquant ailleurs
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.search-container').length) {
                $('.search-results').hide();
            }
        });
        
        // Date minimum aujourd'hui
        const today = new Date().toISOString().split('T')[0];
        $('#date_debut').attr('min', today);
        
        // Date fin minimum = date début
        $('#date_debut').on('change', function() {
            $('#date_fin').attr('min', $(this).val());
        });
    }
    
    function searchBungalows(query) {
        const results = $('#bungalow_results');
        
        if (query.length < 1) {
            results.hide();
            return;
        }
        
        console.log('🏠 Recherche bungalows:', query);
        
        const filtered = bungalows.filter(b => {
            const nom = (b.nom || '').toLowerCase();
            const numero = (b.numero || '').toLowerCase();
            const endroit = (b.endroit || '').toLowerCase();
            
            return nom.includes(query) || numero.includes(query) || endroit.includes(query);
        });
        
        if (filtered.length > 0) {
            let html = '';
            filtered.slice(0, 10).forEach(bungalow => {
                const capacite = bungalow.capacite || 0;
                html += `<div class="search-result-item" onclick="selectBungalow(${bungalow.id}, '${bungalow.nom}', '${bungalow.numero}', '${bungalow.endroit}', ${capacite})">
                    <div class="search-result-title">${bungalow.nom} N° ${bungalow.numero}</div>
                    <div class="search-result-subtitle">
                        <i class="fas fa-map-marker-alt me-1"></i>${bungalow.endroit}
                        <span class="ms-3"><i class="fas fa-users me-1"></i>Capacité: ${capacite}</span>
                    </div>
                </div>`;
            });
            results.html(html).show();
        } else {
            results.html('<div class="search-result-item"><div class="search-result-subtitle">Aucun bungalow trouvé</div></div>').show();
        }
    }

    function searchPersonnel(query) {
        const results = $('#personnel_results');

        if (query.length < 1) {
            results.hide();
            return;
        }

        console.log('👤 Recherche personnel:', query);

        const filtered = personnel.filter(p => {
            const nom = (p.nom || '').toLowerCase();
            const prenom = (p.prenom || '').toLowerCase();
            const matricule = (p.matricule || '').toLowerCase();
            const grade = (p.grade_name || '').toLowerCase();
            const unite = (p.unite_description || p.unite_raccourci || '').toLowerCase();
            const numero = (p.numero || '').toString().toLowerCase();

            return nom.includes(query) || prenom.includes(query) || matricule.includes(query) ||
                   grade.includes(query) || unite.includes(query) || numero.includes(query);
        });

        if (filtered.length > 0) {
            let html = '';
            filtered.slice(0, 10).forEach(person => {
                const unite = person.unite_description || person.unite_raccourci || 'Non assigné';
                const numero = person.numero || 'N/A';
                html += `<div class="search-result-item" onclick="selectPersonnel(${person.id}, '${person.nom}', '${person.prenom}', '${person.matricule}', '${person.grade_name}', '${unite}', '${numero}')">
                    <div class="search-result-title">${person.nom} ${person.prenom} (${person.matricule})</div>
                    <div class="search-result-subtitle">
                        <span><i class="fas fa-star me-1"></i>${person.grade_name}</span>
                        <span class="ms-3"><i class="fas fa-users me-1"></i>${unite}</span>
                        <span class="ms-3"><i class="fas fa-id-card me-1"></i>N° ${numero}</span>
                    </div>
                </div>`;
            });
            results.html(html).show();
        } else {
            results.html('<div class="search-result-item"><div class="search-result-subtitle">Aucun personnel trouvé</div></div>').show();
        }
    }

    function searchSessions(query) {
        const results = $('#session_results');

        if (query.length < 1) {
            results.hide();
            return;
        }

        console.log('📅 Recherche sessions:', query);

        const filtered = sessions.filter(s => {
            const numero = (s.numero || '').toLowerCase();
            const description = (s.description || '').toLowerCase();

            return numero.includes(query) || description.includes(query);
        });

        if (filtered.length > 0) {
            let html = '';
            filtered.slice(0, 10).forEach(session => {
                html += `<div class="search-result-item" onclick="selectSession(${session.id}, '${session.numero}', '${session.description}', '${session.date_debut}', '${session.date_fin}')">
                    <div class="search-result-title">${session.numero}</div>
                    <div class="search-result-subtitle">${session.description}</div>
                </div>`;
            });
            results.html(html).show();
        } else {
            results.html('<div class="search-result-item"><div class="search-result-subtitle">Aucune session trouvée</div></div>').show();
        }
    }

    function showAllBungalows() {
        const results = $('#bungalow_results');
        let html = '';

        bungalows.slice(0, 20).forEach(bungalow => {
            const capacite = bungalow.capacite || 0;
            html += `<div class="search-result-item" onclick="selectBungalow(${bungalow.id}, '${bungalow.nom}', '${bungalow.numero}', '${bungalow.endroit}', ${capacite})">
                <div class="search-result-title">${bungalow.nom} N° ${bungalow.numero}</div>
                <div class="search-result-subtitle">
                    <i class="fas fa-map-marker-alt me-1"></i>${bungalow.endroit}
                    <span class="ms-3"><i class="fas fa-users me-1"></i>Capacité: ${capacite}</span>
                </div>
            </div>`;
        });

        if (bungalows.length > 20) {
            html += `<div class="search-result-item text-muted text-center">
                <small>... et ${bungalows.length - 20} autres bungalows</small>
            </div>`;
        }

        results.html(html).show();
    }
});

// Fonctions de sélection
function selectBungalow(id, nom, numero, endroit, capacite = 0) {
    $('#bungalow_id').val(id);
    $('#bungalow_search').val(nom);

    // Show loading state
    $('#bungalow_info').html(`
        <div class="selected-title">${nom} (N° ${numero})</div>
        <div class="selected-subtitle">
            <i class="fas fa-map-marker-alt me-1"></i>${endroit}
            <span class="ms-3"><i class="fas fa-spinner fa-spin me-1"></i>Chargement des statistiques...</span>
        </div>
    `);
    $('#selected_bungalow').slideDown(200);
    $('#bungalow_results').hide();

    // Fetch capacity statistics
    fetch(`/api/bungalow/${id}/capacity`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                $('#bungalow_info').html(`
                    <div class="selected-title">${nom} (N° ${numero})</div>
                    <div class="selected-subtitle">
                        <i class="fas fa-map-marker-alt me-1"></i>${endroit}
                        <span class="ms-3"><i class="fas fa-users me-1"></i>Capacité: ${capacite}</span>
                    </div>
                `);
            } else {
                $('#bungalow_info').html(`
                    <div class="selected-title">${nom} (N° ${numero})</div>
                    <div class="selected-subtitle">
                        <i class="fas fa-map-marker-alt me-1"></i>${endroit}
                    </div>
                    <div class="capacity-stats mt-2">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="capacity-stat">
                                    <div class="capacity-number text-primary">${data.original_capacity}</div>
                                    <div class="capacity-label">Capacité Totale</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="capacity-stat">
                                    <div class="capacity-number text-danger">${data.used}</div>
                                    <div class="capacity-label">Occupées</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="capacity-stat">
                                    <div class="capacity-number text-success">${data.remaining}</div>
                                    <div class="capacity-label">Disponibles</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `);
            }
        })
        .catch(error => {
            console.error('Erreur lors du chargement des statistiques:', error);
            $('#bungalow_info').html(`
                <div class="selected-title">${nom} (N° ${numero})</div>
                <div class="selected-subtitle">
                    <i class="fas fa-map-marker-alt me-1"></i>${endroit}
                    <span class="ms-3"><i class="fas fa-users me-1"></i>Capacité: ${capacite}</span>
                </div>
            `);
        });

    console.log('✅ Bungalow sélectionné:', nom, '- Capacité:', capacite);
}

function selectPersonnel(id, nom, prenom, matricule, grade, unite = 'Non assigné', numero = 'N/A') {
    $('#personnel_id').val(id);
    $('#personnel_search').val(`${nom} ${prenom}`);
    $('#personnel_info').html(`
        <div class="selected-title">${nom} ${prenom} (${matricule})</div>
        <div class="selected-subtitle">
            <span><i class="fas fa-star me-1"></i>${grade}</span>
            <span class="ms-3"><i class="fas fa-users me-1"></i>${unite}</span>
            <span class="ms-3"><i class="fas fa-id-card me-1"></i>N° ${numero}</span>
        </div>
    `);
    $('#selected_personnel').slideDown(200);
    $('#personnel_results').hide();
    console.log('✅ Personnel sélectionné:', nom, prenom, '- Unité:', unite, '- N°:', numero);
}

function selectSession(id, numero, description, dateDebut, dateFin) {
    $('#session_id').val(id);
    $('#session_search').val(numero);
    $('#session_info').html(`
        <div class="selected-title">${numero}</div>
        <div class="selected-subtitle">${description}</div>
    `);
    $('#selected_session').slideDown(200);
    $('#session_results').hide();

    // Auto-remplir les dates
    if (dateDebut) $('#date_debut').val(dateDebut);
    if (dateFin) $('#date_fin').val(dateFin);

    console.log('✅ Session sélectionnée:', numero);
}

function clearBungalowSelection() {
    $('#bungalow_id').val('');
    $('#bungalow_search').val('');
    $('#selected_bungalow').slideUp(200);
    console.log('🗑️ Sélection bungalow effacée');
}

function clearPersonnelSelection() {
    $('#personnel_id').val('');
    $('#personnel_search').val('');
    $('#selected_personnel').slideUp(200);
    console.log('🗑️ Sélection personnel effacée');
}

function clearSessionSelection() {
    $('#session_id').val('');
    $('#session_search').val('');
    $('#selected_session').slideUp(200);
    console.log('🗑️ Sélection session effacée');
}
</script>
{% endblock %}
