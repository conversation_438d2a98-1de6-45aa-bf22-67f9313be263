{% extends "base.html" %}

{% block title %}Personnel Militaire - BANGHALAU{% endblock %}

{% block extra_css %}
<!-- استيراد الخطوط العربية المحسنة -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
/* خطوط عربية محسنة لصفحة Personnel */
:root {
    --arabic-font-primary: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --arabic-font-secondary: '<PERSON><PERSON><PERSON>', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --mixed-font: 'Cairo', 'Noto Sans Arabic', 'Inter', 'Segoe UI', sans-serif;
}

/* تحسين عام للنصوص */
body, .card, .btn, .form-control, .modal-content {
    font-family: var(--mixed-font);
    font-feature-settings: 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* العناوين */
.page-title, .card-title, .modal-title, h1, h2, h3, h4, h5, h6 {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    line-height: 1.3;
}

/* الجداول */
.table-personnel th {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    font-size: 0.95rem;
}

.table-personnel td {
    font-family: var(--mixed-font);
    font-weight: 400;
    font-size: 0.9rem;
    line-height: 1.5;
    vertical-align: middle;
}

/* النصوص المخصصة للموظفين */
.personnel-name {
    font-family: var(--mixed-font);
    font-weight: 600;
    line-height: 1.4;
    direction: auto;
    text-align: start;
    font-size: 1rem;
    color: #1f2937;
}

.grade-text {
    font-family: var(--mixed-font);
    font-weight: 500;
    line-height: 1.3;
    direction: auto;
    text-align: start;
    font-size: 0.9rem;
}

.unit-text {
    font-family: var(--mixed-font);
    font-weight: 500;
    line-height: 1.3;
    direction: auto;
    text-align: start;
    font-size: 0.9rem;
}

.region-text {
    font-family: var(--mixed-font);
    font-weight: 500;
    line-height: 1.3;
    direction: auto;
    text-align: start;
    font-size: 0.9rem;
}

.contact-info {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.4;
    direction: auto;
    text-align: start;
    font-size: 0.85rem;
    color: #4b5563;
}

/* الشارات والتسميات */
.badge-personnel {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* التنبيهات */
.alert {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.5;
    direction: auto;
    text-align: start;
}

/* الأزرار */
.btn {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* النماذج */
.form-control, .form-select {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
}

.form-label {
    font-family: var(--mixed-font);
    font-weight: 500;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    .personnel-name {
        font-size: 0.95rem;
    }

    .grade-text, .unit-text, .region-text {
        font-size: 0.85rem;
    }

    .contact-info {
        font-size: 0.8rem;
    }

    .table-personnel th, .table-personnel td {
        font-size: 0.85rem;
    }

    .page-title, .card-title {
        font-size: 1.1rem;
    }

    .badge-personnel {
        font-size: 0.7rem;
    }
}

/* تحسين للطباعة */
@media print {
    body, .table, .card, .btn, .form-control, .modal-content {
        font-family: 'Cairo', 'Noto Sans Arabic', serif;
        color: #000;
    }
}

/* النصوص العربية المخصصة */
.arabic-text {
    font-family: var(--arabic-font-primary);
    font-weight: 500;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
}

.mixed-text {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
    line-height: 1.5;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gradient page-title">
                        <i class="fas fa-users me-2"></i>Personnel Militaire
                    </h1>
                    <p class="text-muted mb-0">Gérez tout le personnel militaire</p>
                </div>
                <a href="{{ url_for('add_personnel') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus me-1"></i>Nouveau Personnel
                </a>
            </div>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card-modern">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Liste du Personnel
                </h5>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control" id="searchInput" placeholder="Rechercher..." style="width: 250px;">
                    <button class="btn btn-outline-primary" onclick="exportData()">
                        <i class="fas fa-download me-1"></i>Exporter
                    </button>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-modern table-personnel" id="personnelTable">
                <thead>
                    <tr>
                        <th><i class="fas fa-hashtag me-1"></i>Matricule</th>
                        <th><i class="fas fa-user me-1"></i>Nom Complet</th>
                        <th><i class="fas fa-star me-1"></i>Grade</th>
                        <th><i class="fas fa-flag me-1"></i>Unité</th>
                        <th><i class="fas fa-map-marker-alt me-1"></i>Région</th>
                        <th><i class="fas fa-phone me-1"></i>Contact</th>
                        <th><i class="fas fa-cogs me-1"></i>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for person in personnel %}
                    <tr>
                        <td>
                            <strong class="text-primary">{{ person.numero }}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <strong class="personnel-name">{{ person.nom }} {{ person.prenom }}</strong>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-warning badge-personnel">
                                <i class="fas fa-star me-1"></i><span class="grade-text">{{ person.grade_name or 'Non défini' }}</span>
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-info badge-personnel">
                                <i class="fas fa-flag me-1"></i><span class="unit-text">{{ person.unite_description or 'Non définie' }}</span>
                            </span>
                        </td>
                        <td>
                            {% if person.region %}
                                <span class="badge bg-secondary badge-personnel">
                                    <i class="fas fa-map-marker-alt me-1"></i><span class="region-text">{{ person.region }}</span>
                                </span>
                            {% else %}
                                <span class="text-muted">Non définie</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if person.telephone or person.email %}
                                <div class="contact-info">
                                    {% if person.telephone %}
                                        <div><i class="fas fa-phone me-1"></i>{{ person.telephone }}</div>
                                    {% endif %}
                                    {% if person.email %}
                                        <div><i class="fas fa-envelope me-1"></i>{{ person.email }}</div>
                                    {% endif %}
                                </div>
                            {% else %}
                                <span class="text-muted">Aucun contact</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('edit_personnel', personnel_id=person.id) }}"
                                   class="btn btn-sm btn-outline-warning"
                                   title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-info"
                                        onclick="viewDetails({{ person.id }})"
                                        title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger"
                                        onclick="confirmDelete({{ person.id }}, '{{ person.nom }} {{ person.prenom }}')"
                                        title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <h5>Aucun personnel trouvé</h5>
                                <p>Commencez par ajouter votre premier personnel.</p>
                                <a href="{{ url_for('add_personnel') }}" class="btn btn-primary">
                                    <i class="fas fa-user-plus me-1"></i>Ajouter du Personnel
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Confirmer la Suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer <strong id="personnelName"></strong> ?</p>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    Cette action est irréversible.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>Supprimer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden delete form -->
<form id="deleteForm" method="POST" style="display: none;"></form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        $('#personnelTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Table row hover effects
    $('#personnelTable tbody tr').hover(
        function() {
            $(this).addClass('table-hover-effect');
        },
        function() {
            $(this).removeClass('table-hover-effect');
        }
    );
});

function viewDetails(personnelId) {
    // Implement view details functionality
    window.location.href = `/personnel/view/${personnelId}`;
}

function confirmDelete(personnelId, personnelName) {
    $('#personnelName').text(personnelName);
    $('#deleteForm').attr('action', `/personnel/delete/${personnelId}`);
    $('#deleteModal').modal('show');
}

$('#confirmDeleteBtn').on('click', function() {
    $('#deleteForm').submit();
});

function exportData() {
    // Implement export functionality
    window.location.href = '/api/personnel/export';
}
</script>
{% endblock %}
