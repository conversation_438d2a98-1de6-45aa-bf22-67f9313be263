<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة جدول التوزيعات</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* Print-specific styles */
        @media print {
            @page {
                size: A4 landscape;
                margin: 1cm;
            }

            .no-print {
                display: none !important;
            }

            body {
                font-family: Arial, sans-serif !important;
                font-size: 10pt !important;
                line-height: 1.2 !important;
                color: #000000 !important;
            }

            .print-table {
                font-size: 9pt !important;
                border-collapse: collapse !important;
            }

            .print-table th,
            .print-table td {
                border: 1px solid #000000 !important;
                padding: 4px !important;
                font-family: Arial, sans-serif !important;
            }

            .print-header {
                margin-bottom: 20px !important;
            }
        }

        /* Base styles */
        body {
            font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            background-color: #f8f9fa;
        }

        .container-fluid {
            max-width: 1400px;
        }

        /* Header styles */
        .print-header {
            background: linear-gradient(135deg, #2c5530 0%, #3a6b3e 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 8px rgba(44, 85, 48, 0.2);
        }

        .print-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
            text-align: center;
        }

        .print-header .subtitle {
            font-size: 1.1rem;
            text-align: center;
            margin-top: 10px;
            opacity: 0.9;
        }

        /* Filter section */
        .filter-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .filter-title {
            color: #2c5530;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .form-control, .form-select {
            font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #2c5530;
            box-shadow: 0 0 0 0.25rem rgba(44, 85, 48, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #2c5530 0%, #3a6b3e 100%);
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(44, 85, 48, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #34ce57 100%);
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
        }

        /* Table styles */
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .table-modern {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .table-modern thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            text-align: center;
            padding: 15px 12px;
            border: none;
            font-size: 0.9rem;
            position: relative;
        }

        .table-modern thead th:first-child {
            border-top-left-radius: 12px;
        }

        .table-modern thead th:last-child {
            border-top-right-radius: 12px;
        }

        .table-modern tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid #e9ecef;
        }

        .table-modern tbody tr:hover {
            background-color: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .table-modern tbody tr.selected {
            background-color: #e3f2fd !important;
            border-left: 4px solid #2196f3;
        }

        .table-modern tbody td {
            padding: 12px;
            vertical-align: middle;
            border: none;
            font-size: 0.9rem;
        }

        .user-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }

        .personnel-name {
            color: #2c3e50;
            font-weight: 600;
        }

        .personnel-grade {
            color: #6c757d;
            font-size: 0.8rem;
        }

        .bungalow-location {
            color: #6c757d;
            font-size: 0.8rem;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.4em 0.6em;
            font-weight: 500;
        }

        .btn-group .btn {
            margin: 0 1px;
            border-radius: 6px !important;
            transition: all 0.2s ease;
        }

        .btn-group .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .print-table {
            width: 100%;
            margin: 0;
            font-size: 0.9rem;
            border-collapse: collapse;
        }

        .print-table th {
            background: linear-gradient(135deg, #2c5530 0%, #3a6b3e 100%);
            color: white;
            font-weight: 600;
            padding: 15px 8px;
            text-align: center;
            border: 1px solid #2c5530;
            font-size: 0.85rem;
            white-space: nowrap;
        }

        .print-table td {
            padding: 12px 8px;
            border: 1px solid #dee2e6;
            vertical-align: middle;
            font-size: 0.85rem;
        }

        .print-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .print-table tbody tr:hover {
            background-color: #e8f5e8;
        }

        /* Status badges */
        .status-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
            white-space: nowrap;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-upcoming {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-expired {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-not-planned {
            background-color: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        /* Arabic text styling */
        .arabic-text {
            font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
            font-weight: 500;
            direction: rtl;
            text-align: right;
        }

        /* Summary section */
        .summary-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .summary-item {
            text-align: center;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            border: 2px solid #2c5530;
        }

        .summary-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c5530;
            display: block;
        }

        .summary-label {
            font-size: 0.9rem;
            color: #495057;
            margin-top: 5px;
            font-weight: 600;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .print-table {
                font-size: 0.75rem;
            }

            .print-table th,
            .print-table td {
                padding: 8px 4px;
            }
        }

        /* Loading spinner */
        .loading {
            text-align: center;
            padding: 50px;
        }

        .spinner-border {
            color: #2c5530;
        }
    </style>
</head>
<body>
    <!-- Print Header -->
    <div class="print-header">
        <h1><i class="fas fa-print me-3"></i>طباعة جدول التوزيعات</h1>
        <div class="subtitle">نظام إدارة البنغالوز - BANGHALAU</div>
    </div>

    <div class="container-fluid">
        <!-- Filter Section -->
        <div class="filter-section no-print">
            <h3 class="filter-title">
                <i class="fas fa-filter me-2"></i>فلترة التوزيعات
            </h3>

            <form id="filterForm">
                <div class="row">
                    <div class="col-md-3">
                        <label for="regionFilter" class="form-label">
                            <i class="fas fa-map-marker-alt me-1"></i>الناحية العسكرية
                        </label>
                        <select class="form-select" id="regionFilter" name="region">
                            <option value="">جميع النواحي</option>
                            <option value="الناحية العسكرية الأولى">الناحية العسكرية الأولى</option>
                            <option value="الناحية العسكرية الثانية">الناحية العسكرية الثانية</option>
                            <option value="الناحية العسكرية الثالثة">الناحية العسكرية الثالثة</option>
                            <option value="الناحية العسكرية الرابعة">الناحية العسكرية الرابعة</option>
                            <option value="الناحية العسكرية الخامسة">الناحية العسكرية الخامسة</option>
                            <option value="الناحية العسكرية السادسة">الناحية العسكرية السادسة</option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="uniteFilter" class="form-label">
                            <i class="fas fa-shield-alt me-1"></i>الوحدة العسكرية
                        </label>
                        <select class="form-select" id="uniteFilter" name="unite">
                            <option value="">جميع الوحدات</option>
                            <!-- Will be populated dynamically -->
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label for="statusFilter" class="form-label">
                            <i class="fas fa-info-circle me-1"></i>حالة التوزيع
                        </label>
                        <select class="form-select" id="statusFilter" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="upcoming">قادم</option>
                            <option value="expired">منتهي</option>
                            <option value="not_planned">غير مخطط</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label for="searchInput" class="form-label">
                            <i class="fas fa-search me-1"></i>البحث
                        </label>
                        <input type="text" class="form-control" id="searchInput" name="search"
                               placeholder="اسم، رقم، بنغالو...">
                    </div>

                    <div class="col-md-2 d-flex align-items-end">
                        <div class="d-grid w-100">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-1"></i>تطبيق الفلتر
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12 text-center">
                        <button type="button" class="btn btn-success me-2" onclick="printTable()">
                            <i class="fas fa-print me-1"></i>طباعة الجدول
                        </button>
                        <button type="button" class="btn btn-info me-2" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                            <i class="fas fa-undo me-1"></i>إعادة تعيين
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Summary Section -->
        <div class="summary-section" id="summarySection">
            <h4 class="text-center mb-3">
                <i class="fas fa-chart-bar me-2"></i>ملخص التوزيعات
            </h4>
            <div class="summary-grid">
                <div class="summary-item">
                    <span class="summary-number" id="totalDistributions">0</span>
                    <div class="summary-label">إجمالي التوزيعات</div>
                </div>
                <div class="summary-item">
                    <span class="summary-number" id="activeDistributions">0</span>
                    <div class="summary-label">التوزيعات النشطة</div>
                </div>
                <div class="summary-item">
                    <span class="summary-number" id="upcomingDistributions">0</span>
                    <div class="summary-label">التوزيعات القادمة</div>
                </div>
                <div class="summary-item">
                    <span class="summary-number" id="expiredDistributions">0</span>
                    <div class="summary-label">التوزيعات المنتهية</div>
                </div>
            </div>
        </div>

        <!-- Table Container -->
        <div class="table-container">
            <div class="loading" id="loadingSpinner">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل البيانات...</p>
            </div>

            <div id="tableContainer" style="display: none;">
                <table class="table table-modern print-table">
                    <thead>
                        <tr>
                            <th style="width: 40px;">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    <label class="form-check-label" for="selectAll"></label>
                                </div>
                            </th>
                            <th><i class="fas fa-hashtag me-1"></i>Numéro</th>
                            <th><i class="fas fa-home me-1"></i>Bungalow</th>
                            <th><i class="fas fa-user me-1"></i>Personnel</th>
                            <th><i class="fas fa-shield-alt me-1"></i>Unité</th>
                            <th><i class="fas fa-calendar me-1"></i>Session</th>
                            <th><i class="fas fa-calendar-alt me-1"></i>Période</th>
                            <th><i class="fas fa-info-circle me-1"></i>Statut</th>
                            <th><i class="fas fa-cogs me-1"></i>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="distributionsTableBody">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        let allDistributions = [];
        let filteredDistributions = [];
        let allUnites = [];

        $(document).ready(function() {
            loadUnites();
            loadDistributions();

            // Form submission
            $('#filterForm').on('submit', function(e) {
                e.preventDefault();
                applyFilters();
            });

            // Real-time search
            $('#searchInput').on('input', function() {
                applyFilters();
            });

            // Filter changes
            $('#regionFilter, #uniteFilter, #statusFilter').on('change', function() {
                applyFilters();
            });
        });

        // Load units for filter
        function loadUnites() {
            $.get('/api/unites', function(data) {
                allUnites = data;
                const uniteSelect = $('#uniteFilter');
                uniteSelect.empty().append('<option value="">جميع الوحدات</option>');

                data.forEach(function(unite) {
                    uniteSelect.append(`<option value="${unite.id}">${unite.description}</option>`);
                });
            }).fail(function() {
                console.error('Failed to load units');
            });
        }

        // Load distributions data
        function loadDistributions() {
            $.get('/api/distributions/detailed', function(data) {
                allDistributions = data;
                filteredDistributions = data;
                updateTable();
                updateSummary();
            }).fail(function() {
                alert('فشل في تحميل البيانات. يرجى المحاولة مرة أخرى.');
            });
        }

        // Apply filters
        function applyFilters() {
            const region = $('#regionFilter').val();
            const unite = $('#uniteFilter').val();
            const status = $('#statusFilter').val();
            const search = $('#searchInput').val().toLowerCase();

            filteredDistributions = allDistributions.filter(function(dist) {
                // Region filter
                if (region && dist.region !== region) return false;

                // Unite filter
                if (unite && dist.unite_id != unite) return false;

                // Status filter
                if (status && dist.status !== status) return false;

                // Search filter
                if (search) {
                    const searchFields = [
                        dist.numero,
                        dist.personnel_nom,
                        dist.personnel_prenom,
                        dist.personnel_grade,
                        dist.unite_description,
                        dist.bungalow_numero,
                        dist.bungalow_endroit
                    ].join(' ').toLowerCase();

                    if (!searchFields.includes(search)) return false;
                }

                return true;
            });

            updateTable();
            updateSummary();
        }

        // Update table with filtered data
        function updateTable() {
            const tbody = $('#distributionsTableBody');
            tbody.empty();

            if (filteredDistributions.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-exchange-alt fa-3x mb-3"></i>
                                <h5>Aucune distribution trouvée</h5>
                                <p>Aucune distribution ne correspond aux critères de filtrage.</p>
                            </div>
                        </td>
                    </tr>
                `);
                return;
            }

            filteredDistributions.forEach(function(dist, index) {
                const statusClass = getStatusClass(dist.status);
                const statusText = getStatusText(dist.status);

                const statusData = dist.date_fin && dist.date_fin < new Date().toISOString().split('T')[0] ? 'expired' :
                                  dist.date_debut && dist.date_debut > new Date().toISOString().split('T')[0] ? 'upcoming' : 'active';

                let statusBadge = '';
                if (!dist.date_debut) {
                    statusBadge = '<span class="badge bg-secondary">Non planifiée</span>';
                } else if (dist.date_debut > new Date().toISOString().split('T')[0]) {
                    statusBadge = '<span class="badge bg-info">À venir</span>';
                } else if (!dist.date_fin || dist.date_fin >= new Date().toISOString().split('T')[0]) {
                    statusBadge = '<span class="badge bg-success">Active</span>';
                } else {
                    statusBadge = '<span class="badge bg-danger">Expirée</span>';
                }

                tbody.append(`
                    <tr data-status="${statusData}" data-distribution-id="${dist.id}" data-unite-id="${dist.unite_id || ''}">
                        <td>
                            <div class="form-check">
                                <input class="form-check-input distribution-checkbox" type="checkbox" value="${dist.id}" id="dist_${dist.id}" onchange="updateSelectAllState()">
                                <label class="form-check-label" for="dist_${dist.id}"></label>
                            </div>
                        </td>
                        <td>
                            <strong class="text-primary">${dist.numero || '-'}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-home text-primary me-2"></i>
                                <div>
                                    <strong>${dist.bungalow_numero || 'Non défini'}</strong>
                                    ${dist.bungalow_endroit ? `<small class="text-muted d-block bungalow-location">${dist.bungalow_endroit}</small>` : ''}
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <strong class="personnel-name">${dist.personnel_nom || 'Non défini'} ${dist.personnel_prenom || ''}</strong>
                                    ${dist.personnel_grade ? `<small class="text-muted d-block personnel-grade">${dist.personnel_grade}</small>` : ''}
                                </div>
                            </div>
                        </td>
                        <td>
                            ${dist.unite_description ?
                                `<span class="badge bg-secondary"><i class="fas fa-shield-alt me-1"></i>${dist.unite_description}</span>` :
                                '<span class="text-muted">Non définie</span>'
                            }
                        </td>
                        <td>
                            ${dist.session_numero ?
                                `<span class="badge bg-info"><i class="fas fa-calendar me-1"></i>${dist.session_numero}</span>` :
                                '<span class="text-muted">Non définie</span>'
                            }
                        </td>
                        <td>
                            <div class="small">
                                ${dist.date_debut ? `<div><i class="fas fa-play text-success me-1"></i>${dist.date_debut}</div>` : ''}
                                ${dist.date_fin ?
                                    `<div><i class="fas fa-stop text-danger me-1"></i>${dist.date_fin}</div>` :
                                    '<div><i class="fas fa-infinity text-warning me-1"></i>Indéterminée</div>'
                                }
                            </div>
                        </td>
                        <td>
                            ${statusBadge}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-primary"
                                        onclick="printSingleDistribution(${dist.id})"
                                        title="Imprimer">
                                    <i class="fas fa-print"></i>
                                </button>
                                <a href="/distributions/edit/${dist.id}"
                                   class="btn btn-sm btn-outline-warning"
                                   title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-info"
                                        onclick="viewDetails(${dist.id})"
                                        title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger"
                                        onclick="confirmDelete(${dist.id}, '${dist.numero}')"
                                        title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `);
            });
        }

        // Update summary statistics
        function updateSummary() {
            const total = filteredDistributions.length;
            const active = filteredDistributions.filter(d => d.status === 'active').length;
            const upcoming = filteredDistributions.filter(d => d.status === 'upcoming').length;
            const expired = filteredDistributions.filter(d => d.status === 'expired').length;

            $('#totalDistributions').text(total);
            $('#activeDistributions').text(active);
            $('#upcomingDistributions').text(upcoming);
            $('#expiredDistributions').text(expired);
        }

        // Get status CSS class
        function getStatusClass(status) {
            switch(status) {
                case 'active': return 'status-active';
                case 'upcoming': return 'status-upcoming';
                case 'expired': return 'status-expired';
                default: return 'status-not-planned';
            }
        }

        // Get status text in Arabic
        function getStatusText(status) {
            switch(status) {
                case 'active': return 'نشط';
                case 'upcoming': return 'قادم';
                case 'expired': return 'منتهي';
                default: return 'غير مخطط';
            }
        }

        // Print table function
        function printTable() {
            // Hide no-print elements
            $('.no-print').hide();

            // Print the page
            window.print();

            // Show no-print elements again
            $('.no-print').show();
        }

        // Export to Excel function
        function exportToExcel() {
            const params = new URLSearchParams();
            params.append('region', $('#regionFilter').val());
            params.append('unite', $('#uniteFilter').val());
            params.append('status', $('#statusFilter').val());
            params.append('search', $('#searchInput').val());

            window.location.href = '/api/distributions/export/excel?' + params.toString();
        }

        // Reset filters
        function resetFilters() {
            $('#filterForm')[0].reset();
            filteredDistributions = allDistributions;
            updateTable();
            updateSummary();
        }

        // Print page setup
        window.addEventListener('beforeprint', function() {
            document.body.style.fontSize = '10pt';
            document.body.style.lineHeight = '1.2';
        });

        window.addEventListener('afterprint', function() {
            document.body.style.fontSize = '';
            document.body.style.lineHeight = '';
        });

        // Checkbox functionality
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const distributionCheckboxes = document.querySelectorAll('.distribution-checkbox');

            distributionCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });

            updateSelectAllState();
        }

        function updateSelectAllState() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const distributionCheckboxes = document.querySelectorAll('.distribution-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.distribution-checkbox:checked');

            if (checkedCheckboxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedCheckboxes.length === distributionCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllCheckbox.checked = false;
            }

            // Update row highlighting
            distributionCheckboxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                if (checkbox.checked) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }
            });
        }

        // Action functions
        function printSingleDistribution(distributionId) {
            window.open(`/print/distribution/${distributionId}`, '_blank');
        }

        function viewDetails(distributionId) {
            window.location.href = `/distributions/view/${distributionId}`;
        }

        function confirmDelete(distributionId, distributionName) {
            if (confirm(`Êtes-vous sûr de vouloir supprimer la distribution "${distributionName}" ?\n\nCette action est irréversible.`)) {
                // Create and submit delete form
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/distributions/delete/${distributionId}`;

                // Add CSRF token if needed
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (csrfToken) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrf_token';
                    csrfInput.value = csrfToken.getAttribute('content');
                    form.appendChild(csrfInput);
                }

                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
