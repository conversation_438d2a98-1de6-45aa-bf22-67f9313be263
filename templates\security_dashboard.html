{% extends "base.html" %}

{% block title %}Gestion de la Sécurité - BANGHALAU{% endblock %}

{% block extra_css %}
<style>
/* Security Dashboard Styling */
.security-header {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(220, 53, 69, 0.3);
}

.security-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    border-left: 4px solid #dc3545;
    transition: all 0.3s ease;
}

.security-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.12);
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-active {
    background-color: #28a745;
    animation: pulse 2s infinite;
}

.status-inactive {
    background-color: #dc3545;
}

.status-warning {
    background-color: #ffc107;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.metric-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-top: 3px solid #dc3545;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #dc3545;
    margin-bottom: 0.5rem;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.threat-item {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
}

.blocked-ip {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.btn-security {
    background: #dc3545;
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.btn-security:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.security-log {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.log-entry {
    margin-bottom: 0.5rem;
    padding: 0.25rem;
    border-radius: 4px;
}

.log-success {
    background: #d4edda;
    color: #155724;
}

.log-warning {
    background: #fff3cd;
    color: #856404;
}

.log-error {
    background: #f8d7da;
    color: #721c24;
}

.arabic-text {
    font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
    direction: rtl;
    text-align: right;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Security Header -->
    <div class="security-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-2">
                    <i class="fas fa-shield-alt me-2"></i>Gestion de la Sécurité de la Base de Données
                </h1>
                <p class="mb-0">Surveillance et protection complète du système BANGHALAU</p>
            </div>
            <div class="text-end">
                <span class="status-indicator status-active"></span>
                <span>Système Protégé</span>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Security Metrics -->
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ protection_status.blocked_ips|length }}</div>
                <div class="metric-label">Adresses IP Bloquées</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ protection_status.failed_attempts.values()|sum }}</div>
                <div class="metric-label">Tentatives Échouées</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ (protection_status.database_size / 1024 / 1024)|round(1) }}</div>
                <div class="metric-label">Taille Base de Données (MB)</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ protection_status.integrity_issues|length }}</div>
                <div class="metric-label">Problèmes d'Intégrité</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Protection Status -->
        <div class="col-md-6">
            <div class="security-card">
                <h5><i class="fas fa-shield-alt me-2"></i>État de la Protection</h5>
                <div class="mt-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Surveillance Active</span>
                        <span class="status-indicator {% if protection_status.monitoring_active %}status-active{% else %}status-inactive{% endif %}"></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Chiffrement des Sauvegardes</span>
                        <span class="status-indicator {% if security_report.security_config.backup_encryption %}status-active{% else %}status-inactive{% endif %}"></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Journalisation des Événements</span>
                        <span class="status-indicator {% if security_report.security_config.audit_logging %}status-active{% else %}status-inactive{% endif %}"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Blocked IPs -->
        <div class="col-md-6">
            <div class="security-card">
                <h5><i class="fas fa-ban me-2"></i>Adresses IP Bloquées</h5>
                <div class="mt-3">
                    {% if protection_status.blocked_ips %}
                        {% for ip in protection_status.blocked_ips %}
                        <div class="blocked-ip">
                            <span>{{ ip }}</span>
                            <button class="btn btn-sm btn-outline-danger" onclick="unblockIP('{{ ip }}')">
                                Débloquer
                            </button>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune adresse IP bloquée</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Backup Management -->
        <div class="col-md-6">
            <div class="security-card">
                <h5><i class="fas fa-database me-2"></i>Gestion des Sauvegardes</h5>
                <form id="backupForm" class="mt-3">
                    <div class="mb-3">
                        <label for="backup_name" class="form-label">Nom de la Sauvegarde</label>
                        <input type="text" class="form-control" id="backup_name" name="backup_name"
                               placeholder="backup_20241220_143000">
                    </div>
                    <div class="mb-3">
                        <label for="backup_password" class="form-label">Mot de Passe de Chiffrement</label>
                        <input type="password" class="form-control" id="backup_password" name="password" required>
                        <div class="form-text">Doit contenir au moins 12 caractères avec majuscules, minuscules, chiffres et symboles</div>
                    </div>
                    <button type="submit" class="btn btn-security">
                        <i class="fas fa-lock me-2"></i>Créer une Sauvegarde Chiffrée
                    </button>
                </form>
            </div>
        </div>

        <!-- Security Logs -->
        <div class="col-md-6">
            <div class="security-card">
                <h5><i class="fas fa-file-alt me-2"></i>Journal des Événements de Sécurité</h5>
                <div class="security-log mt-3">
                    {% if security_report.recent_threats %}
                        {% for threat in security_report.recent_threats %}
                        <div class="log-entry log-error">{{ threat }}</div>
                        {% endfor %}
                    {% else %}
                        <div class="log-entry log-success">Aucune menace récente</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Integrity Issues -->
    {% if protection_status.integrity_issues %}
    <div class="row">
        <div class="col-12">
            <div class="security-card">
                <h5><i class="fas fa-exclamation-triangle me-2 text-warning"></i>Problèmes d'Intégrité de la Base de Données</h5>
                <div class="mt-3">
                    {% for issue in protection_status.integrity_issues %}
                    <div class="threat-item">
                        <i class="fas fa-exclamation-circle me-2 text-warning"></i>{{ issue }}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Security Recommendations -->
    {% if security_report.recommendations %}
    <div class="row">
        <div class="col-12">
            <div class="security-card">
                <h5><i class="fas fa-lightbulb me-2 text-info"></i>Recommandations de Sécurité</h5>
                <div class="mt-3">
                    {% for recommendation in security_report.recommendations %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>{{ recommendation }}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Notification Container -->
<div id="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set default backup name with current timestamp
    const now = new Date();
    const timestamp = now.getFullYear() +
                     String(now.getMonth() + 1).padStart(2, '0') +
                     String(now.getDate()).padStart(2, '0') + '_' +
                     String(now.getHours()).padStart(2, '0') +
                     String(now.getMinutes()).padStart(2, '0') +
                     String(now.getSeconds()).padStart(2, '0');
    $('#backup_name').attr('placeholder', `backup_${timestamp}`);

    // Backup form submission
    $('#backupForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Création en cours...').prop('disabled', true);
        
        $.ajax({
            url: '/security/backup',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification('Sauvegarde chiffrée créée avec succès', 'success');
                    $('#backupForm')[0].reset();
                } else {
                    showNotification(response.message, 'error');
                    if (response.errors) {
                        response.errors.forEach(error => {
                            showNotification(error, 'warning');
                        });
                    }
                }
            },
            error: function() {
                showNotification('Erreur lors de la création de la sauvegarde', 'error');
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
});

function unblockIP(ip) {
    if (!confirm(`Êtes-vous sûr de vouloir débloquer l'adresse IP : ${ip} ?`)) {
        return;
    }
    
    $.ajax({
        url: '/security/unblock_ip',
        method: 'POST',
        data: { ip_address: ip },
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                location.reload(); // Refresh to update the list
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('Erreur lors du déblocage de l\'adresse IP', 'error');
        }
    });
}

function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const notification = $(`
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('#notification-container').append(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.fadeOut(() => notification.remove());
    }, 5000);
}
</script>
{% endblock %}
