{% extends "base.html" %}

{% block title %}Sessions - BANGHALAU{% endblock %}

{% block extra_css %}
<!-- استيراد الخطوط العربية المحسنة -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
/* خطوط عربية محسنة لصفحة Sessions */
:root {
    --arabic-font-primary: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --arabic-font-secondary: '<PERSON><PERSON><PERSON>', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
    --mixed-font: 'Cairo', 'Noto Sans Arabic', 'Inter', 'Segoe UI', sans-serif;
}

/* تحسين عام للنصوص */
body, .table, .card, .btn, .form-control, .modal-content {
    font-family: var(--mixed-font);
    font-feature-settings: 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* العناوين */
.page-title, .card-title, .modal-title, h1, h2, h3, h4, h5, h6 {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    line-height: 1.3;
}

/* الجداول */
.table th {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    font-size: 0.95rem;
}

.table td {
    font-family: var(--mixed-font);
    font-weight: 400;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* الشارات والتسميات */
.badge {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* التنبيهات */
.alert {
    font-family: var(--mixed-font);
    font-weight: 400;
    line-height: 1.5;
}

/* الأزرار */
.btn {
    font-family: var(--mixed-font);
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* النماذج */
.form-control, .form-select {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
}

.form-label {
    font-family: var(--mixed-font);
    font-weight: 500;
}

/* النصوص العربية المخصصة */
.arabic-text {
    font-family: var(--arabic-font-primary);
    font-weight: 500;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
}

.mixed-text {
    font-family: var(--mixed-font);
    font-weight: 400;
    direction: auto;
    text-align: start;
    line-height: 1.5;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .table th, .table td {
        font-size: 0.85rem;
    }

    .page-title, .card-title {
        font-size: 1.1rem;
    }
}

/* تحسين للطباعة */
@media print {
    body, .table, .card, .btn, .form-control, .modal-content {
        font-family: 'Cairo', 'Noto Sans Arabic', serif;
        color: #000;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gradient">
                        <i class="fas fa-calendar-alt me-2"></i>Sessions
                    </h1>
                    <p class="text-muted mb-0">Gérez toutes les sessions de formation</p>
                </div>
                <a href="{{ url_for('add_session') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Nouvelle Session
                </a>
            </div>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'exclamation-circle' }} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Search and Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-modern">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-5">
                            <label for="search" class="form-label">
                                <i class="fas fa-search me-1"></i>Rechercher
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="search" 
                                   name="search" 
                                   value="{{ search }}"
                                   placeholder="Numéro, description ou statut...">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">
                                <i class="fas fa-filter me-1"></i>Statut
                            </label>
                            <select class="form-control" id="status" name="status">
                                <option value="">Tous les statuts</option>
                                <option value="Planifiée" {% if status_filter == 'Planifiée' %}selected{% endif %}>Planifiée</option>
                                <option value="En cours" {% if status_filter == 'En cours' %}selected{% endif %}>En cours</option>
                                <option value="Terminée" {% if status_filter == 'Terminée' %}selected{% endif %}>Terminée</option>
                                <option value="Annulée" {% if status_filter == 'Annulée' %}selected{% endif %}>Annulée</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <div class="btn-group w-100">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Rechercher
                                </button>
                                <a href="{{ url_for('sessions') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Reset
                                </a>
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-success w-100" onclick="exportData()">
                                <i class="fas fa-download me-1"></i>Exporter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="card-modern">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Liste des Sessions
                    {% if search %}
                        <small class="text-muted">(Résultats pour "{{ search }}")</small>
                    {% endif %}
                    {% if status_filter %}
                        <small class="text-muted">(Statut: {{ status_filter }})</small>
                    {% endif %}
                </h5>
                <span class="badge bg-primary">{{ sessions|length }} session(s)</span>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-modern" id="sessionsTable">
                <thead>
                    <tr>
                        <th><i class="fas fa-hashtag me-1"></i>Numéro</th>
                        <th><i class="fas fa-info-circle me-1"></i>Description</th>
                        <th><i class="fas fa-calendar-start me-1"></i>Date Début</th>
                        <th><i class="fas fa-calendar-end me-1"></i>Date Fin</th>
                        <th><i class="fas fa-chart-pie me-1"></i>Statut</th>
                        <th><i class="fas fa-home me-1"></i>Distributions</th>
                        <th><i class="fas fa-cogs me-1"></i>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for session in sessions %}
                    <tr>
                        <td>
                            <strong class="text-primary">{{ session.numero }}</strong>
                        </td>
                        <td>
                            {% if session.description %}
                                <span class="text-truncate" style="max-width: 200px;" title="{{ session.description }}">
                                    {{ session.description }}
                                </span>
                            {% else %}
                                <span class="text-muted">Aucune description</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-calendar text-success me-2"></i>
                                <span>{{ session.date_debut }}</span>
                            </div>
                        </td>
                        <td>
                            {% if session.date_fin %}
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calendar text-danger me-2"></i>
                                    <span>{{ session.date_fin }}</span>
                                </div>
                            {% else %}
                                <span class="text-muted">Non définie</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if session.etat_session %}
                                {% set status_class = 'primary' if session.etat_session == 'Planifiée' else 'success' if session.etat_session == 'En cours' else 'secondary' if session.etat_session == 'Terminée' else 'danger' %}
                                <span class="badge bg-{{ status_class }}">
                                    <i class="fas fa-{{ 'clock' if session.etat_session == 'Planifiée' else 'play' if session.etat_session == 'En cours' else 'check' if session.etat_session == 'Terminée' else 'times' }} me-1"></i>
                                    {{ session.etat_session }}
                                </span>
                            {% else %}
                                <span class="text-muted">Non défini</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-{{ 'success' if session.distribution_count > 0 else 'secondary' }}">
                                    <i class="fas fa-home me-1"></i>{{ session.distribution_count }} dist.
                                </span>
                            </div>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('edit_session', session_id=session.id) }}" 
                                   class="btn btn-sm btn-outline-warning" 
                                   title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-info" 
                                        onclick="viewDetails({{ session.id }})"
                                        title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="confirmDelete({{ session.id }}, '{{ session.numero }}', {{ session.distribution_count }})"
                                        title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                                <h5>Aucune session trouvée</h5>
                                {% if search or status_filter %}
                                    <p>Aucun résultat pour les critères de recherche.</p>
                                    <a href="{{ url_for('sessions') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-times me-1"></i>Effacer les filtres
                                    </a>
                                {% else %}
                                    <p>Commencez par ajouter votre première session.</p>
                                    <a href="{{ url_for('add_session') }}" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>Ajouter une Session
                                    </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Confirmer la Suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la session <strong id="sessionNumero"></strong> ?</p>
                <div id="distributionWarning" class="alert alert-warning" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Attention :</strong> Cette session est utilisée dans <span id="distributionCount"></span> distribution(s). 
                    Vous devez d'abord supprimer ces distributions.
                </div>
                <p class="text-danger">
                    <i class="fas fa-warning me-1"></i>
                    Cette action est irréversible.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>Supprimer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle text-primary me-2"></i>
                    Détails de la Session
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailsContent">
                <!-- Details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Fermer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden delete form -->
<form id="deleteForm" method="POST" style="display: none;"></form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Real-time search
    $('#search').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        $('#sessionsTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
    
    // Table row hover effects
    $('#sessionsTable tbody tr').hover(
        function() {
            $(this).addClass('table-hover-effect');
        },
        function() {
            $(this).removeClass('table-hover-effect');
        }
    );
});

function viewDetails(sessionId) {
    // Load session details via AJAX
    $.get(`/api/sessions/${sessionId}`)
        .done(function(data) {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-hashtag me-2"></i>Numéro</h6>
                        <p class="text-muted">${data.numero}</p>
                        
                        <h6><i class="fas fa-info-circle me-2"></i>Description</h6>
                        <p class="text-muted">${data.description || 'Aucune description'}</p>
                        
                        <h6><i class="fas fa-chart-pie me-2"></i>Statut</h6>
                        <p class="text-muted">${data.etat_session || 'Non défini'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-calendar-start me-2"></i>Date de Début</h6>
                        <p class="text-muted">${data.date_debut}</p>
                        
                        <h6><i class="fas fa-calendar-end me-2"></i>Date de Fin</h6>
                        <p class="text-muted">${data.date_fin || 'Non définie'}</p>
                        
                        <h6><i class="fas fa-home me-2"></i>Distributions</h6>
                        <p class="text-muted">${data.distribution_count || 0} distribution(s)</p>
                    </div>
                </div>
            `;
            $('#detailsContent').html(content);
            $('#detailsModal').modal('show');
        })
        .fail(function() {
            showToast('Erreur lors du chargement des détails', 'error');
        });
}

function confirmDelete(sessionId, sessionNumero, distributionCount) {
    $('#sessionNumero').text(sessionNumero);
    $('#distributionCount').text(distributionCount);
    $('#deleteForm').attr('action', `/sessions/delete/${sessionId}`);
    
    if (distributionCount > 0) {
        $('#distributionWarning').show();
        $('#confirmDeleteBtn').prop('disabled', true).addClass('disabled');
    } else {
        $('#distributionWarning').hide();
        $('#confirmDeleteBtn').prop('disabled', false).removeClass('disabled');
    }
    
    $('#deleteModal').modal('show');
}

$('#confirmDeleteBtn').on('click', function() {
    if (!$(this).prop('disabled')) {
        $('#deleteForm').submit();
    }
});

function exportData() {
    // Create CSV export
    const table = document.getElementById('sessionsTable');
    const rows = table.querySelectorAll('tr');
    let csv = [];
    
    for (let i = 0; i < rows.length; i++) {
        const row = [], cols = rows[i].querySelectorAll('td, th');
        
        for (let j = 0; j < cols.length - 1; j++) { // Exclude actions column
            row.push(cols[j].innerText);
        }
        csv.push(row.join(','));
    }
    
    const csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
    const downloadLink = document.createElement('a');
    downloadLink.download = `sessions_${new Date().toISOString().split('T')[0]}.csv`;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
    
    showToast('Export terminé avec succès', 'success');
}

// Show toast notification
function showToast(message, type = 'info') {
    const toast = $(`
        <div class="toast-notification ${type}">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
        </div>
    `);
    
    $('body').append(toast);
    
    setTimeout(() => {
        toast.fadeOut(() => toast.remove());
    }, 3000);
}
</script>

<style>
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    animation: slideInRight 0.3s ease;
}

.toast-notification.success {
    background: #10b981;
}

.toast-notification.error {
    background: #ef4444;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
{% endblock %}
