{% extends "base.html" %}

{% block title %}Gestion des Utilisateurs - BANGHALAU{% endblock %}

{% block content %}
<div class="container-fluid animate-fade-in-up">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="users-header">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h1 class="display-6 fw-bold text-white mb-2">
                            <i class="fas fa-users-cog me-3"></i>Gestion des Utilisateurs
                        </h1>
                        <p class="text-white-50 mb-0">Ajouter, modifier et supprimer les utilisateurs du système</p>
                    </div>
                    <div>
                        <button class="btn btn-light me-2" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-user-plus me-2"></i>Ajouter un utilisateur
                        </button>
                        <button class="btn btn-outline-light" onclick="refreshUsersList()">
                            <i class="fas fa-sync-alt me-2"></i>Actualiser
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card bg-gradient-primary">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalUsers">{{ users|length }}</h3>
                    <p>Total Utilisateurs</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-gradient-success">
                <div class="stat-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-content">
                    <h3 id="activeUsers">{{ active_users_count }}</h3>
                    <p>Utilisateurs Actifs</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-gradient-warning">
                <div class="stat-icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="stat-content">
                    <h3 id="adminUsers">{{ admin_users_count }}</h3>
                    <p>Administrateurs</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-gradient-info">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3 id="recentLogins">{{ recent_logins_count }}</h3>
                    <p>Connexions Récentes (24h)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list me-2 text-primary"></i>Liste des Utilisateurs</h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control form-control-sm" id="searchUsers" placeholder="Rechercher des utilisateurs..." style="width: 250px;">
                        <select class="form-select form-select-sm" id="filterRole" style="width: 150px;">
                            <option value="">Tous les rôles</option>
                            <option value="admin">Administrateur</option>
                            <option value="user">Utilisateur</option>
                        </select>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover table-modern mb-0" id="usersTable">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="fas fa-hashtag me-1"></i>ID</th>
                                    <th><i class="fas fa-user me-1"></i>Nom d'utilisateur</th>
                                    <th><i class="fas fa-envelope me-1"></i>Email</th>
                                    <th><i class="fas fa-user-tag me-1"></i>Rôle</th>
                                    <th><i class="fas fa-toggle-on me-1"></i>Statut</th>
                                    <th><i class="fas fa-clock me-1"></i>Dernière connexion</th>
                                    <th><i class="fas fa-calendar me-1"></i>Date de création</th>
                                    <th><i class="fas fa-cogs me-1"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr data-user-id="{{ user.id }}">
                                    <td>{{ user.id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-2">
                                                <i class="fas fa-user-circle text-primary"></i>
                                            </div>
                                            <div>
                                                <strong>{{ user.username }}</strong>
                                                {% if user.full_name %}
                                                <br><small class="text-muted">{{ user.full_name }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ user.email or 'Non spécifié' }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if user.role == 'admin' else 'primary' }}">
                                            <i class="fas fa-{{ 'user-shield' if user.role == 'admin' else 'user' }} me-1"></i>
                                            {{ 'Administrateur' if user.role == 'admin' else 'Utilisateur' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if user.is_active else 'secondary' }}">
                                            <i class="fas fa-{{ 'check' if user.is_active else 'times' }} me-1"></i>
                                            {{ 'Actif' if user.is_active else 'Inactif' }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if user.last_login %}
                                            <small>{{ user.last_login[:16] if user.last_login else 'Jamais connecté' }}</small>
                                        {% else %}
                                            <small class="text-muted">Jamais connecté</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ user.created_at[:10] if user.created_at else 'Non spécifié' }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="editUser({{ user.id }})" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning" onclick="toggleUserStatus({{ user.id }})" title="{{ 'Désactiver' if user.is_active else 'Activer' }}">
                                                <i class="fas fa-{{ 'user-slash' if user.is_active else 'user-check' }}"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" onclick="resetPassword({{ user.id }})" title="Réinitialiser le mot de passe">
                                                <i class="fas fa-key"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser({{ user.id }})" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>Ajouter un nouvel utilisateur
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm" onsubmit="return submitAddUser(event)">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nom d'utilisateur <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="username" required>
                                <div class="form-text">Doit être unique et sans espaces</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nom complet</label>
                                <input type="text" class="form-control" name="full_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Rôle <span class="text-danger">*</span></label>
                                <select class="form-select" name="role" required>
                                    <option value="user">Utilisateur standard</option>
                                    <option value="admin">Administrateur système</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Mot de passe <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" class="form-control" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword(this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">Doit contenir au moins 6 caractères</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Confirmer le mot de passe <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="confirm_password" required>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" checked>
                                <label class="form-check-label">
                                    Activer l'utilisateur lors de la création
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Ajouter l'utilisateur
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit me-2"></i>Modifier l'utilisateur
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editUserForm" onsubmit="return handleEditUser(event)">
                <input type="hidden" name="user_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nom d'utilisateur <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nom complet</label>
                                <input type="text" class="form-control" name="full_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Rôle <span class="text-danger">*</span></label>
                                <select class="form-select" name="role" required>
                                    <option value="user">Utilisateur</option>
                                    <option value="admin">Administrateur</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active">
                                <label class="form-check-label">
                                    Utilisateur actif
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-2"></i>Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>Réinitialiser le mot de passe
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="resetPasswordForm">
                <input type="hidden" name="user_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Le mot de passe de l'utilisateur sélectionné sera réinitialisé
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Nouveau mot de passe <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" name="new_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword(this)">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Confirmer le mot de passe <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" name="confirm_new_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-key me-2"></i>Réinitialiser
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Users Management Header */
.users-header {
    background: linear-gradient(135deg, #059669 0%, #10b981 50%, #34d399 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(5, 150, 105, 0.3);
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient);
}

.bg-gradient-primary::before {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.bg-gradient-success::before {
    background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
}

.bg-gradient-warning::before {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

.bg-gradient-info::before {
    background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
}

.stat-card .stat-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    opacity: 0.8;
}

.bg-gradient-primary .stat-icon {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.bg-gradient-success .stat-icon {
    background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
}

.bg-gradient-warning .stat-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

.bg-gradient-info .stat-icon {
    background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.stat-content p {
    margin: 0;
    color: #6b7280;
    font-weight: 500;
}

/* Table Styles */
.table-modern {
    border-radius: 15px;
    overflow: hidden;
}

.table-modern thead th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: none;
    font-weight: 600;
    color: #374151;
    padding: 1rem;
}

.table-modern tbody tr {
    transition: all 0.3s ease;
}

.table-modern tbody tr:hover {
    background: rgba(5, 150, 105, 0.05);
    transform: scale(1.01);
}

.table-modern td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #f1f5f9;
}

/* User Avatar */
.user-avatar {
    font-size: 2rem;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
}

/* Button Groups */
.btn-group .btn {
    border-radius: 8px;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.btn-group .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-radius: 15px 15px 0 0;
    border-bottom: none;
    padding: 1.5rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: none;
    padding: 1.5rem;
}

/* Form Styles */
.form-control:focus {
    border-color: #059669;
    box-shadow: 0 0 0 0.2rem rgba(5, 150, 105, 0.25);
}

.form-select:focus {
    border-color: #059669;
    box-shadow: 0 0 0 0.2rem rgba(5, 150, 105, 0.25);
}

.form-check-input:checked {
    background-color: #059669;
    border-color: #059669;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Simple form submission functions
function submitAddUser(event) {
    event.preventDefault();
    console.log('Add user form submitted');

    const form = event.target;
    const formData = new FormData(form);
    const userData = Object.fromEntries(formData.entries());

    // Basic validation
    if (!userData.username || !userData.password || !userData.role) {
        alert('Veuillez remplir tous les champs obligatoires');
        return false;
    }

    if (userData.password !== userData.confirm_password) {
        alert('Les mots de passe ne correspondent pas');
        return false;
    }

    // Send request
    fetch('/api/users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: userData.username,
            full_name: userData.full_name,
            email: userData.email,
            password: userData.password,
            role: userData.role,
            is_active: userData.is_active === 'on'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Utilisateur ajouté avec succès');
            location.reload();
        } else {
            alert(data.message || 'Erreur lors de l\'ajout');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erreur de connexion');
    });

    return false;
}

// Global variables
let currentEditUserId = null;
let currentResetUserId = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing...');

    // Simple direct event listeners
    const addForm = document.getElementById('addUserForm');
    if (addForm) {
        addForm.onsubmit = function(e) {
            e.preventDefault();
            console.log('Add form submitted!');
            handleAddUser(e);
        };
        console.log('Add form listener attached directly');
    }

    const editForm = document.getElementById('editUserForm');
    if (editForm) {
        editForm.onsubmit = function(e) {
            e.preventDefault();
            console.log('Edit form submitted!');
            handleEditUser(e);
        };
        console.log('Edit form listener attached directly');
    }

    const resetForm = document.getElementById('resetPasswordForm');
    if (resetForm) {
        resetForm.onsubmit = function(e) {
            e.preventDefault();
            console.log('Reset form submitted!');
            handleResetPassword(e);
        };
        console.log('Reset form listener attached directly');
    }

    // Search and filter
    const searchInput = document.getElementById('searchUsers');
    if (searchInput) {
        searchInput.oninput = filterUsers;
    }

    const roleFilter = document.getElementById('filterRole');
    if (roleFilter) {
        roleFilter.onchange = filterUsers;
    }

    console.log('Initialization complete');
});

// Utility functions - removed duplicates

// Filter users based on search and role
function filterUsers() {
    const searchTerm = document.getElementById('searchUsers').value.toLowerCase();
    const roleFilter = document.getElementById('filterRole').value;
    const tableRows = document.querySelectorAll('#usersTable tbody tr');

    tableRows.forEach(row => {
        const username = row.cells[1].textContent.toLowerCase();
        const email = row.cells[2].textContent.toLowerCase();
        const role = row.cells[3].textContent.toLowerCase();

        const matchesSearch = username.includes(searchTerm) || email.includes(searchTerm);
        const matchesRole = !roleFilter || role.includes(roleFilter === 'admin' ? 'administrateur' : 'utilisateur');

        if (matchesSearch && matchesRole) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Handle add user form submission
function handleAddUser(e) {
    e.preventDefault();
    console.log('Form submitted!');

    const formData = new FormData(e.target);
    const userData = Object.fromEntries(formData.entries());
    console.log('User data:', userData);

    // Validate passwords match
    if (userData.password !== userData.confirm_password) {
        showToast('Les mots de passe ne correspondent pas', 'error');
        return;
    }

    // Validate password length
    if (userData.password.length < 6) {
        showToast('Le mot de passe doit contenir au moins 6 caractères', 'error');
        return;
    }

    // Show loading
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Ajout en cours...';
    submitBtn.disabled = true;

    // Send request
    fetch('/api/users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: userData.username,
            full_name: userData.full_name,
            email: userData.email,
            password: userData.password,
            role: userData.role,
            is_active: userData.is_active === 'on'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Utilisateur ajouté avec succès', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            refreshUsersList();
            e.target.reset();
        } else {
            showToast(data.message || 'Erreur lors de l\'ajout de l\'utilisateur', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Erreur de connexion', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Handle edit user form submission
function handleEditUser(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const userData = Object.fromEntries(formData.entries());

    // Show loading
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement...';
    submitBtn.disabled = true;

    // Send request
    fetch(`/api/users/${userData.user_id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: userData.username,
            full_name: userData.full_name,
            email: userData.email,
            role: userData.role,
            is_active: userData.is_active === 'on'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Utilisateur mis à jour avec succès', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            refreshUsersList();
        } else {
            showToast(data.message || 'Erreur lors de la mise à jour de l\'utilisateur', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Erreur de connexion', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });

    return false;
}

// Handle reset password form submission
function handleResetPassword(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const passwordData = Object.fromEntries(formData.entries());

    // Validate passwords match
    if (passwordData.new_password !== passwordData.confirm_new_password) {
        showToast('Les mots de passe ne correspondent pas', 'error');
        return;
    }

    // Validate password length
    if (passwordData.new_password.length < 6) {
        showToast('Le mot de passe doit contenir au moins 6 caractères', 'error');
        return;
    }

    // Show loading
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Réinitialisation...';
    submitBtn.disabled = true;

    // Send request
    fetch(`/api/users/${passwordData.user_id}/reset-password`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            new_password: passwordData.new_password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Mot de passe réinitialisé avec succès', 'success');
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
            e.target.reset();
        } else {
            showToast(data.message || 'Erreur lors de la réinitialisation du mot de passe', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Erreur de connexion', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Edit user function - opens modal with user data
function editUser(userId) {
    console.log('Edit user clicked for ID:', userId);

    // Fetch user data
    fetch(`/api/users/${userId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const user = data.user;
            const form = document.getElementById('editUserForm');

            // Populate form
            form.querySelector('input[name="user_id"]').value = user.id;
            form.querySelector('input[name="username"]').value = user.username;
            form.querySelector('input[name="full_name"]').value = user.full_name || '';
            form.querySelector('input[name="email"]').value = user.email || '';
            form.querySelector('select[name="role"]').value = user.role;
            form.querySelector('input[name="is_active"]').checked = user.is_active;

            // Show modal
            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        } else {
            showToast('Erreur lors du chargement des données utilisateur', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Erreur de connexion', 'error');
    });
}



// Toggle user status
function toggleUserStatus(userId) {
    if (confirm('Êtes-vous sûr de vouloir changer le statut de cet utilisateur ?')) {
        fetch(`/api/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Statut de l\'utilisateur modifié avec succès', 'success');
                refreshUsersList();
            } else {
                showToast(data.message || 'Erreur lors du changement de statut', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Erreur de connexion', 'error');
        });
    }
}

// Reset password function
function resetPassword(userId) {
    currentResetUserId = userId;
    document.getElementById('resetPasswordForm').querySelector('input[name="user_id"]').value = userId;
    new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
}

// Simple delete user function
function deleteUser(userId) {
    console.log('Delete user clicked for ID:', userId);
    if (confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur? Cette action ne peut pas être annulée.')) {
        fetch(`/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Utilisateur supprimé avec succès', 'success');
                refreshUsersList();
            } else {
                showToast(data.message || 'Erreur lors de la suppression de l\'utilisateur', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Erreur de connexion', 'error');
        });
    }
}

// Refresh users list
function refreshUsersList() {
    window.location.reload();
}

// Toggle password visibility
function togglePassword(button) {
    const input = button.parentElement.querySelector('input');
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Show toast notification
function showToast(message, type = 'info') {
    // Map error to danger for Bootstrap
    const bootstrapType = type === 'error' ? 'danger' : type;

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${bootstrapType} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    // Add to page
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);

    // Show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove after hiding
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
</script>
{% endblock %}