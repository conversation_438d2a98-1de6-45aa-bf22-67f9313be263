{% extends "base.html" %}

{% block title %}إدارة المستخدمين - BANGHALAU{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h2 class="mb-0">
                        <i class="fas fa-users me-2"></i>إدارة المستخدمين
                    </h2>
                    <p class="mb-0">إضافة وتعديل وحذف المستخدمين</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="/api/users/add">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم *</label>
                                    <input type="text" class="form-control" name="username" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم الكامل</label>
                                    <input type="text" class="form-control" name="full_name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور *</label>
                                    <input type="password" class="form-control" name="password" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الدور *</label>
                                    <select class="form-control" name="role" required>
                                        <option value="">اختر الدور</option>
                                        <option value="admin">مدير</option>
                                        <option value="user">مستخدم</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">حالة المستخدم</label>
                                    <select class="form-control" name="is_active">
                                        <option value="1">نشط</option>
                                        <option value="0">غير نشط</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>إضافة المستخدم
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Users List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>قائمة المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>اسم المستخدم</th>
                                    <th>الاسم الكامل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.id }}</td>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.full_name or '-' }}</td>
                                    <td>{{ user.email or '-' }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if user.role == 'admin' else 'primary' }}">
                                            {{ 'مدير' if user.role == 'admin' else 'مستخدم' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if user.is_active else 'secondary' }}">
                                            {{ 'نشط' if user.is_active else 'غير نشط' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="/users/edit/{{ user.id }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a>
                                            <a href="/users/delete/{{ user.id }}" 
                                               class="btn btn-sm btn-danger"
                                               onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    console.log('Users management page loaded - SIMPLE VERSION');
    
    // Show success/error messages
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                alert('{{ message }}');
            {% endfor %}
        {% endif %}
    {% endwith %}
});
</script>
{% endblock %}
