{% extends "base.html" %}

{% block title %}Détails du Bungalow {{ bungalow.numero }} - BANGHALAU{% endblock %}

{% block extra_css %}
<style>
/* خطوط عربية محسنة لعرض تفاصيل البنغالو */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* تحسين عرض النصوص العربية */
.bungalow-details {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

.bungalow-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.bungalow-location {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.bungalow-characteristics {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.6;
    direction: auto;
    text-align: start;
}

.card-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.table-details {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

.table-details th {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.table-details td {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    vertical-align: middle;
}

.badge-details {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

.alert {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    .bungalow-location, .bungalow-characteristics {
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gradient bungalow-title">
                        <i class="fas fa-home me-2"></i>Détails du Bungalow {{ bungalow.numero }}
                    </h1>
                    <p class="text-muted mb-0">Informations complètes et historique</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('edit_bungalow', bungalow_id=bungalow.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>Modifier
                    </a>
                    <a href="{{ url_for('bungalows') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Informations principales -->
                <div class="col-lg-6">
                    <div class="card-modern mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2 text-primary"></i>
                                Informations Principales
                            </h5>
                        </div>
                        <div class="card-body bungalow-details">
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Numéro:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-primary badge-details">{{ bungalow.numero }}</span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Emplacement:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="bungalow-location">
                                        <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                        {{ bungalow.endroit }}
                                    </span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Capacité:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-info badge-details">
                                        <i class="fas fa-users me-1"></i>{{ bungalow.capacite }} personnes
                                    </span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Statut actuel:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {% if current_distribution %}
                                        <span class="badge bg-danger badge-details">
                                            <i class="fas fa-user-check me-1"></i>Occupé
                                        </span>
                                    {% else %}
                                        <span class="badge bg-success badge-details">
                                            <i class="fas fa-check-circle me-1"></i>Disponible
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                            {% if bungalow.caracteristiques %}
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Caractéristiques:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <div class="bungalow-characteristics">
                                        {{ bungalow.caracteristiques }}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Occupation actuelle -->
                <div class="col-lg-6">
                    {% if current_distribution %}
                    <div class="card-modern mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-check me-2 text-danger"></i>
                                Occupation Actuelle
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-user me-2"></i>{{ current_distribution.personnel_nom }} {{ current_distribution.personnel_prenom }}</h6>
                                <p class="mb-1"><strong>Du:</strong> {{ current_distribution.date_debut }}</p>
                                {% if current_distribution.date_fin %}
                                <p class="mb-1"><strong>Au:</strong> {{ current_distribution.date_fin }}</p>
                                {% else %}
                                <p class="mb-1"><strong>Durée:</strong> Indéterminée</p>
                                {% endif %}
                                {% if current_distribution.notes %}
                                <p class="mb-0"><strong>Notes:</strong> {{ current_distribution.notes }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="card-modern mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-check-circle me-2 text-success"></i>
                                Statut
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-home me-2"></i>Bungalow Disponible</h6>
                                <p class="mb-0">Ce bungalow est actuellement libre et peut être attribué.</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Historique des distributions -->
            {% if distributions %}
            <div class="row">
                <div class="col-12">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-history me-2 text-info"></i>
                                Historique des Distributions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-modern table-details">
                                    <thead>
                                        <tr>
                                            <th>Personnel</th>
                                            <th>Date Début</th>
                                            <th>Date Fin</th>
                                            <th>Durée</th>
                                            <th>Statut</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for distribution in distributions %}
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>{{ distribution.personnel_nom }} {{ distribution.personnel_prenom }}</strong>
                                                </div>
                                            </td>
                                            <td>{{ distribution.date_debut }}</td>
                                            <td>{{ distribution.date_fin or 'En cours' }}</td>
                                            <td>
                                                {% if distribution.date_fin %}
                                                    <!-- Calcul simple de durée -->
                                                    Terminée
                                                {% else %}
                                                    En cours
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if distribution == current_distribution %}
                                                    <span class="badge bg-success badge-details">Actuelle</span>
                                                {% else %}
                                                    <span class="badge bg-secondary badge-details">Terminée</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add any specific JavaScript for the view page here
    console.log('Bungalow details page loaded');
});
</script>
{% endblock %}
