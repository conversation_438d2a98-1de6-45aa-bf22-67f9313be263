{% extends "base.html" %}

{% block title %}Détails de la Distribution {{ distribution.numero }} - BANGHALAU{% endblock %}

{% block extra_css %}
<style>
/* خطوط عربية محسنة لعرض تفاصيل التوزيع */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* تحسين عرض النصوص العربية */
.distribution-details {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

.distribution-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.personnel-name {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.personnel-grade {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.bungalow-location {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.distribution-notes {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.6;
    direction: auto;
    text-align: start;
}

.card-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.badge-details {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

.alert {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    .personnel-name, .bungalow-location, .distribution-notes {
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gradient distribution-title">
                        <i class="fas fa-exchange-alt me-2"></i>Distribution {{ distribution.numero }}
                    </h1>
                    <p class="text-muted mb-0">Détails complets de la distribution</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('edit_distribution', distribution_id=distribution.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>Modifier
                    </a>
                    <a href="{{ url_for('distributions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Informations principales -->
                <div class="col-12">
                    <div class="card-modern mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2 text-primary"></i>
                                Informations Principales
                            </h5>
                        </div>
                        <div class="card-body distribution-details">
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Numéro:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-primary badge-details">{{ distribution.numero }}</span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Statut:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {% if status == 'not_planned' %}
                                        <span class="badge bg-secondary badge-details">Non planifiée</span>
                                    {% elif status == 'upcoming' %}
                                        <span class="badge bg-info badge-details">À venir</span>
                                    {% elif status == 'active' %}
                                        <span class="badge bg-success badge-details">Active</span>
                                    {% elif status == 'expired' %}
                                        <span class="badge bg-danger badge-details">Expirée</span>
                                    {% else %}
                                        <span class="badge bg-secondary badge-details">Inconnu</span>
                                    {% endif %}
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Date début:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {% if distribution.date_debut %}
                                        <i class="fas fa-calendar-alt text-success me-1"></i>{{ distribution.date_debut }}
                                    {% else %}
                                        <span class="text-muted">Non définie</span>
                                    {% endif %}
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Date fin:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {% if distribution.date_fin %}
                                        <i class="fas fa-calendar-alt text-danger me-1"></i>{{ distribution.date_fin }}
                                    {% else %}
                                        <i class="fas fa-infinity text-warning me-1"></i>Indéterminée
                                    {% endif %}
                                </div>
                            </div>
                            {% if distribution.notes %}
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Notes:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <div class="distribution-notes">
                                        {{ distribution.notes }}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Détails du bungalow -->
                <div class="col-12">
                    <!-- Bungalow -->
                    {% if distribution.bungalow_numero %}
                    <div class="card-modern mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-home me-2 text-info"></i>
                                Bungalow Assigné
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-home me-2"></i>{{ distribution.bungalow_numero }}</h6>
                                {% if distribution.bungalow_endroit %}
                                <p class="mb-1 bungalow-location"><strong>Emplacement:</strong> {{ distribution.bungalow_endroit }}</p>
                                {% endif %}
                                {% if distribution.bungalow_capacite %}
                                <p class="mb-0"><strong>Capacité:</strong> {{ distribution.bungalow_capacite }} personnes</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Détails du personnel -->
                <div class="col-12">
                    <!-- Personnel -->
                    {% if distribution.personnel_nom %}
                    <div class="card-modern mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2 text-warning"></i>
                                Personnel Assigné
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <h6 class="personnel-name"><i class="fas fa-user me-2"></i>{{ distribution.personnel_nom }} {{ distribution.personnel_prenom or '' }}</h6>
                                {% if distribution.personnel_grade %}
                                <p class="mb-1 personnel-grade"><strong>Grade:</strong> {{ distribution.personnel_grade }}</p>
                                {% endif %}
                                {% if distribution.unite_description %}
                                <p class="mb-0 personnel-grade"><strong>Unité:</strong> {{ distribution.unite_description }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Détails de la session -->
                <div class="col-12">
                    <!-- Session -->
                    {% if distribution.session_numero %}
                    <div class="card-modern mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-calendar me-2 text-success"></i>
                                Session Associée
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-calendar me-2"></i>{{ distribution.session_numero }}</h6>
                                {% if distribution.session_description %}
                                <p class="mb-0"><strong>Description:</strong> {{ distribution.session_description }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add any specific JavaScript for the view page here
    console.log('Distribution details page loaded');
});
</script>
{% endblock %}
