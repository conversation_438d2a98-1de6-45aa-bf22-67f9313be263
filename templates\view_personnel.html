{% extends "base.html" %}

{% block title %}Détails de {{ personnel.nom }} {{ personnel.prenom }} - BANGHALAU{% endblock %}

{% block extra_css %}
<style>
/* خطوط عربية محسنة لعرض تفاصيل الأفراد */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* تحسين عرض النصوص العربية */
.personnel-details {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

.personnel-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.personnel-name {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.personnel-grade {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.personnel-unit {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
    line-height: 1.4;
    direction: auto;
    text-align: start;
}

.card-title {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.table-details {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
}

.table-details th {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 600;
}

.table-details td {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    vertical-align: middle;
}

.badge-details {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 500;
}

.alert {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
    direction: auto;
    text-align: start;
}

.contact-info {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Inter', sans-serif;
    font-weight: 400;
}

/* تحسين عرض النص للشاشات الصغيرة */
@media (max-width: 768px) {
    .personnel-name, .personnel-grade, .personnel-unit {
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gradient personnel-title">
                        <i class="fas fa-user me-2"></i>Détails de {{ personnel.nom }} {{ personnel.prenom }}
                    </h1>
                    <p class="text-muted mb-0">Informations complètes et historique des affectations</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('edit_personnel', personnel_id=personnel.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>Modifier
                    </a>
                    <a href="{{ url_for('personnel') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Informations personnelles -->
                <div class="col-lg-6">
                    <div class="card-modern mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-id-card me-2 text-primary"></i>
                                Informations Personnelles
                            </h5>
                        </div>
                        <div class="card-body personnel-details">
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Matricule:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-primary badge-details">{{ personnel.numero or personnel.matricule }}</span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Nom complet:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="personnel-name">
                                        <i class="fas fa-user text-muted me-1"></i>
                                        {{ personnel.nom }} {{ personnel.prenom }}
                                    </span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Grade:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-warning badge-details personnel-grade">
                                        <i class="fas fa-star me-1"></i>{{ personnel.grade_name or 'Non défini' }}
                                    </span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Unité:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-info badge-details personnel-unit">
                                        <i class="fas fa-flag me-1"></i>{{ personnel.unite_description or 'Non définie' }}
                                    </span>
                                </div>
                            </div>
                            {% if personnel.region %}
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Région Militaire:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-secondary badge-details">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ personnel.region }}
                                    </span>
                                </div>
                            </div>
                            {% endif %}
                            {% if personnel.telephone or personnel.email %}
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Contact:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <div class="contact-info">
                                        {% if personnel.telephone %}
                                            <div><i class="fas fa-phone me-1 text-success"></i>{{ personnel.telephone }}</div>
                                        {% endif %}
                                        {% if personnel.email %}
                                            <div><i class="fas fa-envelope me-1 text-info"></i>{{ personnel.email }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Affectation actuelle -->
                <div class="col-lg-6">
                    {% if current_distribution %}
                    <div class="card-modern mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-home me-2 text-success"></i>
                                Affectation Actuelle
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-home me-2"></i>Bungalow {{ current_distribution.bungalow_numero }}</h6>
                                <p class="mb-1"><strong>Emplacement:</strong> {{ current_distribution.bungalow_endroit }}</p>
                                <p class="mb-1"><strong>Depuis le:</strong> {{ current_distribution.date_debut }}</p>
                                {% if current_distribution.date_fin %}
                                <p class="mb-1"><strong>Jusqu'au:</strong> {{ current_distribution.date_fin }}</p>
                                {% else %}
                                <p class="mb-1"><strong>Durée:</strong> Indéterminée</p>
                                {% endif %}
                                {% if current_distribution.notes %}
                                <p class="mb-0"><strong>Notes:</strong> {{ current_distribution.notes }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="card-modern mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2 text-info"></i>
                                Statut
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-user-clock me-2"></i>Aucune Affectation</h6>
                                <p class="mb-0">Ce personnel n'a actuellement aucune affectation de bungalow.</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Historique des affectations -->
            {% if distributions %}
            <div class="row">
                <div class="col-12">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-history me-2 text-info"></i>
                                Historique des Affectations
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-modern table-details">
                                    <thead>
                                        <tr>
                                            <th>Bungalow</th>
                                            <th>Emplacement</th>
                                            <th>Date Début</th>
                                            <th>Date Fin</th>
                                            <th>Statut</th>
                                            <th>Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for distribution in distributions %}
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary badge-details">
                                                    {{ distribution.bungalow_numero }}
                                                </span>
                                            </td>
                                            <td class="personnel-unit">{{ distribution.bungalow_endroit }}</td>
                                            <td>{{ distribution.date_debut }}</td>
                                            <td>{{ distribution.date_fin or 'En cours' }}</td>
                                            <td>
                                                {% if distribution == current_distribution %}
                                                    <span class="badge bg-success badge-details">Actuelle</span>
                                                {% else %}
                                                    <span class="badge bg-secondary badge-details">Terminée</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if distribution.notes %}
                                                    <small class="text-muted">{{ distribution.notes }}</small>
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add any specific JavaScript for the view page here
    console.log('Personnel details page loaded');
});
</script>
{% endblock %}
