#!/usr/bin/env python3
"""
Script pour tester l'importation des unités arabes
"""

import pandas as pd
from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def test_arabic_import():
    """Tester l'importation des unités arabes"""
    
    try:
        # قراءة ملف Excel العربي
        df = pd.read_excel('unites_arabes.xlsx')
        print('✅ تم قراءة ملف الوحدات العربية بنجاح')
        print(f'📊 عدد الصفوف: {len(df)}')
        
        # تحويل إلى قائمة من القواميس
        unites_data = []
        for index, row in df.iterrows():
            unite_data = {
                'numero': str(row.get('numero', '')).strip() if str(row.get('numero', '')) != 'nan' else '',
                'description': str(row.get('description', '')).strip() if str(row.get('description', '')) != 'nan' else '',
                'raccourci': str(row.get('raccourci', '')).strip() if str(row.get('raccourci', '')) != 'nan' and row.get('raccourci') else None
            }
            unites_data.append(unite_data)
        
        print(f'📋 تم تحضير {len(unites_data)} وحدة عربية للاستيراد')
        
        # عرض البيانات
        for i, unite in enumerate(unites_data, 1):
            print(f'  {i}. {unite["numero"]}: {unite["description"]} ({unite["raccourci"]})')
        
        # استيراد البيانات
        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)
        
        results = db_ops.import_unites_from_data(unites_data)
        db_manager.close()
        
        print('\n📊 نتائج استيراد الوحدات العربية:')
        print(f'✅ نجح: {results["success"]}')
        print(f'❌ أخطاء: {results["errors"]}')
        print(f'🔄 مكررات: {results["duplicates"]}')
        
        if results["messages"]:
            print('\n📝 الرسائل:')
            for msg in results["messages"]:
                print(f'  - {msg}')
        
        return results
        
    except Exception as e:
        print(f'❌ خطأ: {str(e)}')
        return None

if __name__ == "__main__":
    test_arabic_import()
