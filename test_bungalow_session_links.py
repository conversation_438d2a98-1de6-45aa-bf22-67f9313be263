#!/usr/bin/env python3
"""
Script pour tester les liaisons Bungalows-Sessions
"""

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def test_bungalow_session_relationships():
    """Tester les relations entre bungalows et sessions"""
    
    print("🔗 Test des Relations Bungalows-Sessions")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)
    
    # 1. Afficher les liaisons directes
    print("\n📋 1. Liaisons Directes (bungalow_sessions):")
    links = db_ops.list_bungalow_session_links()
    if links:
        for link in links:
            print(f"  🔗 {link['bungalow_numero']} ({link['bungalow_endroit']}) ↔️ {link['session_numero']} ({link['statut']})")
    else:
        print("  Aucune liaison directe trouvée")
    
    # 2. Afficher les bungalows avec informations de session
    print("\n🏠 2. Bungalows avec Sessions Assignées:")
    bungalows_with_sessions = db_ops.get_bungalows_with_session_info()
    for bungalow in bungalows_with_sessions:
        if bungalow['assigned_session_id']:
            print(f"  🏠 {bungalow['numero']} ({bungalow['endroit']}) → 📅 {bungalow['assigned_session_numero']}")
        else:
            print(f"  🏠 {bungalow['numero']} ({bungalow['endroit']}) → ❌ Aucune session")
    
    # 3. Afficher les sessions avec nombre de bungalows
    print("\n📅 3. Sessions avec Nombre de Bungalows:")
    sessions_with_counts = db_ops.get_sessions_with_bungalow_count()
    for session in sessions_with_counts:
        total_count = session['total_bungalows_count']
        direct_count = session['direct_bungalows_count']
        distribution_count = session['distribution_bungalows_count']
        
        print(f"  📅 {session['numero']} ({session['description'] or 'Sans description'})")
        print(f"     Total: {total_count} | Direct: {direct_count} | Via distributions: {distribution_count}")
    
    # 4. Test des fonctions de recherche
    print("\n🔍 4. Tests de Recherche:")
    
    # Obtenir le premier bungalow et session pour les tests
    bungalows = db_ops.list_bungalows()
    sessions = db_ops.list_sessions()
    
    if bungalows and sessions:
        test_bungalow = bungalows[0]
        test_session = sessions[0]
        
        print(f"\n  🏠 Bungalows liés à la session '{test_session['numero']}':")
        session_bungalows = db_ops.get_bungalows_by_session_direct(test_session['id'])
        for bungalow in session_bungalows:
            print(f"    - {bungalow['numero']} ({bungalow['endroit']})")
        
        print(f"\n  📅 Sessions liées au bungalow '{test_bungalow['numero']}':")
        bungalow_sessions = db_ops.get_sessions_by_bungalow_direct(test_bungalow['id'])
        for session in bungalow_sessions:
            print(f"    - {session['numero']} ({session['description'] or 'Sans description'})")
    
    # 5. Statistiques générales
    print("\n📊 5. Statistiques Générales:")
    total_bungalows = len(bungalows)
    total_sessions = len(sessions)
    total_links = len(links)
    
    # Compter les bungalows avec sessions assignées
    bungalows_with_assigned_sessions = sum(1 for b in bungalows_with_sessions if b['assigned_session_id'])
    
    # Compter les sessions avec bungalows
    sessions_with_bungalows = sum(1 for s in sessions_with_counts if s['total_bungalows_count'] > 0)
    
    print(f"  📈 Total Bungalows: {total_bungalows}")
    print(f"  📈 Total Sessions: {total_sessions}")
    print(f"  📈 Total Liaisons Directes: {total_links}")
    print(f"  📈 Bungalows avec Sessions: {bungalows_with_assigned_sessions}")
    print(f"  📈 Sessions avec Bungalows: {sessions_with_bungalows}")
    
    # Calculer les pourcentages
    if total_bungalows > 0:
        bungalow_assignment_rate = (bungalows_with_assigned_sessions / total_bungalows) * 100
        print(f"  📊 Taux d'assignation des bungalows: {bungalow_assignment_rate:.1f}%")
    
    if total_sessions > 0:
        session_usage_rate = (sessions_with_bungalows / total_sessions) * 100
        print(f"  📊 Taux d'utilisation des sessions: {session_usage_rate:.1f}%")
    
    db_manager.close()
    
    print("\n✅ Test terminé avec succès!")

def create_sample_links():
    """Créer des exemples de liaisons pour les tests"""
    
    print("\n🔧 Création d'exemples de liaisons...")
    
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)
    
    bungalows = db_ops.list_bungalows()
    sessions = db_ops.list_sessions()
    
    if not bungalows or not sessions:
        print("❌ Pas de bungalows ou sessions disponibles")
        db_manager.close()
        return
    
    # Créer quelques liaisons d'exemple
    sample_assignments = [
        {
            'bungalow_id': bungalows[0]['id'],
            'session_id': sessions[0]['id'],
            'notes': 'Assignation principale pour la session'
        },
        {
            'bungalow_id': bungalows[1]['id'] if len(bungalows) > 1 else bungalows[0]['id'],
            'session_id': sessions[0]['id'],
            'notes': 'Bungalow secondaire pour la même session'
        }
    ]
    
    success_count = 0
    for assignment in sample_assignments:
        # Assigner via la table bungalows
        success = db_ops.assign_bungalow_to_session(
            assignment['bungalow_id'], 
            assignment['session_id']
        )
        
        if success:
            print(f"✅ Bungalow {assignment['bungalow_id']} assigné à la session {assignment['session_id']}")
            success_count += 1
        else:
            print(f"❌ Erreur lors de l'assignation")
    
    db_manager.close()
    print(f"🎉 {success_count} assignations créées")

def show_relationship_summary():
    """Afficher un résumé des relations"""
    
    print("\n📋 Résumé des Relations Bungalows-Sessions")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)
    
    # Utiliser les vues créées
    try:
        # Vue bungalows_with_sessions
        result = db_manager.execute('''
        SELECT 
            bungalow_numero,
            bungalow_endroit,
            session_numero,
            session_description,
            distribution_date_debut,
            distribution_date_fin
        FROM bungalows_with_sessions
        WHERE session_numero IS NOT NULL
        ORDER BY bungalow_numero
        ''')
        
        print("\n🔍 Relations via Distributions:")
        rows = result.fetchall()
        if rows:
            for row in rows:
                print(f"  🏠 {row['bungalow_numero']} ({row['bungalow_endroit']}) ↔️ 📅 {row['session_numero']}")
                if row['distribution_date_debut']:
                    print(f"     📅 Du {row['distribution_date_debut']} au {row['distribution_date_fin'] or 'indéterminé'}")
        else:
            print("  Aucune relation trouvée")
            
    except Exception as e:
        print(f"❌ Erreur lors de l'accès aux vues: {e}")
    
    db_manager.close()

if __name__ == "__main__":
    test_bungalow_session_relationships()
    create_sample_links()
    show_relationship_summary()
