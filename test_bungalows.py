#!/usr/bin/env python
"""
Test script for Bungalows in BANGHALAU database
"""

from database import DatabaseManager, DatabaseOperations


def main():
    """Test the Bungalows operations"""
    print("Testing Bungalows in BANGHALAU database...")
    
    # Initialize database
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)
    
    # Get bungalows
    print("\nBungalows in database:")
    bungalows = db_ops.list_bungalows()
    for bungalow in bungalows:
        print(f"ID: {bungalow['id']}")
        print(f"Numero: {bungalow['numero']}")
        print(f"Endroit: {bungalow['endroit']}")
        print(f"Capacité: {bungalow['capacite']}")
        print(f"Caractéristiques: {bungalow['caracteristiques']}")
        print(f"Created at: {bungalow['created_at']}")
        print("-" * 30)
    
    # Close connection
    db_manager.close()
    print("\nTest completed successfully.")


if __name__ == "__main__":
    main()
