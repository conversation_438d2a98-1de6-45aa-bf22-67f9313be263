#!/usr/bin/env python
"""
Test script for BANGHALAU database
"""

from database import DatabaseManager, DatabaseOperations


def main():
    """Test the database operations"""
    print("Testing BANGHALAU database...")
    
    # Initialize database
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)
    
    # Get users
    print("\nUsers in database:")
    users = db_ops.list_users()
    for user in users:
        print(f"ID: {user['id']}, Username: {user['username']}, Email: {user['email']}")
    
    # Get items
    print("\nItems in database:")
    items = db_ops.list_items()
    for item in items:
        print(f"ID: {item['id']}, Name: {item['name']}, Price: {item['price']}")
    
    # Close connection
    db_manager.close()
    print("\nTest completed successfully.")


if __name__ == "__main__":
    main()
