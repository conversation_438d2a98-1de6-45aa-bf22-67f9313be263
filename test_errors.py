#!/usr/bin/env python3
"""
Script pour tester la gestion des erreurs lors de l'importation
"""

import pandas as pd
from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def test_error_handling():
    """Tester la gestion des erreurs"""
    
    try:
        # قراءة ملف Excel مع أخطاء
        df = pd.read_excel('unites_avec_erreurs.xlsx')
        print('✅ تم قراءة ملف Excel مع الأخطاء')
        print(f'📊 عدد الصفوف: {len(df)}')
        
        # تحويل إلى قائمة من القواميس
        unites_data = []
        for index, row in df.iterrows():
            unite_data = {
                'numero': row.get('numero', ''),
                'description': row.get('description', ''),
                'raccourci': row.get('raccourci', '') if 'raccourci' in df.columns else None
            }
            unites_data.append(unite_data)
        
        print(f'📋 تم تحضير {len(unites_data)} وحدة للاختبار')
        
        # عرض البيانات
        for i, unite in enumerate(unites_data, 1):
            print(f'  {i}. "{unite["numero"]}": "{unite["description"]}" ({unite["raccourci"]})')
        
        # استيراد البيانات
        db_manager = DatabaseManager()
        db_manager.connect()
        db_ops = DatabaseOperations(db_manager)
        
        results = db_ops.import_unites_from_data(unites_data)
        db_manager.close()
        
        print('\n📊 نتائج اختبار معالجة الأخطاء:')
        print(f'✅ نجح: {results["success"]}')
        print(f'❌ أخطاء: {results["errors"]}')
        print(f'🔄 مكررات: {results["duplicates"]}')
        
        if results["messages"]:
            print('\n📝 الرسائل التفصيلية:')
            for msg in results["messages"]:
                print(f'  - {msg}')
        
        return results
        
    except Exception as e:
        print(f'❌ خطأ: {str(e)}')
        return None

if __name__ == "__main__":
    test_error_handling()
