#!/usr/bin/env python
"""
Test script for Grades in BANGHALAU database
"""

from database import DatabaseManager, DatabaseOperations


def main():
    """Test the Grades operations"""
    print("Testing Grades in BANGHALAU database...")
    
    # Initialize database
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)
    
    # Get grades
    print("\nGrades in database:")
    grades = db_ops.list_grades()
    for grade in grades:
        print(f"ID: {grade['id']}")
        print(f"Numero: {grade['numero']}")
        print(f"Grade: {grade['grade']}")
        print(f"Description: {grade['description']}")
        print(f"Created at: {grade['created_at']}")
        print("-" * 30)
    
    # Close connection
    db_manager.close()
    print("\nTest completed successfully.")


if __name__ == "__main__":
    main()
