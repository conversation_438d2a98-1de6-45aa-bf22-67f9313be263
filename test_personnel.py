#!/usr/bin/env python
"""
Test script for Personnel Militaire in BANGHALAU database
"""

from database import DatabaseManager, DatabaseOperations


def main():
    """Test the Personnel Militaire operations"""
    print("Testing Personnel Militaire in BANGHALAU database...")
    
    # Initialize database
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)
    
    # Get personnel
    print("\nPersonnel Militaire in database:")
    personnel_list = db_ops.list_personnel_militaire()
    for personnel in personnel_list:
        print(f"ID: {personnel['id']}")
        print(f"Numero: {personnel['numero']}")
        print(f"Matricule: {personnel['matricule']}")
        print(f"Nom: {personnel['nom']}")
        print(f"Prenom: {personnel['prenom']}")
        print(f"Grade: {personnel['grade']}")
        print(f"Unite: {personnel['unite']}")
        print(f"Created at: {personnel['created_at']}")
        print("-" * 30)
    
    # Close connection
    db_manager.close()
    print("\nTest completed successfully.")


if __name__ == "__main__":
    main()
