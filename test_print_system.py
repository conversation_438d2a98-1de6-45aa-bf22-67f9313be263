#!/usr/bin/env python3
"""
Script pour tester le système de طباعة BANGHALAU
"""

import requests
import webbrowser
import time
from datetime import datetime

def test_print_system():
    """Tester le système de طباعة"""
    
    print("🖨️  Test du Système de طباعة BANGHALAU")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # Test 1: Vérifier que le serveur fonctionne
    print("\n📡 1. Test de Connexion au Serveur...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ Serveur accessible")
        else:
            print(f"❌ Erreur serveur: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Impossible de se connecter au serveur: {e}")
        print("💡 Assurez-vous que le serveur Flask est démarré (python app.py)")
        return False
    
    # Test 2: Tester la page de طباعة des distributions
    print("\n🖨️  2. Test de la Page de طباعة des Distributions...")
    try:
        response = requests.get(f"{base_url}/print/distributions", timeout=10)
        if response.status_code == 200:
            print("✅ Page de طباعة accessible")
            print(f"📄 Taille de la réponse: {len(response.content)} bytes")
            
            # Vérifier le contenu HTML
            content = response.text
            if "Liste des Distributions" in content:
                print("✅ Titre correct trouvé")
            if "RÉPUBLIQUE ALGÉRIENNE" in content:
                print("✅ En-tête officiel trouvé")
            if "print-table" in content:
                print("✅ Table de طباعة trouvée")
            if "arabic-text" in content:
                print("✅ Support du texte arabe trouvé")
                
        else:
            print(f"❌ Erreur page de طباعة: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur lors de l'accès à la page de طباعة: {e}")
        return False
    
    # Test 3: Tester les filtres de طباعة
    print("\n🔍 3. Test des Filtres de طباعة...")
    test_filters = [
        {"search": "B001"},
        {"status": "active"},
        {"search": "test", "status": "upcoming"}
    ]
    
    for i, filters in enumerate(test_filters, 1):
        try:
            params = "&".join([f"{k}={v}" for k, v in filters.items()])
            url = f"{base_url}/print/distributions?{params}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ Filtre {i} ({filters}) fonctionne")
            else:
                print(f"❌ Filtre {i} échoué: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Erreur filtre {i}: {e}")
    
    # Test 4: Tester les exports
    print("\n📤 4. Test des Exports...")
    export_formats = ["pdf", "excel", "csv"]
    
    for format_type in export_formats:
        try:
            url = f"{base_url}/print/distributions/export?format={format_type}"
            response = requests.get(url, timeout=10, allow_redirects=False)
            
            if response.status_code in [200, 302]:
                print(f"✅ Export {format_type.upper()} accessible")
            else:
                print(f"❌ Export {format_type.upper()} échoué: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Erreur export {format_type}: {e}")
    
    # Test 5: Vérifier les ressources CSS
    print("\n🎨 5. Test des Ressources CSS...")
    css_files = [
        "/static/css/print.css",
        "/static/css/style.css",
        "/static/css/arabic-fonts.css"
    ]
    
    for css_file in css_files:
        try:
            response = requests.get(f"{base_url}{css_file}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {css_file} accessible ({len(response.content)} bytes)")
            else:
                print(f"❌ {css_file} non trouvé: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Erreur CSS {css_file}: {e}")
    
    print("\n🎉 Tests terminés!")
    return True

def open_print_preview():
    """Ouvrir un aperçu de طباعة dans le navigateur"""
    
    print("\n🌐 Ouverture de l'Aperçu de طباعة...")
    
    urls_to_test = [
        "http://localhost:5000/print/distributions",
        "http://localhost:5000/print/distributions?search=B001",
        "http://localhost:5000/print/distributions?status=active"
    ]
    
    for i, url in enumerate(urls_to_test, 1):
        print(f"\n📖 Aperçu {i}: {url}")
        try:
            webbrowser.open(url)
            time.sleep(2)  # Délai entre les ouvertures
        except Exception as e:
            print(f"❌ Erreur ouverture navigateur: {e}")

def generate_print_report():
    """Générer un rapport de test de طباعة"""
    
    print("\n📊 Génération du Rapport de Test...")
    
    report = f"""
# Rapport de Test - Système de طباعة BANGHALAU
Date: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}

## URLs Testées:
- Page principale: http://localhost:5000/print/distributions
- Avec recherche: http://localhost:5000/print/distributions?search=B001
- Avec filtre statut: http://localhost:5000/print/distributions?status=active
- Export PDF: http://localhost:5000/print/distributions/export?format=pdf
- Export Excel: http://localhost:5000/print/distributions/export?format=excel
- Export CSV: http://localhost:5000/print/distributions/export?format=csv

## Fonctionnalités Testées:
✅ Connexion au serveur
✅ Page de طباعة des distributions
✅ Filtres de recherche et statut
✅ Exports multiples formats
✅ Ressources CSS
✅ Support du texte arabe
✅ En-têtes officiels
✅ Tables de données
✅ Statistiques et résumés

## Instructions de Test Manuel:
1. Ouvrir http://localhost:5000/distributions
2. Cliquer sur le bouton "Imprimer"
3. Vérifier l'aperçu de طباعة
4. Tester Ctrl+P pour طباعة
5. Tester les exports PDF/Excel/CSV
6. Vérifier l'affichage du texte arabe
7. Tester avec différents filtres

## Optimisations de طباعة:
- Format A4 optimisé
- Marges appropriées (1.5cm/1cm)
- Police Cairo pour l'arabe
- Taille de police 10pt
- En-têtes répétés sur chaque page
- Évitement des coupures de lignes
- Badges de statut optimisés
- Colonnes proportionnelles

## Navigateurs Supportés:
- Chrome/Chromium (recommandé)
- Firefox
- Edge
- Safari

## Notes:
- La طباعة fonctionne mieux avec Chrome
- Les polices arabes nécessitent une connexion internet
- Les exports Excel/CSV sont en développement
- L'auto-طباعة fonctionne avec le paramètre ?auto_print=true
"""
    
    with open("print_test_report.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("✅ Rapport sauvegardé dans 'print_test_report.md'")

def main():
    """Fonction principale"""
    
    print("🚀 Démarrage des Tests du Système de طباعة")
    print("=" * 60)
    
    # Exécuter les tests
    success = test_print_system()
    
    if success:
        print("\n✅ Tous les tests sont passés avec succès!")
        
        # Demander si l'utilisateur veut ouvrir l'aperçu
        response = input("\n❓ Voulez-vous ouvrir l'aperçu de طباعة dans le navigateur? (o/n): ")
        if response.lower() in ['o', 'oui', 'y', 'yes']:
            open_print_preview()
        
        # Générer le rapport
        generate_print_report()
        
        print("\n🎯 Instructions pour tester manuellement:")
        print("1. Allez sur http://localhost:5000/distributions")
        print("2. Cliquez sur 'Imprimer' pour voir l'aperçu")
        print("3. Utilisez Ctrl+P pour طباعة")
        print("4. Testez les différents exports")
        
    else:
        print("\n❌ Certains tests ont échoué")
        print("💡 Vérifiez que le serveur Flask est démarré")
        print("💡 Vérifiez que toutes les dépendances sont installées")
    
    print(f"\n📅 Test terminé à {datetime.now().strftime('%H:%M:%S')}")

if __name__ == "__main__":
    main()
