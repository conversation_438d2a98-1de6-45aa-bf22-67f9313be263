#!/usr/bin/env python3

print("🔍 Testing BANGHALAU Application...")

try:
    print("1. Testing basic imports...")
    import os
    import sys
    print("   ✅ Basic imports OK")
    
    print("2. Testing Flask import...")
    import flask
    print(f"   ✅ Flask version: {flask.__version__}")
    
    print("3. Testing app import...")
    from app import app
    print("   ✅ App imported successfully")
    
    print("4. Testing database...")
    from database.db_manager import DatabaseManager
    from database.operations import DatabaseOperations
    print("   ✅ Database modules imported")
    
    print("5. Starting Flask server...")
    print("   📍 URL: http://localhost:5000")
    print("   👤 Username: admin")
    print("   🔑 Password: admin123")
    print("   🚀 Starting server...")
    
    app.run(host='127.0.0.1', port=5000, debug=False)
    
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Installing required packages...")
    import subprocess
    subprocess.run([sys.executable, "-m", "pip", "install", "flask", "flask-login", "werkzeug"])
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
