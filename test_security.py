#!/usr/bin/env python3
"""
Security System Test Script for BANGHALAU
Tests all security components and features
"""

import os
import sys
import time
from datetime import datetime

# Add database directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))

def test_security_imports():
    """Test if all security modules can be imported"""
    print("🔍 Testing security module imports...")
    
    try:
        from database.security import DatabaseSecurity
        print("✅ DatabaseSecurity imported successfully")
    except Exception as e:
        print(f"❌ Failed to import DatabaseSecurity: {e}")
        return False
    
    try:
        from database.protection import DatabaseProtection
        print("✅ DatabaseProtection imported successfully")
    except Exception as e:
        print(f"❌ Failed to import DatabaseProtection: {e}")
        return False
    
    return True

def test_security_initialization():
    """Test security system initialization"""
    print("\n🔧 Testing security system initialization...")
    
    try:
        from database.security import DatabaseSecurity
        from database.protection import DatabaseProtection
        
        # Initialize security systems
        db_security = DatabaseSecurity()
        print("✅ DatabaseSecurity initialized")
        
        db_protection = DatabaseProtection()
        print("✅ DatabaseProtection initialized")
        
        # Test basic functionality
        config = db_security.config
        print(f"✅ Security config loaded: {len(config)} settings")
        
        status = db_protection.get_protection_status()
        print(f"✅ Protection status retrieved: monitoring={status['monitoring_active']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Security initialization failed: {e}")
        return False

def test_password_policy():
    """Test password policy validation"""
    print("\n🔑 Testing password policy...")
    
    try:
        from database.security import DatabaseSecurity
        db_security = DatabaseSecurity()
        
        # Test weak password
        weak_password = "123"
        is_valid, errors = db_security.validate_password_policy(weak_password)
        if not is_valid:
            print("✅ Weak password correctly rejected")
        else:
            print("❌ Weak password incorrectly accepted")
            return False
        
        # Test strong password
        strong_password = "MySecure@Pass123!"
        is_valid, errors = db_security.validate_password_policy(strong_password)
        if is_valid:
            print("✅ Strong password correctly accepted")
        else:
            print(f"❌ Strong password incorrectly rejected: {errors}")
            return False
        
        # Test password generation
        generated_password = db_security.generate_secure_password(16)
        print(f"✅ Generated secure password: {generated_password[:4]}****")
        
        return True
        
    except Exception as e:
        print(f"❌ Password policy test failed: {e}")
        return False

def test_encryption():
    """Test data encryption functionality"""
    print("\n🔒 Testing encryption functionality...")
    
    try:
        from database.security import DatabaseSecurity
        db_security = DatabaseSecurity()
        
        # Test encryption/decryption
        test_data = "Sensitive test data 123"
        password = "TestPassword123!"
        
        key, salt = db_security.generate_encryption_key(password)
        print("✅ Encryption key generated")
        
        encrypted_data = db_security.encrypt_data(test_data, key)
        print("✅ Data encrypted successfully")
        
        decrypted_data = db_security.decrypt_data(encrypted_data, key)
        print("✅ Data decrypted successfully")
        
        if decrypted_data == test_data:
            print("✅ Encryption/decryption cycle successful")
        else:
            print("❌ Encryption/decryption cycle failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")
        return False

def test_protection_monitoring():
    """Test protection monitoring functionality"""
    print("\n🛡️ Testing protection monitoring...")
    
    try:
        from database.protection import DatabaseProtection
        db_protection = DatabaseProtection()
        
        # Test access logging
        db_protection.log_access("test_user", "TEST_QUERY", "127.0.0.1", True)
        print("✅ Access logging works")
        
        # Test failed attempt logging
        db_protection.log_failed_attempt("test_user", "127.0.0.1", "Test failed attempt")
        print("✅ Failed attempt logging works")
        
        # Test IP blocking check
        is_blocked = db_protection.is_ip_blocked("127.0.0.1")
        print(f"✅ IP blocking check works: blocked={is_blocked}")
        
        # Test protection status
        status = db_protection.get_protection_status()
        print(f"✅ Protection status retrieved: {len(status)} metrics")
        
        return True
        
    except Exception as e:
        print(f"❌ Protection monitoring test failed: {e}")
        return False

def test_backup_functionality():
    """Test backup functionality"""
    print("\n💾 Testing backup functionality...")
    
    try:
        from database.security import DatabaseSecurity
        db_security = DatabaseSecurity()
        
        # Create test backup directory
        os.makedirs("test_backups", exist_ok=True)
        
        backup_path = "test_backups/test_backup.bak"
        backup_password = "TestBackup123!"
        
        # Test backup creation
        success, message = db_security.create_secure_backup(backup_path, backup_password)
        
        if success:
            print("✅ Encrypted backup created successfully")
            
            # Check if backup file exists
            if os.path.exists(backup_path):
                print("✅ Backup file exists")
                
                # Clean up test backup
                os.remove(backup_path)
                print("✅ Test backup cleaned up")
            else:
                print("❌ Backup file not found")
                return False
        else:
            print(f"❌ Backup creation failed: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Backup functionality test failed: {e}")
        return False

def test_database_integrity():
    """Test database integrity checking"""
    print("\n🔍 Testing database integrity checking...")
    
    try:
        from database.protection import DatabaseProtection
        db_protection = DatabaseProtection()
        
        # Test integrity check
        issues = db_protection.verify_database_integrity()
        print(f"✅ Database integrity check completed: {len(issues)} issues found")
        
        if issues:
            print("⚠️ Integrity issues found:")
            for issue in issues[:3]:  # Show first 3 issues
                print(f"  - {issue}")
        else:
            print("✅ No integrity issues found")
        
        return True
        
    except Exception as e:
        print(f"❌ Database integrity test failed: {e}")
        return False

def run_all_tests():
    """Run all security tests"""
    print("🔐 BANGHALAU Security System Test Suite")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_security_imports),
        ("System Initialization", test_security_initialization),
        ("Password Policy", test_password_policy),
        ("Encryption", test_encryption),
        ("Protection Monitoring", test_protection_monitoring),
        ("Backup Functionality", test_backup_functionality),
        ("Database Integrity", test_database_integrity)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"💥 {test_name} CRASHED: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All security tests PASSED! System is ready.")
        return True
    else:
        print(f"⚠️ {total - passed} tests FAILED. Please review the issues.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n✅ Security system is fully operational!")
        print("🔒 BANGHALAU is protected and ready for use.")
    else:
        print("\n❌ Security system has issues!")
        print("🔧 Please fix the failed tests before using the system.")
        sys.exit(1)
