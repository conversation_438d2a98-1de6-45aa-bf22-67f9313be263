#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم اختبار بسيط لـ BANGHALAU
"""

print("🚀 بدء تشغيل خادم BANGHALAU...")
print("📍 الرابط: http://localhost:5000")
print("=" * 50)

try:
    from flask import Flask
    print("✅ Flask متوفر")

    app = Flask(__name__)
    app.secret_key = 'banghalau_test_key'

    @app.route('/')
    def home():
        return '''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>BANGHALAU - نظام إدارة البنغالوهات</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    margin: 0;
                    padding: 0;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    text-align: center;
                }
                .container {
                    background: rgba(255, 255, 255, 0.1);
                    padding: 40px;
                    border-radius: 20px;
                    backdrop-filter: blur(10px);
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    max-width: 600px;
                }
                h1 {
                    font-size: 3em;
                    margin-bottom: 20px;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                }
                h2 {
                    font-size: 1.5em;
                    margin-bottom: 30px;
                    opacity: 0.9;
                }
                .status {
                    background: rgba(76, 175, 80, 0.2);
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                    border: 2px solid rgba(76, 175, 80, 0.5);
                }
                .info {
                    background: rgba(33, 150, 243, 0.2);
                    padding: 15px;
                    border-radius: 10px;
                    margin: 15px 0;
                    border: 2px solid rgba(33, 150, 243, 0.5);
                }
                .btn {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 12px 24px;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 25px;
                    text-decoration: none;
                    display: inline-block;
                    margin: 10px;
                    transition: all 0.3s ease;
                }
                .btn:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: translateY(-2px);
                    color: white;
                    text-decoration: none;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🏠 BANGHALAU</h1>
                <h2>نظام إدارة توزيع البنغالوهات</h2>

                <div class="status">
                    ✅ الخادم يعمل بنجاح!
                </div>

                <div class="info">
                    📍 <strong>الرابط:</strong> http://localhost:5000<br>
                    🚀 <strong>الحالة:</strong> جاهز للاستخدام<br>
                    ⏰ <strong>الوقت:</strong> <span id="time"></span>
                </div>

                <div>
                    <a href="/login" class="btn">🔐 تسجيل الدخول</a>
                    <a href="/test" class="btn">🧪 اختبار النظام</a>
                    <a href="/dashboard" class="btn">📊 لوحة التحكم</a>
                    <a href="/about" class="btn">ℹ️ حول النظام</a>
                </div>

                <div style="margin-top: 30px; font-size: 0.9em; opacity: 0.7;">
                    © 2024 BANGHALAU - تم التطوير باستخدام Flask و Python
                </div>
            </div>

            <script>
                function updateTime() {
                    const now = new Date();
                    document.getElementById('time').textContent = now.toLocaleString('ar-SA');
                }
                setInterval(updateTime, 1000);
                updateTime();
            </script>
        </body>
        </html>
        '''

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        from flask import request, redirect, url_for, session

        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')

            # التحقق من بيانات تسجيل الدخول
            if username == 'admin' and password == 'admin123':
                session['user_id'] = 1
                session['username'] = username
                return redirect(url_for('dashboard'))
            else:
                error_message = "اسم المستخدم أو كلمة المرور غير صحيحة"
                return f'''
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>تسجيل الدخول - BANGHALAU</title>
                    <style>
                        body {{
                            font-family: Arial, sans-serif;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            padding: 50px;
                            color: white;
                            text-align: center;
                        }}
                        .login-box {{
                            max-width: 400px;
                            margin: 0 auto;
                            background: rgba(255, 255, 255, 0.1);
                            padding: 40px;
                            border-radius: 20px;
                            backdrop-filter: blur(10px);
                        }}
                        input {{
                            width: 100%;
                            padding: 12px;
                            margin: 10px 0;
                            border: none;
                            border-radius: 8px;
                            font-size: 16px;
                            box-sizing: border-box;
                        }}
                        .btn {{
                            background: #4CAF50;
                            color: white;
                            padding: 12px 24px;
                            border: none;
                            border-radius: 8px;
                            cursor: pointer;
                            width: 100%;
                            font-size: 16px;
                        }}
                        .btn:hover {{
                            background: #45a049;
                        }}
                        .info {{
                            background: rgba(33, 150, 243, 0.2);
                            padding: 15px;
                            border-radius: 10px;
                            margin-bottom: 20px;
                        }}
                        .error {{
                            background: rgba(244, 67, 54, 0.2);
                            color: #ffcdd2;
                            padding: 15px;
                            border-radius: 10px;
                            margin-bottom: 20px;
                            border: 2px solid rgba(244, 67, 54, 0.5);
                        }}
                    </style>
                </head>
                <body>
                    <div class="login-box">
                        <h1>🔐 تسجيل الدخول</h1>
                        <div class="error">
                            ❌ {error_message}
                        </div>
                        <div class="info">
                            <strong>بيانات الدخول الافتراضية:</strong><br>
                            👤 اسم المستخدم: admin<br>
                            🔑 كلمة المرور: admin123
                        </div>
                        <form method="POST">
                            <input type="text" name="username" placeholder="اسم المستخدم" required>
                            <input type="password" name="password" placeholder="كلمة المرور" required>
                            <button type="submit" class="btn">دخول</button>
                        </form>
                        <div style="margin-top: 20px;">
                            <a href="/" style="color: white;">← العودة للصفحة الرئيسية</a>
                        </div>
                    </div>
                </body>
                </html>
                '''

        # GET request - عرض نموذج تسجيل الدخول
        return '''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تسجيل الدخول - BANGHALAU</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 50px;
                    color: white;
                    text-align: center;
                }
                .login-box {
                    max-width: 400px;
                    margin: 0 auto;
                    background: rgba(255, 255, 255, 0.1);
                    padding: 40px;
                    border-radius: 20px;
                    backdrop-filter: blur(10px);
                }
                input {
                    width: 100%;
                    padding: 12px;
                    margin: 10px 0;
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    box-sizing: border-box;
                }
                .btn {
                    background: #4CAF50;
                    color: white;
                    padding: 12px 24px;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    width: 100%;
                    font-size: 16px;
                }
                .btn:hover {
                    background: #45a049;
                }
                .info {
                    background: rgba(33, 150, 243, 0.2);
                    padding: 15px;
                    border-radius: 10px;
                    margin-bottom: 20px;
                }
            </style>
        </head>
        <body>
            <div class="login-box">
                <h1>🔐 تسجيل الدخول</h1>
                <div class="info">
                    <strong>بيانات الدخول الافتراضية:</strong><br>
                    👤 اسم المستخدم: admin<br>
                    🔑 كلمة المرور: admin123
                </div>
                <form method="POST">
                    <input type="text" name="username" placeholder="اسم المستخدم" required>
                    <input type="password" name="password" placeholder="كلمة المرور" required>
                    <button type="submit" class="btn">دخول</button>
                </form>
                <div style="margin-top: 20px;">
                    <a href="/" style="color: white;">← العودة للصفحة الرئيسية</a>
                </div>
            </div>
        </body>
        </html>
        '''

    @app.route('/dashboard')
    def dashboard():
        from flask import session, redirect, url_for

        # التحقق من تسجيل الدخول
        if 'user_id' not in session:
            return redirect(url_for('login'))

        username = session.get('username', 'المستخدم')

        return f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>BANGHALAU - لوحة التحكم</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
            <style>
                :root {{
                    --primary-color: #2563eb;
                    --secondary-color: #1e40af;
                    --accent-color: #3b82f6;
                    --success-color: #10b981;
                    --warning-color: #f59e0b;
                    --danger-color: #ef4444;
                    --dark-color: #1f2937;
                    --light-color: #f8fafc;
                    --sidebar-width: 280px;
                }}

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: #f1f5f9;
                    color: var(--dark-color);
                    line-height: 1.6;
                }}

                /* Modern Sidebar */
                .sidebar {{
                    position: fixed;
                    top: 0;
                    right: 0;
                    width: var(--sidebar-width);
                    height: 100vh;
                    background: #ffffff;
                    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
                    z-index: 1000;
                    transition: transform 0.3s ease;
                    overflow-y: auto;
                }}

                .sidebar-brand {{
                    padding: 2rem 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    text-align: center;
                }}

                .brand-logo {{
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 1rem;
                    color: white;
                    font-size: 1.5rem;
                    font-weight: bold;
                }}

                .brand-text h3 {{
                    font-size: 1.25rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    margin-bottom: 0.25rem;
                }}

                .brand-text p {{
                    font-size: 0.875rem;
                    color: #64748b;
                }}

                .sidebar-nav {{
                    padding: 1rem 0;
                }}

                .nav-section {{
                    margin-bottom: 2rem;
                }}

                .nav-section-title {{
                    padding: 0 1.5rem 0.5rem;
                    font-size: 0.75rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    color: #94a3b8;
                }}

                .nav-item {{
                    margin: 0.25rem 1rem;
                }}

                .nav-link {{
                    display: flex;
                    align-items: center;
                    padding: 0.75rem 1rem;
                    color: #64748b;
                    text-decoration: none;
                    border-radius: 12px;
                    transition: all 0.2s ease;
                    font-weight: 500;
                    position: relative;
                }}

                .nav-link:hover {{
                    background: #f1f5f9;
                    color: var(--primary-color);
                    transform: translateX(-4px);
                }}

                .nav-link.active {{
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    color: white;
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                }}

                .nav-link i {{
                    margin-left: 0.75rem;
                    width: 20px;
                    text-align: center;
                    font-size: 1.1rem;
                }}

                .nav-badge {{
                    margin-right: auto;
                    background: var(--danger-color);
                    color: white;
                    font-size: 0.75rem;
                    padding: 0.25rem 0.5rem;
                    border-radius: 10px;
                    font-weight: 600;
                    min-width: 20px;
                    text-align: center;
                }}

                /* Main Content Area */
                .main-wrapper {{
                    margin-right: var(--sidebar-width);
                    min-height: 100vh;
                    transition: margin-right 0.3s ease;
                }}

                .main-header {{
                    background: white;
                    padding: 1.5rem 2rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}

                .header-title {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .menu-toggle {{
                    display: none;
                    background: none;
                    border: none;
                    font-size: 1.25rem;
                    color: var(--dark-color);
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 8px;
                }}

                .menu-toggle:hover {{
                    background: #f1f5f9;
                }}

                .page-title {{
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: var(--dark-color);
                }}

                .header-actions {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .user-profile {{
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0.5rem 1rem;
                    background: #f8fafc;
                    border-radius: 12px;
                    cursor: pointer;
                    transition: background 0.2s ease;
                }}

                .user-profile:hover {{
                    background: #e2e8f0;
                }}

                .user-avatar {{
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                }}

                .user-info h4 {{
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: var(--dark-color);
                }}

                .user-info p {{
                    font-size: 0.75rem;
                    color: #64748b;
                }}

                /* Dashboard Content */
                .dashboard-content {{
                    padding: 2rem;
                }}

                .stats-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 1.5rem;
                    margin-bottom: 2rem;
                }}

                .stat-card {{
                    background: white;
                    padding: 1.5rem;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    transition: all 0.2s ease;
                }}

                .stat-card:hover {{
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }}

                .stat-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1rem;
                }}

                .stat-icon {{
                    width: 48px;
                    height: 48px;
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1.25rem;
                    color: white;
                }}

                .stat-icon.primary {{ background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }}
                .stat-icon.success {{ background: linear-gradient(135deg, var(--success-color), #059669); }}
                .stat-icon.warning {{ background: linear-gradient(135deg, var(--warning-color), #d97706); }}
                .stat-icon.danger {{ background: linear-gradient(135deg, var(--danger-color), #dc2626); }}

                .stat-value {{
                    font-size: 2rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    margin-bottom: 0.25rem;
                }}

                .stat-label {{
                    font-size: 0.875rem;
                    color: #64748b;
                    font-weight: 500;
                }}

                .stat-change {{
                    font-size: 0.75rem;
                    font-weight: 600;
                    margin-top: 0.5rem;
                }}

                .stat-change.positive {{ color: var(--success-color); }}
                .stat-change.negative {{ color: var(--danger-color); }}

                /* Quick Actions */
                .quick-actions {{
                    background: white;
                    padding: 1.5rem;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                }}

                .section-title {{
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: var(--dark-color);
                    margin-bottom: 1rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                .actions-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1rem;
                }}

                .action-card {{
                    display: flex;
                    align-items: center;
                    padding: 1rem;
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 12px;
                    text-decoration: none;
                    color: var(--dark-color);
                    transition: all 0.2s ease;
                }}

                .action-card:hover {{
                    background: var(--primary-color);
                    color: white;
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                    text-decoration: none;
                }}

                .action-icon {{
                    width: 40px;
                    height: 40px;
                    background: var(--primary-color);
                    color: white;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 0.75rem;
                    transition: background 0.2s ease;
                }}

                .action-card:hover .action-icon {{
                    background: rgba(255, 255, 255, 0.2);
                }}

                .action-text h4 {{
                    font-size: 0.875rem;
                    font-weight: 600;
                    margin-bottom: 0.25rem;
                }}

                .action-text p {{
                    font-size: 0.75rem;
                    opacity: 0.8;
                }}

                /* Mobile Responsive */
                @media (max-width: 768px) {{
                    .sidebar {{
                        transform: translateX(100%);
                    }}

                    .sidebar.active {{
                        transform: translateX(0);
                    }}

                    .main-wrapper {{
                        margin-right: 0;
                    }}

                    .menu-toggle {{
                        display: block;
                    }}

                    .main-header {{
                        padding: 1rem;
                    }}

                    .dashboard-content {{
                        padding: 1rem;
                    }}

                    .stats-grid {{
                        grid-template-columns: 1fr;
                    }}

                    .actions-grid {{
                        grid-template-columns: 1fr;
                    }}
                }}

                .sidebar-overlay {{
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 999;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }}

                .sidebar-overlay.active {{
                    opacity: 1;
                    visibility: visible;
                }}
            </style>
        </head>
        <body>
            <!-- Sidebar Overlay -->
            <div class="sidebar-overlay" id="sidebarOverlay"></div>

            <!-- Modern Sidebar -->
            <div class="sidebar" id="sidebar">
                <div class="sidebar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="brand-text">
                        <h3>BANGHALAU</h3>
                        <p>نظام إدارة البنغالوهات</p>
                    </div>
                </div>

                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <div class="nav-section-title">الرئيسية</div>
                        <div class="nav-item">
                            <a href="/dashboard" class="nav-link active">
                                <i class="fas fa-chart-pie"></i>
                                <span>لوحة التحكم</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/analytics" class="nav-link">
                                <i class="fas fa-chart-line"></i>
                                <span>التحليلات</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-title">الإدارة</div>
                        <div class="nav-item">
                            <a href="/bungalows" class="nav-link">
                                <i class="fas fa-building"></i>
                                <span>البنغالوهات</span>
                                <span class="nav-badge">25</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/personnel" class="nav-link">
                                <i class="fas fa-users"></i>
                                <span>الأفراد</span>
                                <span class="nav-badge">150</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/distributions" class="nav-link">
                                <i class="fas fa-exchange-alt"></i>
                                <span>التوزيعات</span>
                                <span class="nav-badge">45</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-title">النظام</div>
                        <div class="nav-item">
                            <a href="/grades" class="nav-link">
                                <i class="fas fa-star"></i>
                                <span>الرتب</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/units" class="nav-link">
                                <i class="fas fa-flag"></i>
                                <span>الوحدات</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/sessions" class="nav-link">
                                <i class="fas fa-calendar"></i>
                                <span>الجلسات</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-title">التقارير</div>
                        <div class="nav-item">
                            <a href="/reports" class="nav-link">
                                <i class="fas fa-file-alt"></i>
                                <span>التقارير</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/export" class="nav-link">
                                <i class="fas fa-download"></i>
                                <span>التصدير</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-item">
                            <a href="/settings" class="nav-link">
                                <i class="fas fa-cog"></i>
                                <span>الإعدادات</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/logout" class="nav-link" style="color: var(--danger-color);">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </nav>
            </div>

            <!-- Main Content Wrapper -->
            <div class="main-wrapper">
                <!-- Header -->
                <header class="main-header">
                    <div class="header-title">
                        <button class="menu-toggle" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="page-title">لوحة التحكم</h1>
                    </div>

                    <div class="header-actions">
                        <div class="user-profile">
                            <div class="user-avatar">
                                {username[0].upper()}
                            </div>
                            <div class="user-info">
                                <h4>{username}</h4>
                                <p>مدير النظام</p>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Dashboard Content -->
                <main class="dashboard-content">
                    <!-- Statistics Cards -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon primary">
                                    <i class="fas fa-building"></i>
                                </div>
                            </div>
                            <div class="stat-value">25</div>
                            <div class="stat-label">إجمالي البنغالوهات</div>
                            <div class="stat-change positive">+2 هذا الشهر</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon success">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="stat-value">150</div>
                            <div class="stat-label">إجمالي الأفراد</div>
                            <div class="stat-change positive">+12 هذا الشهر</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon warning">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                            </div>
                            <div class="stat-value">45</div>
                            <div class="stat-label">التوزيعات النشطة</div>
                            <div class="stat-change positive">+8 هذا الأسبوع</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon danger">
                                    <i class="fas fa-chart-pie"></i>
                                </div>
                            </div>
                            <div class="stat-value">80%</div>
                            <div class="stat-label">معدل الإشغال</div>
                            <div class="stat-change positive">مستقر</div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <h2 class="section-title">
                            <i class="fas fa-bolt"></i>
                            الإجراءات السريعة
                        </h2>
                        <div class="actions-grid">
                            <a href="/bungalows/add" class="action-card">
                                <div class="action-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="action-text">
                                    <h4>إضافة بنغالو</h4>
                                    <p>إضافة بنغالو جديد للنظام</p>
                                </div>
                            </a>

                            <a href="/personnel/add" class="action-card">
                                <div class="action-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="action-text">
                                    <h4>إضافة فرد</h4>
                                    <p>تسجيل فرد عسكري جديد</p>
                                </div>
                            </a>

                            <a href="/distributions/new" class="action-card">
                                <div class="action-icon">
                                    <i class="fas fa-clipboard-check"></i>
                                </div>
                                <div class="action-text">
                                    <h4>توزيع جديد</h4>
                                    <p>إنشاء توزيع بنغالوهات</p>
                                </div>
                            </a>

                            <a href="/reports" class="action-card">
                                <div class="action-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="action-text">
                                    <h4>التقارير</h4>
                                    <p>عرض التقارير والإحصائيات</p>
                                </div>
                            </a>

                            <a href="/export" class="action-card">
                                <div class="action-icon">
                                    <i class="fas fa-download"></i>
                                </div>
                                <div class="action-text">
                                    <h4>تصدير البيانات</h4>
                                    <p>تصدير البيانات بصيغ مختلفة</p>
                                </div>
                            </a>

                            <a href="/settings" class="action-card">
                                <div class="action-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="action-text">
                                    <h4>الإعدادات</h4>
                                    <p>إعدادات النظام والتفضيلات</p>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="quick-actions">
                        <h2 class="section-title">
                            <i class="fas fa-clock"></i>
                            النشاط الأخير
                        </h2>
                        <div style="display: grid; gap: 1rem;">
                            <div style="background: #f8fafc; padding: 1rem; border-radius: 12px; border-right: 4px solid var(--primary-color);">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <h4 style="margin: 0; font-size: 0.875rem; font-weight: 600;">تم إضافة بنغالو جديد</h4>
                                        <p style="margin: 0.25rem 0 0; font-size: 0.75rem; color: #64748b;">البنغالو رقم B026 - المنطقة الغربية</p>
                                    </div>
                                    <span style="color: var(--primary-color); font-size: 0.75rem; font-weight: 500;">منذ ساعتين</span>
                                </div>
                            </div>

                            <div style="background: #f8fafc; padding: 1rem; border-radius: 12px; border-right: 4px solid var(--success-color);">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <h4 style="margin: 0; font-size: 0.875rem; font-weight: 600;">تم تسجيل فرد جديد</h4>
                                        <p style="margin: 0.25rem 0 0; font-size: 0.75rem; color: #64748b;">الرقم العسكري: 12345 - رتبة: نقيب</p>
                                    </div>
                                    <span style="color: var(--success-color); font-size: 0.75rem; font-weight: 500;">منذ 4 ساعات</span>
                                </div>
                            </div>

                            <div style="background: #f8fafc; padding: 1rem; border-radius: 12px; border-right: 4px solid var(--warning-color);">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <h4 style="margin: 0; font-size: 0.875rem; font-weight: 600;">تم إنجاز توزيع جديد</h4>
                                        <p style="margin: 0.25rem 0 0; font-size: 0.75rem; color: #64748b;">توزيع 5 أفراد على البنغالوهات المتاحة</p>
                                    </div>
                                    <span style="color: var(--warning-color); font-size: 0.75rem; font-weight: 500;">أمس</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>

            <script>
                // Modern Sidebar Toggle
                function toggleSidebar() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.toggle('active');
                    overlay.classList.toggle('active');
                }}

                // Close sidebar when clicking overlay
                document.getElementById('sidebarOverlay').addEventListener('click', function() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                }});

                // Initialize dashboard
                document.addEventListener('DOMContentLoaded', function() {{
                    // Animate statistics values
                    const statValues = document.querySelectorAll('.stat-value');
                    statValues.forEach(stat => {{
                        const finalValue = stat.textContent;
                        const isPercentage = finalValue.includes('%');
                        const numericValue = parseInt(finalValue);

                        if (!isNaN(numericValue)) {{
                            stat.textContent = '0';
                            let current = 0;
                            const increment = Math.ceil(numericValue / 30);

                            const timer = setInterval(() => {{
                                current += increment;
                                if (current >= numericValue) {{
                                    stat.textContent = finalValue;
                                    clearInterval(timer);
                                }} else {{
                                    stat.textContent = current + (isPercentage ? '%' : '');
                                }}
                            }}, 50);
                        }}
                    }});

                    // Add hover effects to action cards
                    const actionCards = document.querySelectorAll('.action-card');
                    actionCards.forEach(card => {{
                        card.addEventListener('mouseenter', function() {{
                            this.style.transform = 'translateY(-4px)';
                        }});

                        card.addEventListener('mouseleave', function() {{
                            this.style.transform = 'translateY(0)';
                        }});
                    }});

                    // Auto-close sidebar on mobile navigation
                    const navLinks = document.querySelectorAll('.nav-link');
                    navLinks.forEach(link => {{
                        link.addEventListener('click', function() {{
                            if (window.innerWidth <= 768) {{
                                const sidebar = document.getElementById('sidebar');
                                const overlay = document.getElementById('sidebarOverlay');

                                sidebar.classList.remove('active');
                                overlay.classList.remove('active');
                            }}
                        }});
                    }});

                    // Add loading states to action cards
                    actionCards.forEach(card => {{
                        card.addEventListener('click', function(e) {{
                            const icon = this.querySelector('.action-icon i');
                            const originalIcon = icon.className;

                            icon.className = 'fas fa-spinner fa-spin';

                            setTimeout(() => {{
                                icon.className = originalIcon;
                            }}, 1000);
                        }});
                    }});
                }});

                // Handle window resize
                window.addEventListener('resize', function() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    if (window.innerWidth > 768) {{
                        sidebar.classList.remove('active');
                        overlay.classList.remove('active');
                    }}
                }});

                // Add smooth transitions
                document.querySelectorAll('a').forEach(link => {{
                    link.addEventListener('click', function(e) {{
                        if (this.href && !this.href.includes('#')) {{
                            e.preventDefault();

                            // Add loading effect
                            document.body.style.opacity = '0.8';
                            document.body.style.transition = 'opacity 0.3s ease';

                            setTimeout(() => {{
                                window.location.href = this.href;
                            }}, 200);
                        }}
                    }});
                }});
            </script>
        </body>
        </html>
        '''

    @app.route('/logout')
    def logout():
        from flask import session, redirect, url_for
        session.clear()
        return redirect(url_for('home'))

    @app.route('/about')
    def about():
        return '''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>حول النظام - BANGHALAU</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 50px;
                    color: white;
                    text-align: center;
                    min-height: 100vh;
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                    background: rgba(255, 255, 255, 0.1);
                    padding: 40px;
                    border-radius: 20px;
                    backdrop-filter: blur(10px);
                }
                .feature {
                    background: rgba(255, 255, 255, 0.1);
                    padding: 20px;
                    margin: 15px 0;
                    border-radius: 10px;
                    text-align: right;
                }
                .btn {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 12px 24px;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 25px;
                    text-decoration: none;
                    display: inline-block;
                    margin: 10px;
                    transition: all 0.3s ease;
                }
                .btn:hover {
                    background: rgba(255, 255, 255, 0.3);
                    color: white;
                    text-decoration: none;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🏠 حول نظام BANGHALAU</h1>
                <h2>نظام إدارة توزيع البنغالوهات للأفراد العسكريين</h2>

                <div class="feature">
                    <h3>🎯 الهدف من النظام</h3>
                    <p>تسهيل وتنظيم عملية توزيع البنغالوهات على الأفراد العسكريين بطريقة عادلة وفعالة</p>
                </div>

                <div class="feature">
                    <h3>⚡ الميزات الرئيسية</h3>
                    <ul style="text-align: right;">
                        <li>إدارة شاملة للبنغالوهات والأفراد</li>
                        <li>نظام توزيع ذكي ومرن</li>
                        <li>تقارير وإحصائيات مفصلة</li>
                        <li>واجهة عربية سهلة الاستخدام</li>
                        <li>نظام أمان متقدم</li>
                    </ul>
                </div>

                <div class="feature">
                    <h3>🛠️ التقنيات المستخدمة</h3>
                    <p>Python Flask • HTML5 • CSS3 • JavaScript • SQLite</p>
                </div>

                <div>
                    <a href="/" class="btn">🏠 الصفحة الرئيسية</a>
                    <a href="/dashboard" class="btn">📊 لوحة التحكم</a>
                </div>
            </div>
        </body>
        </html>
        '''

    # صفحات إدارة البنغالوهات
    @app.route('/bungalows')
    def bungalows():
        from flask import session, redirect, url_for
        if 'user_id' not in session:
            return redirect(url_for('login'))

        username = session.get('username', 'المستخدم')

        return f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>إدارة البنغالوهات - BANGHALAU</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
            <style>
                :root {{
                    --primary-color: #2563eb;
                    --secondary-color: #1e40af;
                    --accent-color: #3b82f6;
                    --success-color: #10b981;
                    --warning-color: #f59e0b;
                    --danger-color: #ef4444;
                    --dark-color: #1f2937;
                    --light-color: #f8fafc;
                    --sidebar-width: 280px;
                }}

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: #f1f5f9;
                    color: var(--dark-color);
                    line-height: 1.6;
                }}

                /* Sidebar Styles (Same as dashboard) */
                .sidebar {{
                    position: fixed;
                    top: 0;
                    right: 0;
                    width: var(--sidebar-width);
                    height: 100vh;
                    background: #ffffff;
                    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
                    z-index: 1000;
                    transition: transform 0.3s ease;
                    overflow-y: auto;
                }}

                .sidebar-brand {{
                    padding: 2rem 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    text-align: center;
                }}

                .brand-logo {{
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 1rem;
                    color: white;
                    font-size: 1.5rem;
                    font-weight: bold;
                }}

                .brand-text h3 {{
                    font-size: 1.25rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    margin-bottom: 0.25rem;
                }}

                .brand-text p {{
                    font-size: 0.875rem;
                    color: #64748b;
                }}

                .sidebar-nav {{
                    padding: 1rem 0;
                }}

                .nav-section {{
                    margin-bottom: 2rem;
                }}

                .nav-section-title {{
                    padding: 0 1.5rem 0.5rem;
                    font-size: 0.75rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    color: #94a3b8;
                }}

                .nav-item {{
                    margin: 0.25rem 1rem;
                }}

                .nav-link {{
                    display: flex;
                    align-items: center;
                    padding: 0.75rem 1rem;
                    color: #64748b;
                    text-decoration: none;
                    border-radius: 12px;
                    transition: all 0.2s ease;
                    font-weight: 500;
                    position: relative;
                }}

                .nav-link:hover {{
                    background: #f1f5f9;
                    color: var(--primary-color);
                    transform: translateX(-4px);
                }}

                .nav-link.active {{
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    color: white;
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                }}

                .nav-link i {{
                    margin-left: 0.75rem;
                    width: 20px;
                    text-align: center;
                    font-size: 1.1rem;
                }}

                .nav-badge {{
                    margin-right: auto;
                    background: var(--danger-color);
                    color: white;
                    font-size: 0.75rem;
                    padding: 0.25rem 0.5rem;
                    border-radius: 10px;
                    font-weight: 600;
                    min-width: 20px;
                    text-align: center;
                }}

                /* Main Content */
                .main-wrapper {{
                    margin-right: var(--sidebar-width);
                    min-height: 100vh;
                    transition: margin-right 0.3s ease;
                }}

                .main-header {{
                    background: white;
                    padding: 1.5rem 2rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}

                .header-title {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .menu-toggle {{
                    display: none;
                    background: none;
                    border: none;
                    font-size: 1.25rem;
                    color: var(--dark-color);
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 8px;
                }}

                .menu-toggle:hover {{
                    background: #f1f5f9;
                }}

                .page-title {{
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: var(--dark-color);
                }}

                .header-actions {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .user-profile {{
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0.5rem 1rem;
                    background: #f8fafc;
                    border-radius: 12px;
                    cursor: pointer;
                    transition: background 0.2s ease;
                }}

                .user-profile:hover {{
                    background: #e2e8f0;
                }}

                .user-avatar {{
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                }}

                .user-info h4 {{
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: var(--dark-color);
                }}

                .user-info p {{
                    font-size: 0.75rem;
                    color: #64748b;
                }}

                /* Page Content */
                .page-content {{
                    padding: 2rem;
                }}

                .page-header {{
                    background: white;
                    padding: 1.5rem;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                }}

                .page-header-content {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }}

                .page-header h1 {{
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                .page-header p {{
                    color: #64748b;
                    margin-top: 0.5rem;
                }}

                .header-actions-group {{
                    display: flex;
                    gap: 0.75rem;
                    flex-wrap: wrap;
                }}

                .btn {{
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1rem;
                    border: none;
                    border-radius: 12px;
                    font-size: 0.875rem;
                    font-weight: 500;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }}

                .btn-primary {{
                    background: var(--primary-color);
                    color: white;
                }}

                .btn-primary:hover {{
                    background: var(--secondary-color);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                    color: white;
                    text-decoration: none;
                }}

                .btn-secondary {{
                    background: #f1f5f9;
                    color: var(--dark-color);
                    border: 1px solid #e2e8f0;
                }}

                .btn-secondary:hover {{
                    background: #e2e8f0;
                    color: var(--dark-color);
                    text-decoration: none;
                }}

                /* Data Table */
                .data-table-container {{
                    background: white;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }}

                .table-header {{
                    padding: 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }}

                .table-title {{
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: var(--dark-color);
                }}

                .table-filters {{
                    display: flex;
                    gap: 0.75rem;
                    align-items: center;
                }}

                .search-input {{
                    padding: 0.5rem 1rem;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    width: 250px;
                }}

                .search-input:focus {{
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                }}

                .filter-select {{
                    padding: 0.5rem 1rem;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    background: white;
                }}

                .data-table {{
                    width: 100%;
                    border-collapse: collapse;
                }}

                .data-table th,
                .data-table td {{
                    padding: 1rem;
                    text-align: right;
                    border-bottom: 1px solid #f1f5f9;
                }}

                .data-table th {{
                    background: #f8fafc;
                    font-weight: 600;
                    color: var(--dark-color);
                    font-size: 0.875rem;
                }}

                .data-table td {{
                    font-size: 0.875rem;
                    color: #64748b;
                }}

                .data-table tbody tr:hover {{
                    background: #f8fafc;
                }}

                /* Status Badges */
                .status-badge {{
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    border-radius: 20px;
                    font-size: 0.75rem;
                    font-weight: 600;
                }}

                .status-available {{
                    background: #dcfce7;
                    color: #166534;
                }}

                .status-occupied {{
                    background: #fee2e2;
                    color: #991b1b;
                }}

                .status-maintenance {{
                    background: #fef3c7;
                    color: #92400e;
                }}

                /* Action Buttons */
                .action-buttons {{
                    display: flex;
                    gap: 0.5rem;
                }}

                .btn-sm {{
                    padding: 0.5rem 0.75rem;
                    font-size: 0.75rem;
                }}

                .btn-edit {{
                    background: #f59e0b;
                    color: white;
                }}

                .btn-edit:hover {{
                    background: #d97706;
                    color: white;
                    text-decoration: none;
                }}

                .btn-delete {{
                    background: var(--danger-color);
                    color: white;
                }}

                .btn-delete:hover {{
                    background: #dc2626;
                    color: white;
                    text-decoration: none;
                }}

                .btn-view {{
                    background: var(--primary-color);
                    color: white;
                }}

                .btn-view:hover {{
                    background: var(--secondary-color);
                    color: white;
                    text-decoration: none;
                }}

                /* Mobile Responsive */
                @media (max-width: 768px) {{
                    .sidebar {{
                        transform: translateX(100%);
                    }}

                    .sidebar.active {{
                        transform: translateX(0);
                    }}

                    .main-wrapper {{
                        margin-right: 0;
                    }}

                    .menu-toggle {{
                        display: block;
                    }}

                    .main-header {{
                        padding: 1rem;
                    }}

                    .page-content {{
                        padding: 1rem;
                    }}

                    .page-header-content {{
                        flex-direction: column;
                        align-items: stretch;
                    }}

                    .header-actions-group {{
                        justify-content: stretch;
                    }}

                    .search-input {{
                        width: 100%;
                    }}

                    .table-header {{
                        flex-direction: column;
                        align-items: stretch;
                    }}

                    .table-filters {{
                        flex-direction: column;
                    }}

                    .data-table {{
                        font-size: 0.75rem;
                    }}

                    .data-table th,
                    .data-table td {{
                        padding: 0.5rem;
                    }}

                    .action-buttons {{
                        flex-direction: column;
                    }}
                }}

                .sidebar-overlay {{
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 999;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }}

                .sidebar-overlay.active {{
                    opacity: 1;
                    visibility: visible;
                }}
            </style>
        </head>
        <body>
            <!-- Sidebar Overlay -->
            <div class="sidebar-overlay" id="sidebarOverlay"></div>

            <!-- Sidebar -->
            <div class="sidebar" id="sidebar">
                <div class="sidebar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="brand-text">
                        <h3>BANGHALAU</h3>
                        <p>نظام إدارة البنغالوهات</p>
                    </div>
                </div>

                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <div class="nav-section-title">الرئيسية</div>
                        <div class="nav-item">
                            <a href="/dashboard" class="nav-link">
                                <i class="fas fa-chart-pie"></i>
                                <span>لوحة التحكم</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-title">الإدارة</div>
                        <div class="nav-item">
                            <a href="/bungalows" class="nav-link active">
                                <i class="fas fa-building"></i>
                                <span>البنغالوهات</span>
                                <span class="nav-badge">25</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/personnel" class="nav-link">
                                <i class="fas fa-users"></i>
                                <span>الأفراد</span>
                                <span class="nav-badge">150</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/distributions" class="nav-link">
                                <i class="fas fa-exchange-alt"></i>
                                <span>التوزيعات</span>
                                <span class="nav-badge">45</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-item">
                            <a href="/logout" class="nav-link" style="color: var(--danger-color);">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="main-wrapper">
                <!-- Header -->
                <header class="main-header">
                    <div class="header-title">
                        <button class="menu-toggle" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="page-title">إدارة البنغالوهات</h1>
                    </div>

                    <div class="header-actions">
                        <div class="user-profile">
                            <div class="user-avatar">
                                {username[0].upper()}
                            </div>
                            <div class="user-info">
                                <h4>{username}</h4>
                                <p>مدير النظام</p>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Page Content -->
                <main class="page-content">
                    <!-- Page Header -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <div>
                                <h1>
                                    <i class="fas fa-building"></i>
                                    إدارة البنغالوهات
                                </h1>
                                <p>إدارة وتنظيم جميع البنغالوهات في النظام</p>
                            </div>
                            <div class="header-actions-group">
                                <a href="/bungalows/add" class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    إضافة بنغالو جديد
                                </a>
                                <a href="/dashboard" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right"></i>
                                    العودة للوحة التحكم
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <div class="data-table-container">
                        <div class="table-header">
                            <h2 class="table-title">قائمة البنغالوهات</h2>
                            <div class="table-filters">
                                <input type="text" class="search-input" placeholder="البحث في البنغالوهات...">
                                <select class="filter-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="available">متاح</option>
                                    <option value="occupied">مشغول</option>
                                    <option value="maintenance">صيانة</option>
                                </select>
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم البنغالو</th>
                                    <th>الموقع</th>
                                    <th>السعة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>B001</strong></td>
                                    <td>المنطقة الشمالية</td>
                                    <td>4 أشخاص</td>
                                    <td>
                                        <span class="status-badge status-available">
                                            <i class="fas fa-check-circle"></i>
                                            متاح
                                        </span>
                                    </td>
                                    <td>2024/01/15</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="#" class="btn btn-view btn-sm">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <a href="#" class="btn btn-edit btn-sm">
                                                <i class="fas fa-edit"></i>
                                                تعديل
                                            </a>
                                            <a href="#" class="btn btn-delete btn-sm">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>B002</strong></td>
                                    <td>المنطقة الجنوبية</td>
                                    <td>6 أشخاص</td>
                                    <td>
                                        <span class="status-badge status-occupied">
                                            <i class="fas fa-user"></i>
                                            مشغول
                                        </span>
                                    </td>
                                    <td>2024/01/20</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="#" class="btn btn-view btn-sm">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <a href="#" class="btn btn-edit btn-sm">
                                                <i class="fas fa-edit"></i>
                                                تعديل
                                            </a>
                                            <a href="#" class="btn btn-delete btn-sm">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>B003</strong></td>
                                    <td>المنطقة الشرقية</td>
                                    <td>8 أشخاص</td>
                                    <td>
                                        <span class="status-badge status-maintenance">
                                            <i class="fas fa-tools"></i>
                                            صيانة
                                        </span>
                                    </td>
                                    <td>2024/02/01</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="#" class="btn btn-view btn-sm">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <a href="#" class="btn btn-edit btn-sm">
                                                <i class="fas fa-edit"></i>
                                                تعديل
                                            </a>
                                            <a href="#" class="btn btn-delete btn-sm">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>B004</strong></td>
                                    <td>المنطقة الغربية</td>
                                    <td>4 أشخاص</td>
                                    <td>
                                        <span class="status-badge status-available">
                                            <i class="fas fa-check-circle"></i>
                                            متاح
                                        </span>
                                    </td>
                                    <td>2024/02/10</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="#" class="btn btn-view btn-sm">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <a href="#" class="btn btn-edit btn-sm">
                                                <i class="fas fa-edit"></i>
                                                تعديل
                                            </a>
                                            <a href="#" class="btn btn-delete btn-sm">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>B005</strong></td>
                                    <td>المنطقة الوسطى</td>
                                    <td>6 أشخاص</td>
                                    <td>
                                        <span class="status-badge status-occupied">
                                            <i class="fas fa-user"></i>
                                            مشغول
                                        </span>
                                    </td>
                                    <td>2024/02/15</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="#" class="btn btn-view btn-sm">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <a href="#" class="btn btn-edit btn-sm">
                                                <i class="fas fa-edit"></i>
                                                تعديل
                                            </a>
                                            <a href="#" class="btn btn-delete btn-sm">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </main>
            </div>

            <script>
                // Sidebar Toggle
                function toggleSidebar() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.toggle('active');
                    overlay.classList.toggle('active');
                }}

                // Close sidebar when clicking overlay
                document.getElementById('sidebarOverlay').addEventListener('click', function() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                }});

                // Search functionality
                document.querySelector('.search-input').addEventListener('input', function() {{
                    const searchTerm = this.value.toLowerCase();
                    const rows = document.querySelectorAll('.data-table tbody tr');

                    rows.forEach(row => {{
                        const text = row.textContent.toLowerCase();
                        row.style.display = text.includes(searchTerm) ? '' : 'none';
                    }});
                }});

                // Filter functionality
                document.querySelector('.filter-select').addEventListener('change', function() {{
                    const filterValue = this.value;
                    const rows = document.querySelectorAll('.data-table tbody tr');

                    rows.forEach(row => {{
                        if (!filterValue) {{
                            row.style.display = '';
                        }} else {{
                            const statusBadge = row.querySelector('.status-badge');
                            const hasStatus = statusBadge.classList.contains('status-' + filterValue);
                            row.style.display = hasStatus ? '' : 'none';
                        }}
                    }});
                }});

                // Delete confirmation
                document.querySelectorAll('.btn-delete').forEach(btn => {{
                    btn.addEventListener('click', function(e) {{
                        e.preventDefault();
                        if (confirm('هل أنت متأكد من حذف هذا البنغالو؟')) {{
                            // Handle delete action here
                            alert('تم حذف البنغالو بنجاح');
                        }}
                    }});
                }});
            </script>
        </body>
        </html>
        '''

    @app.route('/bungalows/add')
    def add_bungalow():
        from flask import session, redirect, url_for
        if 'user_id' not in session:
            return redirect(url_for('login'))

        username = session.get('username', 'المستخدم')

        return f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>إضافة بنغالو جديد - BANGHALAU</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
            <style>
                :root {{
                    --primary-color: #2563eb;
                    --secondary-color: #1e40af;
                    --accent-color: #3b82f6;
                    --success-color: #10b981;
                    --warning-color: #f59e0b;
                    --danger-color: #ef4444;
                    --dark-color: #1f2937;
                    --light-color: #f8fafc;
                    --sidebar-width: 280px;
                }}

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: #f1f5f9;
                    color: var(--dark-color);
                    line-height: 1.6;
                }}

                /* Sidebar Styles */
                .sidebar {{
                    position: fixed;
                    top: 0;
                    right: 0;
                    width: var(--sidebar-width);
                    height: 100vh;
                    background: #ffffff;
                    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
                    z-index: 1000;
                    transition: transform 0.3s ease;
                    overflow-y: auto;
                }}

                .sidebar-brand {{
                    padding: 2rem 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    text-align: center;
                }}

                .brand-logo {{
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 1rem;
                    color: white;
                    font-size: 1.5rem;
                    font-weight: bold;
                }}

                .brand-text h3 {{
                    font-size: 1.25rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    margin-bottom: 0.25rem;
                }}

                .brand-text p {{
                    font-size: 0.875rem;
                    color: #64748b;
                }}

                .sidebar-nav {{
                    padding: 1rem 0;
                }}

                .nav-section {{
                    margin-bottom: 2rem;
                }}

                .nav-section-title {{
                    padding: 0 1.5rem 0.5rem;
                    font-size: 0.75rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    color: #94a3b8;
                }}

                .nav-item {{
                    margin: 0.25rem 1rem;
                }}

                .nav-link {{
                    display: flex;
                    align-items: center;
                    padding: 0.75rem 1rem;
                    color: #64748b;
                    text-decoration: none;
                    border-radius: 12px;
                    transition: all 0.2s ease;
                    font-weight: 500;
                    position: relative;
                }}

                .nav-link:hover {{
                    background: #f1f5f9;
                    color: var(--primary-color);
                    transform: translateX(-4px);
                }}

                .nav-link.active {{
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    color: white;
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                }}

                .nav-link i {{
                    margin-left: 0.75rem;
                    width: 20px;
                    text-align: center;
                    font-size: 1.1rem;
                }}

                .nav-badge {{
                    margin-right: auto;
                    background: var(--danger-color);
                    color: white;
                    font-size: 0.75rem;
                    padding: 0.25rem 0.5rem;
                    border-radius: 10px;
                    font-weight: 600;
                    min-width: 20px;
                    text-align: center;
                }}

                /* Main Content */
                .main-wrapper {{
                    margin-right: var(--sidebar-width);
                    min-height: 100vh;
                    transition: margin-right 0.3s ease;
                }}

                .main-header {{
                    background: white;
                    padding: 1.5rem 2rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}

                .header-title {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .menu-toggle {{
                    display: none;
                    background: none;
                    border: none;
                    font-size: 1.25rem;
                    color: var(--dark-color);
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 8px;
                }}

                .menu-toggle:hover {{
                    background: #f1f5f9;
                }}

                .page-title {{
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: var(--dark-color);
                }}

                .header-actions {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .user-profile {{
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0.5rem 1rem;
                    background: #f8fafc;
                    border-radius: 12px;
                    cursor: pointer;
                    transition: background 0.2s ease;
                }}

                .user-profile:hover {{
                    background: #e2e8f0;
                }}

                .user-avatar {{
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                }}

                .user-info h4 {{
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: var(--dark-color);
                }}

                .user-info p {{
                    font-size: 0.75rem;
                    color: #64748b;
                }}

                /* Page Content */
                .page-content {{
                    padding: 2rem;
                }}

                .page-header {{
                    background: white;
                    padding: 1.5rem;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                }}

                .page-header-content {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }}

                .page-header h1 {{
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                .page-header p {{
                    color: #64748b;
                    margin-top: 0.5rem;
                }}

                .header-actions-group {{
                    display: flex;
                    gap: 0.75rem;
                    flex-wrap: wrap;
                }}

                .btn {{
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1rem;
                    border: none;
                    border-radius: 12px;
                    font-size: 0.875rem;
                    font-weight: 500;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }}

                .btn-primary {{
                    background: var(--primary-color);
                    color: white;
                }}

                .btn-primary:hover {{
                    background: var(--secondary-color);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                    color: white;
                    text-decoration: none;
                }}

                .btn-secondary {{
                    background: #f1f5f9;
                    color: var(--dark-color);
                    border: 1px solid #e2e8f0;
                }}

                .btn-secondary:hover {{
                    background: #e2e8f0;
                    color: var(--dark-color);
                    text-decoration: none;
                }}

                .btn-success {{
                    background: var(--success-color);
                    color: white;
                }}

                .btn-success:hover {{
                    background: #059669;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
                    color: white;
                    text-decoration: none;
                }}

                /* Form Styles */
                .form-container {{
                    background: white;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }}

                .form-header {{
                    padding: 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    background: #f8fafc;
                }}

                .form-title {{
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: var(--dark-color);
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                .form-body {{
                    padding: 2rem;
                }}

                .form-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 1.5rem;
                    margin-bottom: 2rem;
                }}

                .form-group {{
                    margin-bottom: 1.5rem;
                }}

                .form-label {{
                    display: block;
                    margin-bottom: 0.5rem;
                    font-weight: 600;
                    color: var(--dark-color);
                    font-size: 0.875rem;
                }}

                .form-label.required::after {{
                    content: ' *';
                    color: var(--danger-color);
                }}

                .form-input {{
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    transition: all 0.2s ease;
                    background: white;
                }}

                .form-input:focus {{
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                }}

                .form-input:invalid {{
                    border-color: var(--danger-color);
                }}

                .form-textarea {{
                    resize: vertical;
                    min-height: 100px;
                }}

                .form-select {{
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    background: white;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }}

                .form-select:focus {{
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                }}

                .form-actions {{
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                }}

                .input-group {{
                    position: relative;
                }}

                .input-icon {{
                    position: absolute;
                    right: 1rem;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #64748b;
                    pointer-events: none;
                }}

                .form-input.with-icon {{
                    padding-right: 3rem;
                }}

                /* Mobile Responsive */
                @media (max-width: 768px) {{
                    .sidebar {{
                        transform: translateX(100%);
                    }}

                    .sidebar.active {{
                        transform: translateX(0);
                    }}

                    .main-wrapper {{
                        margin-right: 0;
                    }}

                    .menu-toggle {{
                        display: block;
                    }}

                    .main-header {{
                        padding: 1rem;
                    }}

                    .page-content {{
                        padding: 1rem;
                    }}

                    .page-header-content {{
                        flex-direction: column;
                        align-items: stretch;
                    }}

                    .header-actions-group {{
                        justify-content: stretch;
                    }}

                    .form-grid {{
                        grid-template-columns: 1fr;
                    }}

                    .form-actions {{
                        flex-direction: column;
                    }}
                }}

                .sidebar-overlay {{
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 999;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }}

                .sidebar-overlay.active {{
                    opacity: 1;
                    visibility: visible;
                }}
            </style>
        </head>
        <body>
            <!-- Sidebar Overlay -->
            <div class="sidebar-overlay" id="sidebarOverlay"></div>

            <!-- Sidebar -->
            <div class="sidebar" id="sidebar">
                <div class="sidebar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="brand-text">
                        <h3>BANGHALAU</h3>
                        <p>نظام إدارة البنغالوهات</p>
                    </div>
                </div>

                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <div class="nav-section-title">الرئيسية</div>
                        <div class="nav-item">
                            <a href="/dashboard" class="nav-link">
                                <i class="fas fa-chart-pie"></i>
                                <span>لوحة التحكم</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-title">الإدارة</div>
                        <div class="nav-item">
                            <a href="/bungalows" class="nav-link active">
                                <i class="fas fa-building"></i>
                                <span>البنغالوهات</span>
                                <span class="nav-badge">25</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/personnel" class="nav-link">
                                <i class="fas fa-users"></i>
                                <span>الأفراد</span>
                                <span class="nav-badge">150</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/distributions" class="nav-link">
                                <i class="fas fa-exchange-alt"></i>
                                <span>التوزيعات</span>
                                <span class="nav-badge">45</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-item">
                            <a href="/logout" class="nav-link" style="color: var(--danger-color);">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="main-wrapper">
                <!-- Header -->
                <header class="main-header">
                    <div class="header-title">
                        <button class="menu-toggle" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="page-title">إضافة بنغالو جديد</h1>
                    </div>

                    <div class="header-actions">
                        <div class="user-profile">
                            <div class="user-avatar">
                                {username[0].upper()}
                            </div>
                            <div class="user-info">
                                <h4>{username}</h4>
                                <p>مدير النظام</p>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Page Content -->
                <main class="page-content">
                    <!-- Page Header -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <div>
                                <h1>
                                    <i class="fas fa-plus-circle"></i>
                                    إضافة بنغالو جديد
                                </h1>
                                <p>إضافة بنغالو جديد إلى النظام</p>
                            </div>
                            <div class="header-actions-group">
                                <a href="/bungalows" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right"></i>
                                    العودة لقائمة البنغالوهات
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Form Container -->
                    <div class="form-container">
                        <div class="form-header">
                            <h2 class="form-title">
                                <i class="fas fa-building"></i>
                                بيانات البنغالو
                            </h2>
                        </div>

                        <div class="form-body">
                            <form id="bungalowForm">
                                <div class="form-grid">
                                    <!-- رقم البنغالو -->
                                    <div class="form-group">
                                        <label class="form-label required">رقم البنغالو</label>
                                        <div class="input-group">
                                            <input type="text" class="form-input with-icon" placeholder="مثال: B006" required>
                                            <i class="fas fa-hashtag input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- الموقع -->
                                    <div class="form-group">
                                        <label class="form-label required">الموقع</label>
                                        <div class="input-group">
                                            <input type="text" class="form-input with-icon" placeholder="مثال: المنطقة الشرقية" required>
                                            <i class="fas fa-map-marker-alt input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- السعة -->
                                    <div class="form-group">
                                        <label class="form-label required">السعة</label>
                                        <select class="form-select" required>
                                            <option value="">اختر السعة</option>
                                            <option value="2">2 أشخاص</option>
                                            <option value="4">4 أشخاص</option>
                                            <option value="6">6 أشخاص</option>
                                            <option value="8">8 أشخاص</option>
                                            <option value="10">10 أشخاص</option>
                                            <option value="12">12 شخص</option>
                                        </select>
                                    </div>

                                    <!-- نوع البنغالو -->
                                    <div class="form-group">
                                        <label class="form-label required">نوع البنغالو</label>
                                        <select class="form-select" required>
                                            <option value="">اختر النوع</option>
                                            <option value="عادي">عادي</option>
                                            <option value="مميز">مميز</option>
                                            <option value="فاخر">فاخر</option>
                                            <option value="VIP">VIP</option>
                                        </select>
                                    </div>

                                    <!-- عدد الغرف -->
                                    <div class="form-group">
                                        <label class="form-label">عدد الغرف</label>
                                        <div class="input-group">
                                            <input type="number" class="form-input with-icon" placeholder="مثال: 3" min="1" max="10">
                                            <i class="fas fa-bed input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- عدد الحمامات -->
                                    <div class="form-group">
                                        <label class="form-label">عدد الحمامات</label>
                                        <div class="input-group">
                                            <input type="number" class="form-input with-icon" placeholder="مثال: 2" min="1" max="5">
                                            <i class="fas fa-bath input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- المساحة -->
                                    <div class="form-group">
                                        <label class="form-label">المساحة (متر مربع)</label>
                                        <div class="input-group">
                                            <input type="number" class="form-input with-icon" placeholder="مثال: 120">
                                            <i class="fas fa-ruler-combined input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- الحالة -->
                                    <div class="form-group">
                                        <label class="form-label required">الحالة</label>
                                        <select class="form-select" required>
                                            <option value="">اختر الحالة</option>
                                            <option value="متاح">متاح</option>
                                            <option value="مشغول">مشغول</option>
                                            <option value="صيانة">تحت الصيانة</option>
                                            <option value="خارج الخدمة">خارج الخدمة</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- المرافق -->
                                <div class="form-group">
                                    <label class="form-label">المرافق المتوفرة</label>
                                    <textarea class="form-input form-textarea" rows="3" placeholder="مثال: مكيف هواء، تلفزيون، ثلاجة، مطبخ صغير..."></textarea>
                                </div>

                                <!-- الوصف -->
                                <div class="form-group">
                                    <label class="form-label">وصف إضافي</label>
                                    <textarea class="form-input form-textarea" rows="4" placeholder="أي معلومات إضافية عن البنغالو..."></textarea>
                                </div>

                                <!-- Form Actions -->
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i>
                                        حفظ البنغالو
                                    </button>
                                    <a href="/bungalows" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </main>
            </div>

            <script>
                // Sidebar Toggle
                function toggleSidebar() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.toggle('active');
                    overlay.classList.toggle('active');
                }}

                // Close sidebar when clicking overlay
                document.getElementById('sidebarOverlay').addEventListener('click', function() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                }});

                // Form validation and submission
                document.getElementById('bungalowForm').addEventListener('submit', function(e) {{
                    e.preventDefault();

                    // Get form data
                    const bungalowNumber = this.querySelector('input[type="text"]').value;
                    const location = this.querySelectorAll('input[type="text"]')[1].value;
                    const capacity = this.querySelector('select').value;
                    const type = this.querySelectorAll('select')[1].value;
                    const status = this.querySelectorAll('select')[2].value;

                    // Basic validation
                    if (!bungalowNumber || !location || !capacity || !type || !status) {{
                        alert('يرجى ملء جميع الحقول المطلوبة');
                        return;
                    }}

                    // Validate bungalow number format
                    if (!/^B\d{{3,4}}$/i.test(bungalowNumber)) {{
                        alert('يرجى إدخال رقم البنغالو بالتنسيق الصحيح (مثال: B006)');
                        return;
                    }}

                    // Simulate form submission
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;

                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                    submitBtn.disabled = true;

                    setTimeout(() => {{
                        alert('تم حفظ بيانات البنغالو بنجاح!');
                        window.location.href = '/bungalows';
                    }}, 2000);
                }});

                // Auto-format bungalow number
                document.querySelector('input[type="text"]').addEventListener('input', function() {{
                    let value = this.value.toUpperCase();
                    if (value && !value.startsWith('B')) {{
                        value = 'B' + value.replace(/[^0-9]/g, '');
                    }}
                    this.value = value;
                }});

                // Auto-calculate capacity based on rooms
                document.querySelector('input[type="number"]').addEventListener('input', function() {{
                    const rooms = parseInt(this.value);
                    if (rooms > 0) {{
                        const estimatedCapacity = rooms * 2;
                        const capacitySelect = document.querySelector('select');
                        const options = capacitySelect.options;

                        for (let i = 0; i < options.length; i++) {{
                            if (parseInt(options[i].value) >= estimatedCapacity) {{
                                capacitySelect.value = options[i].value;
                                break;
                            }}
                        }}
                    }}
                }});
            </script>
        </body>
        </html>
        '''

    # صفحة إدارة الأفراد
    @app.route('/personnel')
    def personnel():
        from flask import session, redirect, url_for
        if 'user_id' not in session:
            return redirect(url_for('login'))

        username = session.get('username', 'المستخدم')

        return f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>إدارة الأفراد - BANGHALAU</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
            <style>
                :root {{
                    --primary-color: #2563eb;
                    --secondary-color: #1e40af;
                    --accent-color: #3b82f6;
                    --success-color: #10b981;
                    --warning-color: #f59e0b;
                    --danger-color: #ef4444;
                    --dark-color: #1f2937;
                    --light-color: #f8fafc;
                    --sidebar-width: 280px;
                }}

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: #f1f5f9;
                    color: var(--dark-color);
                    line-height: 1.6;
                }}

                /* Sidebar Styles */
                .sidebar {{
                    position: fixed;
                    top: 0;
                    right: 0;
                    width: var(--sidebar-width);
                    height: 100vh;
                    background: #ffffff;
                    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
                    z-index: 1000;
                    transition: transform 0.3s ease;
                    overflow-y: auto;
                }}

                .sidebar-brand {{
                    padding: 2rem 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    text-align: center;
                }}

                .brand-logo {{
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 1rem;
                    color: white;
                    font-size: 1.5rem;
                    font-weight: bold;
                }}

                .brand-text h3 {{
                    font-size: 1.25rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    margin-bottom: 0.25rem;
                }}

                .brand-text p {{
                    font-size: 0.875rem;
                    color: #64748b;
                }}

                .sidebar-nav {{
                    padding: 1rem 0;
                }}

                .nav-section {{
                    margin-bottom: 2rem;
                }}

                .nav-section-title {{
                    padding: 0 1.5rem 0.5rem;
                    font-size: 0.75rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    color: #94a3b8;
                }}

                .nav-item {{
                    margin: 0.25rem 1rem;
                }}

                .nav-link {{
                    display: flex;
                    align-items: center;
                    padding: 0.75rem 1rem;
                    color: #64748b;
                    text-decoration: none;
                    border-radius: 12px;
                    transition: all 0.2s ease;
                    font-weight: 500;
                    position: relative;
                }}

                .nav-link:hover {{
                    background: #f1f5f9;
                    color: var(--primary-color);
                    transform: translateX(-4px);
                }}

                .nav-link.active {{
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    color: white;
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                }}

                .nav-link i {{
                    margin-left: 0.75rem;
                    width: 20px;
                    text-align: center;
                    font-size: 1.1rem;
                }}

                .nav-badge {{
                    margin-right: auto;
                    background: var(--danger-color);
                    color: white;
                    font-size: 0.75rem;
                    padding: 0.25rem 0.5rem;
                    border-radius: 10px;
                    font-weight: 600;
                    min-width: 20px;
                    text-align: center;
                }}

                /* Main Content */
                .main-wrapper {{
                    margin-right: var(--sidebar-width);
                    min-height: 100vh;
                    transition: margin-right 0.3s ease;
                }}

                .main-header {{
                    background: white;
                    padding: 1.5rem 2rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}

                .header-title {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .menu-toggle {{
                    display: none;
                    background: none;
                    border: none;
                    font-size: 1.25rem;
                    color: var(--dark-color);
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 8px;
                }}

                .menu-toggle:hover {{
                    background: #f1f5f9;
                }}

                .page-title {{
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: var(--dark-color);
                }}

                .header-actions {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .user-profile {{
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0.5rem 1rem;
                    background: #f8fafc;
                    border-radius: 12px;
                    cursor: pointer;
                    transition: background 0.2s ease;
                }}

                .user-profile:hover {{
                    background: #e2e8f0;
                }}

                .user-avatar {{
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                }}

                .user-info h4 {{
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: var(--dark-color);
                }}

                .user-info p {{
                    font-size: 0.75rem;
                    color: #64748b;
                }}

                /* Page Content */
                .page-content {{
                    padding: 2rem;
                }}

                .page-header {{
                    background: white;
                    padding: 1.5rem;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                }}

                .page-header-content {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }}

                .page-header h1 {{
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                .page-header p {{
                    color: #64748b;
                    margin-top: 0.5rem;
                }}

                .header-actions-group {{
                    display: flex;
                    gap: 0.75rem;
                    flex-wrap: wrap;
                }}

                .btn {{
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1rem;
                    border: none;
                    border-radius: 12px;
                    font-size: 0.875rem;
                    font-weight: 500;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }}

                .btn-primary {{
                    background: var(--primary-color);
                    color: white;
                }}

                .btn-primary:hover {{
                    background: var(--secondary-color);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                    color: white;
                    text-decoration: none;
                }}

                .btn-secondary {{
                    background: #f1f5f9;
                    color: var(--dark-color);
                    border: 1px solid #e2e8f0;
                }}

                .btn-secondary:hover {{
                    background: #e2e8f0;
                    color: var(--dark-color);
                    text-decoration: none;
                }}

                /* Data Table */
                .data-table-container {{
                    background: white;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }}

                .table-header {{
                    padding: 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }}

                .table-title {{
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: var(--dark-color);
                }}

                .table-filters {{
                    display: flex;
                    gap: 0.75rem;
                    align-items: center;
                }}

                .search-input {{
                    padding: 0.5rem 1rem;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    width: 250px;
                }}

                .search-input:focus {{
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                }}

                .filter-select {{
                    padding: 0.5rem 1rem;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    background: white;
                }}

                .data-table {{
                    width: 100%;
                    border-collapse: collapse;
                }}

                .data-table th,
                .data-table td {{
                    padding: 1rem;
                    text-align: right;
                    border-bottom: 1px solid #f1f5f9;
                }}

                .data-table th {{
                    background: #f8fafc;
                    font-weight: 600;
                    color: var(--dark-color);
                    font-size: 0.875rem;
                }}

                .data-table td {{
                    font-size: 0.875rem;
                    color: #64748b;
                }}

                .data-table tbody tr:hover {{
                    background: #f8fafc;
                }}

                /* Status Badges */
                .status-badge {{
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    border-radius: 20px;
                    font-size: 0.75rem;
                    font-weight: 600;
                }}

                .status-active {{
                    background: #dcfce7;
                    color: #166534;
                }}

                .status-inactive {{
                    background: #fee2e2;
                    color: #991b1b;
                }}

                .status-vacation {{
                    background: #fef3c7;
                    color: #92400e;
                }}

                /* Rank Badges */
                .rank-badge {{
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    border-radius: 20px;
                    font-size: 0.75rem;
                    font-weight: 600;
                    background: #e0e7ff;
                    color: #3730a3;
                }}

                /* Action Buttons */
                .action-buttons {{
                    display: flex;
                    gap: 0.5rem;
                }}

                .btn-sm {{
                    padding: 0.5rem 0.75rem;
                    font-size: 0.75rem;
                }}

                .btn-edit {{
                    background: #f59e0b;
                    color: white;
                }}

                .btn-edit:hover {{
                    background: #d97706;
                    color: white;
                    text-decoration: none;
                }}

                .btn-delete {{
                    background: var(--danger-color);
                    color: white;
                }}

                .btn-delete:hover {{
                    background: #dc2626;
                    color: white;
                    text-decoration: none;
                }}

                .btn-view {{
                    background: var(--primary-color);
                    color: white;
                }}

                .btn-view:hover {{
                    background: var(--secondary-color);
                    color: white;
                    text-decoration: none;
                }}

                /* Mobile Responsive */
                @media (max-width: 768px) {{
                    .sidebar {{
                        transform: translateX(100%);
                    }}

                    .sidebar.active {{
                        transform: translateX(0);
                    }}

                    .main-wrapper {{
                        margin-right: 0;
                    }}

                    .menu-toggle {{
                        display: block;
                    }}

                    .main-header {{
                        padding: 1rem;
                    }}

                    .page-content {{
                        padding: 1rem;
                    }}

                    .page-header-content {{
                        flex-direction: column;
                        align-items: stretch;
                    }}

                    .header-actions-group {{
                        justify-content: stretch;
                    }}

                    .search-input {{
                        width: 100%;
                    }}

                    .table-header {{
                        flex-direction: column;
                        align-items: stretch;
                    }}

                    .table-filters {{
                        flex-direction: column;
                    }}

                    .data-table {{
                        font-size: 0.75rem;
                    }}

                    .data-table th,
                    .data-table td {{
                        padding: 0.5rem;
                    }}

                    .action-buttons {{
                        flex-direction: column;
                    }}
                }}

                .sidebar-overlay {{
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 999;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }}

                .sidebar-overlay.active {{
                    opacity: 1;
                    visibility: visible;
                }}
            </style>
        </head>
        <body>
            <!-- Sidebar Overlay -->
            <div class="sidebar-overlay" id="sidebarOverlay"></div>

            <!-- Sidebar -->
            <div class="sidebar" id="sidebar">
                <div class="sidebar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="brand-text">
                        <h3>BANGHALAU</h3>
                        <p>نظام إدارة البنغالوهات</p>
                    </div>
                </div>

                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <div class="nav-section-title">الرئيسية</div>
                        <div class="nav-item">
                            <a href="/dashboard" class="nav-link">
                                <i class="fas fa-chart-pie"></i>
                                <span>لوحة التحكم</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-title">الإدارة</div>
                        <div class="nav-item">
                            <a href="/bungalows" class="nav-link">
                                <i class="fas fa-building"></i>
                                <span>البنغالوهات</span>
                                <span class="nav-badge">25</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/personnel" class="nav-link active">
                                <i class="fas fa-users"></i>
                                <span>الأفراد</span>
                                <span class="nav-badge">150</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/distributions" class="nav-link">
                                <i class="fas fa-exchange-alt"></i>
                                <span>التوزيعات</span>
                                <span class="nav-badge">45</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-item">
                            <a href="/logout" class="nav-link" style="color: var(--danger-color);">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="main-wrapper">
                <!-- Header -->
                <header class="main-header">
                    <div class="header-title">
                        <button class="menu-toggle" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="page-title">إدارة الأفراد</h1>
                    </div>

                    <div class="header-actions">
                        <div class="user-profile">
                            <div class="user-avatar">
                                {username[0].upper()}
                            </div>
                            <div class="user-info">
                                <h4>{username}</h4>
                                <p>مدير النظام</p>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Page Content -->
                <main class="page-content">
                    <!-- Page Header -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <div>
                                <h1>
                                    <i class="fas fa-users"></i>
                                    إدارة الأفراد العسكريين
                                </h1>
                                <p>إدارة وتنظيم جميع الأفراد العسكريين في النظام</p>
                            </div>
                            <div class="header-actions-group">
                                <a href="/personnel/add" class="btn btn-primary">
                                    <i class="fas fa-user-plus"></i>
                                    إضافة فرد جديد
                                </a>
                                <a href="/dashboard" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right"></i>
                                    العودة للوحة التحكم
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <div class="data-table-container">
                        <div class="table-header">
                            <h2 class="table-title">قائمة الأفراد العسكريين</h2>
                            <div class="table-filters">
                                <input type="text" class="search-input" placeholder="البحث في الأفراد...">
                                <select class="filter-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="vacation">في إجازة</option>
                                </select>
                                <select class="filter-select">
                                    <option value="">جميع الرتب</option>
                                    <option value="عقيد">عقيد</option>
                                    <option value="مقدم">مقدم</option>
                                    <option value="رائد">رائد</option>
                                    <option value="نقيب">نقيب</option>
                                    <option value="ملازم">ملازم</option>
                                </select>
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>الرقم العسكري</th>
                                    <th>الاسم الكامل</th>
                                    <th>الرتبة</th>
                                    <th>الوحدة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>12345</strong></td>
                                    <td>أحمد محمد علي</td>
                                    <td>
                                        <span class="rank-badge">
                                            <i class="fas fa-star"></i>
                                            نقيب
                                        </span>
                                    </td>
                                    <td>الوحدة الأولى</td>
                                    <td>
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle"></i>
                                            نشط
                                        </span>
                                    </td>
                                    <td>2024/01/15</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="#" class="btn btn-view btn-sm">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <a href="/personnel/edit/12345" class="btn btn-edit btn-sm">
                                                <i class="fas fa-edit"></i>
                                                تعديل
                                            </a>
                                            <a href="#" class="btn btn-delete btn-sm">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>12346</strong></td>
                                    <td>محمد عبدالله سالم</td>
                                    <td>
                                        <span class="rank-badge">
                                            <i class="fas fa-star"></i>
                                            رائد
                                        </span>
                                    </td>
                                    <td>الوحدة الثانية</td>
                                    <td>
                                        <span class="status-badge status-vacation">
                                            <i class="fas fa-calendar"></i>
                                            في إجازة
                                        </span>
                                    </td>
                                    <td>2024/01/20</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="#" class="btn btn-view btn-sm">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <a href="/personnel/edit/12346" class="btn btn-edit btn-sm">
                                                <i class="fas fa-edit"></i>
                                                تعديل
                                            </a>
                                            <a href="#" class="btn btn-delete btn-sm">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>12347</strong></td>
                                    <td>خالد أحمد محمد</td>
                                    <td>
                                        <span class="rank-badge">
                                            <i class="fas fa-star"></i>
                                            مقدم
                                        </span>
                                    </td>
                                    <td>الوحدة الثالثة</td>
                                    <td>
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle"></i>
                                            نشط
                                        </span>
                                    </td>
                                    <td>2024/02/01</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="#" class="btn btn-view btn-sm">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <a href="/personnel/edit/12347" class="btn btn-edit btn-sm">
                                                <i class="fas fa-edit"></i>
                                                تعديل
                                            </a>
                                            <a href="#" class="btn btn-delete btn-sm">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>12348</strong></td>
                                    <td>سالم محمد علي</td>
                                    <td>
                                        <span class="rank-badge">
                                            <i class="fas fa-star"></i>
                                            ملازم
                                        </span>
                                    </td>
                                    <td>الوحدة الأولى</td>
                                    <td>
                                        <span class="status-badge status-inactive">
                                            <i class="fas fa-times-circle"></i>
                                            غير نشط
                                        </span>
                                    </td>
                                    <td>2024/02/10</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="#" class="btn btn-view btn-sm">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <a href="/personnel/edit/12348" class="btn btn-edit btn-sm">
                                                <i class="fas fa-edit"></i>
                                                تعديل
                                            </a>
                                            <a href="#" class="btn btn-delete btn-sm">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>12349</strong></td>
                                    <td>عبدالرحمن سعد أحمد</td>
                                    <td>
                                        <span class="rank-badge">
                                            <i class="fas fa-star"></i>
                                            عقيد
                                        </span>
                                    </td>
                                    <td>الوحدة الرابعة</td>
                                    <td>
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle"></i>
                                            نشط
                                        </span>
                                    </td>
                                    <td>2024/02/15</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="#" class="btn btn-view btn-sm">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <a href="/personnel/edit/12349" class="btn btn-edit btn-sm">
                                                <i class="fas fa-edit"></i>
                                                تعديل
                                            </a>
                                            <a href="#" class="btn btn-delete btn-sm">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </main>
            </div>

            <script>
                // Sidebar Toggle
                function toggleSidebar() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.toggle('active');
                    overlay.classList.toggle('active');
                }}

                // Close sidebar when clicking overlay
                document.getElementById('sidebarOverlay').addEventListener('click', function() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                }});

                // Search functionality
                document.querySelector('.search-input').addEventListener('input', function() {{
                    const searchTerm = this.value.toLowerCase();
                    const rows = document.querySelectorAll('.data-table tbody tr');

                    rows.forEach(row => {{
                        const text = row.textContent.toLowerCase();
                        row.style.display = text.includes(searchTerm) ? '' : 'none';
                    }});
                }});

                // Filter functionality
                document.querySelectorAll('.filter-select').forEach(select => {{
                    select.addEventListener('change', function() {{
                        filterTable();
                    }});
                }});

                function filterTable() {{
                    const statusFilter = document.querySelectorAll('.filter-select')[0].value;
                    const rankFilter = document.querySelectorAll('.filter-select')[1].value;
                    const rows = document.querySelectorAll('.data-table tbody tr');

                    rows.forEach(row => {{
                        let showRow = true;

                        // Status filter
                        if (statusFilter) {{
                            const statusBadge = row.querySelector('.status-badge');
                            const hasStatus = statusBadge.classList.contains('status-' + statusFilter);
                            if (!hasStatus) showRow = false;
                        }}

                        // Rank filter
                        if (rankFilter) {{
                            const rankText = row.querySelector('.rank-badge').textContent.trim();
                            if (!rankText.includes(rankFilter)) showRow = false;
                        }}

                        row.style.display = showRow ? '' : 'none';
                    }});
                }}

                // Delete confirmation
                document.querySelectorAll('.btn-delete').forEach(btn => {{
                    btn.addEventListener('click', function(e) {{
                        e.preventDefault();
                        if (confirm('هل أنت متأكد من حذف هذا الفرد؟')) {{
                            // Handle delete action here
                            alert('تم حذف الفرد بنجاح');
                        }}
                    }});
                }});
            </script>
        </body>
        </html>
        '''

    # صفحة إضافة فرد جديد
    @app.route('/personnel/add')
    def add_personnel():
        from flask import session, redirect, url_for
        if 'user_id' not in session:
            return redirect(url_for('login'))

        username = session.get('username', 'المستخدم')

        return f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>إضافة فرد عسكري جديد - BANGHALAU</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
            <style>
                :root {{
                    --primary-color: #2563eb;
                    --secondary-color: #1e40af;
                    --accent-color: #3b82f6;
                    --success-color: #10b981;
                    --warning-color: #f59e0b;
                    --danger-color: #ef4444;
                    --dark-color: #1f2937;
                    --light-color: #f8fafc;
                    --sidebar-width: 280px;
                }}

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: #f1f5f9;
                    color: var(--dark-color);
                    line-height: 1.6;
                }}

                /* Sidebar Styles */
                .sidebar {{
                    position: fixed;
                    top: 0;
                    right: 0;
                    width: var(--sidebar-width);
                    height: 100vh;
                    background: #ffffff;
                    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
                    z-index: 1000;
                    transition: transform 0.3s ease;
                    overflow-y: auto;
                }}

                .sidebar-brand {{
                    padding: 2rem 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    text-align: center;
                }}

                .brand-logo {{
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 1rem;
                    color: white;
                    font-size: 1.5rem;
                    font-weight: bold;
                }}

                .brand-text h3 {{
                    font-size: 1.25rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    margin-bottom: 0.25rem;
                }}

                .brand-text p {{
                    font-size: 0.875rem;
                    color: #64748b;
                }}

                .sidebar-nav {{
                    padding: 1rem 0;
                }}

                .nav-section {{
                    margin-bottom: 2rem;
                }}

                .nav-section-title {{
                    padding: 0 1.5rem 0.5rem;
                    font-size: 0.75rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    color: #94a3b8;
                }}

                .nav-item {{
                    margin: 0.25rem 1rem;
                }}

                .nav-link {{
                    display: flex;
                    align-items: center;
                    padding: 0.75rem 1rem;
                    color: #64748b;
                    text-decoration: none;
                    border-radius: 12px;
                    transition: all 0.2s ease;
                    font-weight: 500;
                    position: relative;
                }}

                .nav-link:hover {{
                    background: #f1f5f9;
                    color: var(--primary-color);
                    transform: translateX(-4px);
                }}

                .nav-link.active {{
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    color: white;
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                }}

                .nav-link i {{
                    margin-left: 0.75rem;
                    width: 20px;
                    text-align: center;
                    font-size: 1.1rem;
                }}

                .nav-badge {{
                    margin-right: auto;
                    background: var(--danger-color);
                    color: white;
                    font-size: 0.75rem;
                    padding: 0.25rem 0.5rem;
                    border-radius: 10px;
                    font-weight: 600;
                    min-width: 20px;
                    text-align: center;
                }}

                /* Main Content */
                .main-wrapper {{
                    margin-right: var(--sidebar-width);
                    min-height: 100vh;
                    transition: margin-right 0.3s ease;
                }}

                .main-header {{
                    background: white;
                    padding: 1.5rem 2rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}

                .header-title {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .menu-toggle {{
                    display: none;
                    background: none;
                    border: none;
                    font-size: 1.25rem;
                    color: var(--dark-color);
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 8px;
                }}

                .menu-toggle:hover {{
                    background: #f1f5f9;
                }}

                .page-title {{
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: var(--dark-color);
                }}

                .header-actions {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .user-profile {{
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0.5rem 1rem;
                    background: #f8fafc;
                    border-radius: 12px;
                    cursor: pointer;
                    transition: background 0.2s ease;
                }}

                .user-profile:hover {{
                    background: #e2e8f0;
                }}

                .user-avatar {{
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                }}

                .user-info h4 {{
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: var(--dark-color);
                }}

                .user-info p {{
                    font-size: 0.75rem;
                    color: #64748b;
                }}

                /* Page Content */
                .page-content {{
                    padding: 2rem;
                }}

                .page-header {{
                    background: white;
                    padding: 1.5rem;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                }}

                .page-header-content {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }}

                .page-header h1 {{
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                .page-header p {{
                    color: #64748b;
                    margin-top: 0.5rem;
                }}

                .header-actions-group {{
                    display: flex;
                    gap: 0.75rem;
                    flex-wrap: wrap;
                }}

                .btn {{
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1rem;
                    border: none;
                    border-radius: 12px;
                    font-size: 0.875rem;
                    font-weight: 500;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }}

                .btn-primary {{
                    background: var(--primary-color);
                    color: white;
                }}

                .btn-primary:hover {{
                    background: var(--secondary-color);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                    color: white;
                    text-decoration: none;
                }}

                .btn-secondary {{
                    background: #f1f5f9;
                    color: var(--dark-color);
                    border: 1px solid #e2e8f0;
                }}

                .btn-secondary:hover {{
                    background: #e2e8f0;
                    color: var(--dark-color);
                    text-decoration: none;
                }}

                .btn-success {{
                    background: var(--success-color);
                    color: white;
                }}

                .btn-success:hover {{
                    background: #059669;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
                    color: white;
                    text-decoration: none;
                }}

                /* Form Styles */
                .form-container {{
                    background: white;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }}

                .form-header {{
                    padding: 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    background: #f8fafc;
                }}

                .form-title {{
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: var(--dark-color);
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                .form-body {{
                    padding: 2rem;
                }}

                .form-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 1.5rem;
                    margin-bottom: 2rem;
                }}

                .form-group {{
                    margin-bottom: 1.5rem;
                }}

                .form-label {{
                    display: block;
                    margin-bottom: 0.5rem;
                    font-weight: 600;
                    color: var(--dark-color);
                    font-size: 0.875rem;
                }}

                .form-label.required::after {{
                    content: ' *';
                    color: var(--danger-color);
                }}

                .form-input {{
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    transition: all 0.2s ease;
                    background: white;
                }}

                .form-input:focus {{
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                }}

                .form-input:invalid {{
                    border-color: var(--danger-color);
                }}

                .form-textarea {{
                    resize: vertical;
                    min-height: 100px;
                }}

                .form-select {{
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    background: white;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }}

                .form-select:focus {{
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                }}

                .form-actions {{
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                }}

                .input-group {{
                    position: relative;
                }}

                .input-icon {{
                    position: absolute;
                    right: 1rem;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #64748b;
                    pointer-events: none;
                }}

                .form-input.with-icon {{
                    padding-right: 3rem;
                }}

                /* Mobile Responsive */
                @media (max-width: 768px) {{
                    .sidebar {{
                        transform: translateX(100%);
                    }}

                    .sidebar.active {{
                        transform: translateX(0);
                    }}

                    .main-wrapper {{
                        margin-right: 0;
                    }}

                    .menu-toggle {{
                        display: block;
                    }}

                    .main-header {{
                        padding: 1rem;
                    }}

                    .page-content {{
                        padding: 1rem;
                    }}

                    .page-header-content {{
                        flex-direction: column;
                        align-items: stretch;
                    }}

                    .header-actions-group {{
                        justify-content: stretch;
                    }}

                    .form-grid {{
                        grid-template-columns: 1fr;
                    }}

                    .form-actions {{
                        flex-direction: column;
                    }}
                }}

                .sidebar-overlay {{
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 999;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }}

                .sidebar-overlay.active {{
                    opacity: 1;
                    visibility: visible;
                }}
            </style>
        </head>
        <body>
            <!-- Sidebar Overlay -->
            <div class="sidebar-overlay" id="sidebarOverlay"></div>

            <!-- Sidebar -->
            <div class="sidebar" id="sidebar">
                <div class="sidebar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="brand-text">
                        <h3>BANGHALAU</h3>
                        <p>نظام إدارة البنغالوهات</p>
                    </div>
                </div>

                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <div class="nav-section-title">الرئيسية</div>
                        <div class="nav-item">
                            <a href="/dashboard" class="nav-link">
                                <i class="fas fa-chart-pie"></i>
                                <span>لوحة التحكم</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-title">الإدارة</div>
                        <div class="nav-item">
                            <a href="/bungalows" class="nav-link">
                                <i class="fas fa-building"></i>
                                <span>البنغالوهات</span>
                                <span class="nav-badge">25</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/personnel" class="nav-link active">
                                <i class="fas fa-users"></i>
                                <span>الأفراد</span>
                                <span class="nav-badge">150</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/distributions" class="nav-link">
                                <i class="fas fa-exchange-alt"></i>
                                <span>التوزيعات</span>
                                <span class="nav-badge">45</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-item">
                            <a href="/logout" class="nav-link" style="color: var(--danger-color);">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="main-wrapper">
                <!-- Header -->
                <header class="main-header">
                    <div class="header-title">
                        <button class="menu-toggle" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="page-title">إضافة فرد عسكري جديد</h1>
                    </div>

                    <div class="header-actions">
                        <div class="user-profile">
                            <div class="user-avatar">
                                {username[0].upper()}
                            </div>
                            <div class="user-info">
                                <h4>{username}</h4>
                                <p>مدير النظام</p>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Page Content -->
                <main class="page-content">
                    <!-- Page Header -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <div>
                                <h1>
                                    <i class="fas fa-user-plus"></i>
                                    إضافة فرد عسكري جديد
                                </h1>
                                <p>إضافة فرد عسكري جديد إلى النظام</p>
                            </div>
                            <div class="header-actions-group">
                                <a href="/personnel" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right"></i>
                                    العودة لقائمة الأفراد
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Form Container -->
                    <div class="form-container">
                        <div class="form-header">
                            <h2 class="form-title">
                                <i class="fas fa-user-plus"></i>
                                بيانات الفرد العسكري
                            </h2>
                        </div>

                        <div class="form-body">
                            <form id="personnelForm">
                                <div class="form-grid">
                                    <!-- الرقم العسكري -->
                                    <div class="form-group">
                                        <label class="form-label required">الرقم العسكري</label>
                                        <div class="input-group">
                                            <input type="text" class="form-input with-icon" placeholder="مثال: 12350" required>
                                            <i class="fas fa-id-card input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- الاسم الكامل -->
                                    <div class="form-group">
                                        <label class="form-label required">الاسم الكامل</label>
                                        <div class="input-group">
                                            <input type="text" class="form-input with-icon" placeholder="مثال: أحمد محمد علي" required>
                                            <i class="fas fa-user input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- الرتبة -->
                                    <div class="form-group">
                                        <label class="form-label required">الرتبة العسكرية</label>
                                        <select class="form-select" required>
                                            <option value="">اختر الرتبة</option>
                                            <option value="عقيد">عقيد</option>
                                            <option value="مقدم">مقدم</option>
                                            <option value="رائد">رائد</option>
                                            <option value="نقيب">نقيب</option>
                                            <option value="ملازم أول">ملازم أول</option>
                                            <option value="ملازم">ملازم</option>
                                            <option value="رقيب أول">رقيب أول</option>
                                            <option value="رقيب">رقيب</option>
                                            <option value="عريف">عريف</option>
                                            <option value="جندي أول">جندي أول</option>
                                            <option value="جندي">جندي</option>
                                        </select>
                                    </div>

                                    <!-- الوحدة -->
                                    <div class="form-group">
                                        <label class="form-label required">الوحدة العسكرية</label>
                                        <select class="form-select" required>
                                            <option value="">اختر الوحدة</option>
                                            <option value="الوحدة الأولى">الوحدة الأولى</option>
                                            <option value="الوحدة الثانية">الوحدة الثانية</option>
                                            <option value="الوحدة الثالثة">الوحدة الثالثة</option>
                                            <option value="الوحدة الرابعة">الوحدة الرابعة</option>
                                            <option value="الوحدة الخامسة">الوحدة الخامسة</option>
                                            <option value="وحدة الدعم">وحدة الدعم</option>
                                            <option value="وحدة الاستطلاع">وحدة الاستطلاع</option>
                                        </select>
                                    </div>

                                    <!-- رقم الهاتف -->
                                    <div class="form-group">
                                        <label class="form-label">رقم الهاتف</label>
                                        <div class="input-group">
                                            <input type="tel" class="form-input with-icon" placeholder="مثال: 0501234567">
                                            <i class="fas fa-phone input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- البريد الإلكتروني -->
                                    <div class="form-group">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <div class="input-group">
                                            <input type="email" class="form-input with-icon" placeholder="مثال: <EMAIL>">
                                            <i class="fas fa-envelope input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- تاريخ الميلاد -->
                                    <div class="form-group">
                                        <label class="form-label">تاريخ الميلاد</label>
                                        <div class="input-group">
                                            <input type="date" class="form-input with-icon">
                                            <i class="fas fa-calendar input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- الحالة الاجتماعية -->
                                    <div class="form-group">
                                        <label class="form-label">الحالة الاجتماعية</label>
                                        <select class="form-select">
                                            <option value="">اختر الحالة</option>
                                            <option value="أعزب">أعزب</option>
                                            <option value="متزوج">متزوج</option>
                                            <option value="مطلق">مطلق</option>
                                            <option value="أرمل">أرمل</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- العنوان -->
                                <div class="form-group">
                                    <label class="form-label">العنوان</label>
                                    <div class="input-group">
                                        <input type="text" class="form-input with-icon" placeholder="العنوان الكامل">
                                        <i class="fas fa-map-marker-alt input-icon"></i>
                                    </div>
                                </div>

                                <!-- ملاحظات -->
                                <div class="form-group">
                                    <label class="form-label">ملاحظات إضافية</label>
                                    <textarea class="form-input form-textarea" rows="4" placeholder="أي ملاحظات أو معلومات إضافية..."></textarea>
                                </div>

                                <!-- Form Actions -->
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i>
                                        حفظ الفرد
                                    </button>
                                    <a href="/personnel" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </main>
            </div>

            <script>
                // Sidebar Toggle
                function toggleSidebar() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.toggle('active');
                    overlay.classList.toggle('active');
                }}

                // Close sidebar when clicking overlay
                document.getElementById('sidebarOverlay').addEventListener('click', function() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                }});

                // Form validation and submission
                document.getElementById('personnelForm').addEventListener('submit', function(e) {{
                    e.preventDefault();

                    // Get form data
                    const formData = new FormData(this);
                    const militaryId = this.querySelector('input[type="text"]').value;
                    const fullName = this.querySelectorAll('input[type="text"]')[1].value;
                    const rank = this.querySelector('select').value;
                    const unit = this.querySelectorAll('select')[1].value;

                    // Basic validation
                    if (!militaryId || !fullName || !rank || !unit) {{
                        alert('يرجى ملء جميع الحقول المطلوبة');
                        return;
                    }}

                    // Simulate form submission
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;

                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                    submitBtn.disabled = true;

                    setTimeout(() => {{
                        alert('تم حفظ بيانات الفرد بنجاح!');
                        window.location.href = '/personnel';
                    }}, 2000);
                }});

                // Auto-format phone number
                document.querySelector('input[type="tel"]').addEventListener('input', function() {{
                    let value = this.value.replace(/\D/g, '');
                    if (value.length > 10) {{
                        value = value.substring(0, 10);
                    }}
                    this.value = value;
                }});

                // Auto-format military ID
                document.querySelector('input[type="text"]').addEventListener('input', function() {{
                    let value = this.value.replace(/\D/g, '');
                    if (value.length > 8) {{
                        value = value.substring(0, 8);
                    }}
                    this.value = value;
                }});
            </script>
        </body>
        </html>
        '''

    # صفحة تعديل فرد عسكري
    @app.route('/personnel/edit/<personnel_id>')
    def edit_personnel(personnel_id):
        from flask import session, redirect, url_for
        if 'user_id' not in session:
            return redirect(url_for('login'))

        username = session.get('username', 'المستخدم')

        # بيانات تجريبية للفرد (في التطبيق الحقيقي ستأتي من قاعدة البيانات)
        personnel_data = {
            '12345': {
                'military_id': '12345',
                'full_name': 'أحمد محمد علي',
                'rank': 'نقيب',
                'unit': 'الوحدة الأولى',
                'phone': '0501234567',
                'email': '<EMAIL>',
                'birth_date': '1990-05-15',
                'marital_status': 'متزوج',
                'address': 'الرياض، حي النخيل',
                'notes': 'فرد متميز في الأداء'
            },
            '12346': {
                'military_id': '12346',
                'full_name': 'محمد عبدالله سالم',
                'rank': 'رائد',
                'unit': 'الوحدة الثانية',
                'phone': '0507654321',
                'email': '<EMAIL>',
                'birth_date': '1988-03-20',
                'marital_status': 'أعزب',
                'address': 'جدة، حي الصفا',
                'notes': 'في إجازة حتى نهاية الشهر'
            }
        }

        # الحصول على بيانات الفرد أو استخدام بيانات افتراضية
        person = personnel_data.get(personnel_id, {
            'military_id': personnel_id,
            'full_name': '',
            'rank': '',
            'unit': '',
            'phone': '',
            'email': '',
            'birth_date': '',
            'marital_status': '',
            'address': '',
            'notes': ''
        })

        return f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تعديل بيانات الفرد العسكري - BANGHALAU</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
            <style>
                :root {{
                    --primary-color: #2563eb;
                    --secondary-color: #1e40af;
                    --accent-color: #3b82f6;
                    --success-color: #10b981;
                    --warning-color: #f59e0b;
                    --danger-color: #ef4444;
                    --dark-color: #1f2937;
                    --light-color: #f8fafc;
                    --sidebar-width: 280px;
                }}

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: #f1f5f9;
                    color: var(--dark-color);
                    line-height: 1.6;
                }}

                /* Sidebar Styles */
                .sidebar {{
                    position: fixed;
                    top: 0;
                    right: 0;
                    width: var(--sidebar-width);
                    height: 100vh;
                    background: #ffffff;
                    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
                    z-index: 1000;
                    transition: transform 0.3s ease;
                    overflow-y: auto;
                }}

                .sidebar-brand {{
                    padding: 2rem 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    text-align: center;
                }}

                .brand-logo {{
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 1rem;
                    color: white;
                    font-size: 1.5rem;
                    font-weight: bold;
                }}

                .brand-text h3 {{
                    font-size: 1.25rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    margin-bottom: 0.25rem;
                }}

                .brand-text p {{
                    font-size: 0.875rem;
                    color: #64748b;
                }}

                .sidebar-nav {{
                    padding: 1rem 0;
                }}

                .nav-section {{
                    margin-bottom: 2rem;
                }}

                .nav-section-title {{
                    padding: 0 1.5rem 0.5rem;
                    font-size: 0.75rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    color: #94a3b8;
                }}

                .nav-item {{
                    margin: 0.25rem 1rem;
                }}

                .nav-link {{
                    display: flex;
                    align-items: center;
                    padding: 0.75rem 1rem;
                    color: #64748b;
                    text-decoration: none;
                    border-radius: 12px;
                    transition: all 0.2s ease;
                    font-weight: 500;
                    position: relative;
                }}

                .nav-link:hover {{
                    background: #f1f5f9;
                    color: var(--primary-color);
                    transform: translateX(-4px);
                }}

                .nav-link.active {{
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    color: white;
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                }}

                .nav-link i {{
                    margin-left: 0.75rem;
                    width: 20px;
                    text-align: center;
                    font-size: 1.1rem;
                }}

                .nav-badge {{
                    margin-right: auto;
                    background: var(--danger-color);
                    color: white;
                    font-size: 0.75rem;
                    padding: 0.25rem 0.5rem;
                    border-radius: 10px;
                    font-weight: 600;
                    min-width: 20px;
                    text-align: center;
                }}

                /* Main Content */
                .main-wrapper {{
                    margin-right: var(--sidebar-width);
                    min-height: 100vh;
                    transition: margin-right 0.3s ease;
                }}

                .main-header {{
                    background: white;
                    padding: 1.5rem 2rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}

                .header-title {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .menu-toggle {{
                    display: none;
                    background: none;
                    border: none;
                    font-size: 1.25rem;
                    color: var(--dark-color);
                    cursor: pointer;
                    padding: 0.5rem;
                    border-radius: 8px;
                }}

                .menu-toggle:hover {{
                    background: #f1f5f9;
                }}

                .page-title {{
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: var(--dark-color);
                }}

                .header-actions {{
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }}

                .user-profile {{
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0.5rem 1rem;
                    background: #f8fafc;
                    border-radius: 12px;
                    cursor: pointer;
                    transition: background 0.2s ease;
                }}

                .user-profile:hover {{
                    background: #e2e8f0;
                }}

                .user-avatar {{
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                }}

                .user-info h4 {{
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: var(--dark-color);
                }}

                .user-info p {{
                    font-size: 0.75rem;
                    color: #64748b;
                }}

                /* Page Content */
                .page-content {{
                    padding: 2rem;
                }}

                .page-header {{
                    background: white;
                    padding: 1.5rem;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                }}

                .page-header-content {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }}

                .page-header h1 {{
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: var(--dark-color);
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                .page-header p {{
                    color: #64748b;
                    margin-top: 0.5rem;
                }}

                .header-actions-group {{
                    display: flex;
                    gap: 0.75rem;
                    flex-wrap: wrap;
                }}

                .btn {{
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1rem;
                    border: none;
                    border-radius: 12px;
                    font-size: 0.875rem;
                    font-weight: 500;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }}

                .btn-primary {{
                    background: var(--primary-color);
                    color: white;
                }}

                .btn-primary:hover {{
                    background: var(--secondary-color);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                    color: white;
                    text-decoration: none;
                }}

                .btn-secondary {{
                    background: #f1f5f9;
                    color: var(--dark-color);
                    border: 1px solid #e2e8f0;
                }}

                .btn-secondary:hover {{
                    background: #e2e8f0;
                    color: var(--dark-color);
                    text-decoration: none;
                }}

                .btn-warning {{
                    background: var(--warning-color);
                    color: white;
                }}

                .btn-warning:hover {{
                    background: #d97706;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
                    color: white;
                    text-decoration: none;
                }}

                .btn-danger {{
                    background: var(--danger-color);
                    color: white;
                }}

                .btn-danger:hover {{
                    background: #dc2626;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
                    color: white;
                    text-decoration: none;
                }}

                /* Form Styles */
                .form-container {{
                    background: white;
                    border-radius: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }}

                .form-header {{
                    padding: 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                    background: #f8fafc;
                }}

                .form-title {{
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: var(--dark-color);
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                .form-body {{
                    padding: 2rem;
                }}

                .form-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 1.5rem;
                    margin-bottom: 2rem;
                }}

                .form-group {{
                    margin-bottom: 1.5rem;
                }}

                .form-label {{
                    display: block;
                    margin-bottom: 0.5rem;
                    font-weight: 600;
                    color: var(--dark-color);
                    font-size: 0.875rem;
                }}

                .form-label.required::after {{
                    content: ' *';
                    color: var(--danger-color);
                }}

                .form-input {{
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    transition: all 0.2s ease;
                    background: white;
                }}

                .form-input:focus {{
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                }}

                .form-input:invalid {{
                    border-color: var(--danger-color);
                }}

                .form-textarea {{
                    resize: vertical;
                    min-height: 100px;
                }}

                .form-select {{
                    width: 100%;
                    padding: 0.75rem 1rem;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    background: white;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }}

                .form-select:focus {{
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                }}

                .form-actions {{
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e2e8f0;
                }}

                .input-group {{
                    position: relative;
                }}

                .input-icon {{
                    position: absolute;
                    right: 1rem;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #64748b;
                    pointer-events: none;
                }}

                .form-input.with-icon {{
                    padding-right: 3rem;
                }}

                /* Info Card */
                .info-card {{
                    background: linear-gradient(135deg, #fef3c7, #fbbf24);
                    border: 1px solid #f59e0b;
                    border-radius: 12px;
                    padding: 1rem;
                    margin-bottom: 2rem;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }}

                .info-card-icon {{
                    color: #92400e;
                    font-size: 1.25rem;
                }}

                .info-card-content {{
                    color: #92400e;
                }}

                .info-card-content h3 {{
                    font-size: 0.875rem;
                    font-weight: 600;
                    margin-bottom: 0.25rem;
                }}

                .info-card-content p {{
                    font-size: 0.75rem;
                }}

                /* Mobile Responsive */
                @media (max-width: 768px) {{
                    .sidebar {{
                        transform: translateX(100%);
                    }}

                    .sidebar.active {{
                        transform: translateX(0);
                    }}

                    .main-wrapper {{
                        margin-right: 0;
                    }}

                    .menu-toggle {{
                        display: block;
                    }}

                    .main-header {{
                        padding: 1rem;
                    }}

                    .page-content {{
                        padding: 1rem;
                    }}

                    .page-header-content {{
                        flex-direction: column;
                        align-items: stretch;
                    }}

                    .header-actions-group {{
                        justify-content: stretch;
                    }}

                    .form-grid {{
                        grid-template-columns: 1fr;
                    }}

                    .form-actions {{
                        flex-direction: column;
                    }}
                }}

                .sidebar-overlay {{
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 999;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }}

                .sidebar-overlay.active {{
                    opacity: 1;
                    visibility: visible;
                }}
            </style>
        </head>
        <body>
            <!-- Sidebar Overlay -->
            <div class="sidebar-overlay" id="sidebarOverlay"></div>

            <!-- Sidebar -->
            <div class="sidebar" id="sidebar">
                <div class="sidebar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="brand-text">
                        <h3>BANGHALAU</h3>
                        <p>نظام إدارة البنغالوهات</p>
                    </div>
                </div>

                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <div class="nav-section-title">الرئيسية</div>
                        <div class="nav-item">
                            <a href="/dashboard" class="nav-link">
                                <i class="fas fa-chart-pie"></i>
                                <span>لوحة التحكم</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-title">الإدارة</div>
                        <div class="nav-item">
                            <a href="/bungalows" class="nav-link">
                                <i class="fas fa-building"></i>
                                <span>البنغالوهات</span>
                                <span class="nav-badge">25</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/personnel" class="nav-link active">
                                <i class="fas fa-users"></i>
                                <span>الأفراد</span>
                                <span class="nav-badge">150</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/distributions" class="nav-link">
                                <i class="fas fa-exchange-alt"></i>
                                <span>التوزيعات</span>
                                <span class="nav-badge">45</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-item">
                            <a href="/logout" class="nav-link" style="color: var(--danger-color);">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="main-wrapper">
                <!-- Header -->
                <header class="main-header">
                    <div class="header-title">
                        <button class="menu-toggle" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="page-title">تعديل بيانات الفرد العسكري</h1>
                    </div>

                    <div class="header-actions">
                        <div class="user-profile">
                            <div class="user-avatar">
                                {username[0].upper()}
                            </div>
                            <div class="user-info">
                                <h4>{username}</h4>
                                <p>مدير النظام</p>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Page Content -->
                <main class="page-content">
                    <!-- Page Header -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <div>
                                <h1>
                                    <i class="fas fa-user-edit"></i>
                                    تعديل بيانات الفرد العسكري
                                </h1>
                                <p>تعديل وتحديث بيانات الفرد العسكري رقم: {person['military_id']}</p>
                            </div>
                            <div class="header-actions-group">
                                <a href="/personnel" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right"></i>
                                    العودة لقائمة الأفراد
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Info Card -->
                    <div class="info-card">
                        <div class="info-card-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="info-card-content">
                            <h3>تنبيه هام</h3>
                            <p>يرجى التأكد من صحة البيانات قبل الحفظ. سيتم تحديث جميع المعلومات المرتبطة بهذا الفرد.</p>
                        </div>
                    </div>

                    <!-- Form Container -->
                    <div class="form-container">
                        <div class="form-header">
                            <h2 class="form-title">
                                <i class="fas fa-user-edit"></i>
                                تحديث بيانات الفرد العسكري
                            </h2>
                        </div>

                        <div class="form-body">
                            <form id="editPersonnelForm">
                                <div class="form-grid">
                                    <!-- الرقم العسكري -->
                                    <div class="form-group">
                                        <label class="form-label required">الرقم العسكري</label>
                                        <div class="input-group">
                                            <input type="text" class="form-input with-icon" value="{person['military_id']}" readonly style="background: #f8fafc; color: #64748b;">
                                            <i class="fas fa-id-card input-icon"></i>
                                        </div>
                                        <small style="color: #64748b; font-size: 0.75rem;">لا يمكن تعديل الرقم العسكري</small>
                                    </div>

                                    <!-- الاسم الكامل -->
                                    <div class="form-group">
                                        <label class="form-label required">الاسم الكامل</label>
                                        <div class="input-group">
                                            <input type="text" class="form-input with-icon" value="{person['full_name']}" required>
                                            <i class="fas fa-user input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- الرتبة -->
                                    <div class="form-group">
                                        <label class="form-label required">الرتبة العسكرية</label>
                                        <select class="form-select" required>
                                            <option value="">اختر الرتبة</option>
                                            <option value="عقيد" {'selected' if person['rank'] == 'عقيد' else ''}>عقيد</option>
                                            <option value="مقدم" {'selected' if person['rank'] == 'مقدم' else ''}>مقدم</option>
                                            <option value="رائد" {'selected' if person['rank'] == 'رائد' else ''}>رائد</option>
                                            <option value="نقيب" {'selected' if person['rank'] == 'نقيب' else ''}>نقيب</option>
                                            <option value="ملازم أول" {'selected' if person['rank'] == 'ملازم أول' else ''}>ملازم أول</option>
                                            <option value="ملازم" {'selected' if person['rank'] == 'ملازم' else ''}>ملازم</option>
                                            <option value="رقيب أول" {'selected' if person['rank'] == 'رقيب أول' else ''}>رقيب أول</option>
                                            <option value="رقيب" {'selected' if person['rank'] == 'رقيب' else ''}>رقيب</option>
                                            <option value="عريف" {'selected' if person['rank'] == 'عريف' else ''}>عريف</option>
                                            <option value="جندي أول" {'selected' if person['rank'] == 'جندي أول' else ''}>جندي أول</option>
                                            <option value="جندي" {'selected' if person['rank'] == 'جندي' else ''}>جندي</option>
                                        </select>
                                    </div>

                                    <!-- الوحدة -->
                                    <div class="form-group">
                                        <label class="form-label required">الوحدة العسكرية</label>
                                        <select class="form-select" required>
                                            <option value="">اختر الوحدة</option>
                                            <option value="الوحدة الأولى" {'selected' if person['unit'] == 'الوحدة الأولى' else ''}>الوحدة الأولى</option>
                                            <option value="الوحدة الثانية" {'selected' if person['unit'] == 'الوحدة الثانية' else ''}>الوحدة الثانية</option>
                                            <option value="الوحدة الثالثة" {'selected' if person['unit'] == 'الوحدة الثالثة' else ''}>الوحدة الثالثة</option>
                                            <option value="الوحدة الرابعة" {'selected' if person['unit'] == 'الوحدة الرابعة' else ''}>الوحدة الرابعة</option>
                                            <option value="الوحدة الخامسة" {'selected' if person['unit'] == 'الوحدة الخامسة' else ''}>الوحدة الخامسة</option>
                                            <option value="وحدة الدعم" {'selected' if person['unit'] == 'وحدة الدعم' else ''}>وحدة الدعم</option>
                                            <option value="وحدة الاستطلاع" {'selected' if person['unit'] == 'وحدة الاستطلاع' else ''}>وحدة الاستطلاع</option>
                                        </select>
                                    </div>

                                    <!-- رقم الهاتف -->
                                    <div class="form-group">
                                        <label class="form-label">رقم الهاتف</label>
                                        <div class="input-group">
                                            <input type="tel" class="form-input with-icon" value="{person['phone']}">
                                            <i class="fas fa-phone input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- البريد الإلكتروني -->
                                    <div class="form-group">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <div class="input-group">
                                            <input type="email" class="form-input with-icon" value="{person['email']}">
                                            <i class="fas fa-envelope input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- تاريخ الميلاد -->
                                    <div class="form-group">
                                        <label class="form-label">تاريخ الميلاد</label>
                                        <div class="input-group">
                                            <input type="date" class="form-input with-icon" value="{person['birth_date']}">
                                            <i class="fas fa-calendar input-icon"></i>
                                        </div>
                                    </div>

                                    <!-- الحالة الاجتماعية -->
                                    <div class="form-group">
                                        <label class="form-label">الحالة الاجتماعية</label>
                                        <select class="form-select">
                                            <option value="">اختر الحالة</option>
                                            <option value="أعزب" {'selected' if person['marital_status'] == 'أعزب' else ''}>أعزب</option>
                                            <option value="متزوج" {'selected' if person['marital_status'] == 'متزوج' else ''}>متزوج</option>
                                            <option value="مطلق" {'selected' if person['marital_status'] == 'مطلق' else ''}>مطلق</option>
                                            <option value="أرمل" {'selected' if person['marital_status'] == 'أرمل' else ''}>أرمل</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- العنوان -->
                                <div class="form-group">
                                    <label class="form-label">العنوان</label>
                                    <div class="input-group">
                                        <input type="text" class="form-input with-icon" value="{person['address']}">
                                        <i class="fas fa-map-marker-alt input-icon"></i>
                                    </div>
                                </div>

                                <!-- ملاحظات -->
                                <div class="form-group">
                                    <label class="form-label">ملاحظات إضافية</label>
                                    <textarea class="form-input form-textarea" rows="4">{person['notes']}</textarea>
                                </div>

                                <!-- Form Actions -->
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save"></i>
                                        حفظ التعديلات
                                    </button>
                                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                        <i class="fas fa-trash"></i>
                                        حذف الفرد
                                    </button>
                                    <a href="/personnel" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </main>
            </div>

            <script>
                // Sidebar Toggle
                function toggleSidebar() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.toggle('active');
                    overlay.classList.toggle('active');
                }}

                // Close sidebar when clicking overlay
                document.getElementById('sidebarOverlay').addEventListener('click', function() {{
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebarOverlay');

                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                }});

                // Form validation and submission
                document.getElementById('editPersonnelForm').addEventListener('submit', function(e) {{
                    e.preventDefault();

                    // Get form data
                    const fullName = this.querySelectorAll('input[type="text"]')[1].value;
                    const rank = this.querySelector('select').value;
                    const unit = this.querySelectorAll('select')[1].value;

                    // Basic validation
                    if (!fullName || !rank || !unit) {{
                        alert('يرجى ملء جميع الحقول المطلوبة');
                        return;
                    }}

                    // Simulate form submission
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;

                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                    submitBtn.disabled = true;

                    setTimeout(() => {{
                        alert('تم تحديث بيانات الفرد بنجاح!');
                        window.location.href = '/personnel';
                    }}, 2000);
                }});

                // Delete confirmation
                function confirmDelete() {{
                    if (confirm('هل أنت متأكد من حذف هذا الفرد؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!')) {{
                        if (confirm('تأكيد نهائي: سيتم حذف جميع البيانات المرتبطة بهذا الفرد. هل تريد المتابعة؟')) {{
                            // Simulate delete action
                            alert('تم حذف الفرد بنجاح');
                            window.location.href = '/personnel';
                        }}
                    }}
                }}

                // Auto-format phone number
                document.querySelector('input[type="tel"]').addEventListener('input', function() {{
                    let value = this.value.replace(/\D/g, '');
                    if (value.length > 10) {{
                        value = value.substring(0, 10);
                    }}
                    this.value = value;
                }});

                // Track changes
                let originalData = {{}};
                document.addEventListener('DOMContentLoaded', function() {{
                    const form = document.getElementById('editPersonnelForm');
                    const inputs = form.querySelectorAll('input, select, textarea');

                    inputs.forEach(input => {{
                        originalData[input.name || input.id] = input.value;
                    }});
                }});

                // Warn about unsaved changes
                window.addEventListener('beforeunload', function(e) {{
                    const form = document.getElementById('editPersonnelForm');
                    const inputs = form.querySelectorAll('input:not([readonly]), select, textarea');
                    let hasChanges = false;

                    inputs.forEach(input => {{
                        if (originalData[input.name || input.id] !== input.value) {{
                            hasChanges = true;
                        }}
                    }});

                    if (hasChanges) {{
                        e.preventDefault();
                        e.returnValue = '';
                    }}
                }});
            </script>
        </body>
        </html>
        '''

    @app.route('/test')
    def test():
        return '''
        <html dir="rtl">
        <head><title>اختبار النظام</title></head>
        <body style="font-family: Arial; padding: 20px; background: #f0f0f0;">
            <h1>🧪 اختبار النظام</h1>
            <p>✅ Flask يعمل بشكل صحيح</p>
            <p>✅ الخادم متصل</p>
            <p>✅ الترميز العربي يعمل</p>
            <p>✅ التوجيه RTL يعمل</p>
            <a href="/">← العودة للصفحة الرئيسية</a>
        </body>
        </html>
        '''

    print("✅ تم إعداد التطبيق بنجاح")
    print("🌐 بدء الخادم على http://localhost:5000")

    app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)

except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("يرجى تثبيت Flask أولاً")
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
