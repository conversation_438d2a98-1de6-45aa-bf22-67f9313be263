#!/usr/bin/env python
"""
Test script for Sessions in BANGHALAU database
"""

from database import DatabaseManager, DatabaseOperations


def main():
    """Test the Sessions operations"""
    print("Testing Sessions in BANGHALAU database...")
    
    # Initialize database
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)
    
    # Get sessions
    print("\nSessions in database:")
    sessions = db_ops.list_sessions()
    for session in sessions:
        print(f"ID: {session['id']}")
        print(f"Numero: {session['numero']}")
        print(f"Description: {session['description']}")
        print(f"Date Debut: {session['date_debut']}")
        print(f"Date Fin: {session['date_fin']}")
        print(f"Etat Session: {session['etat_session']}")
        print(f"Created at: {session['created_at']}")
        print("-" * 30)
    
    # Close connection
    db_manager.close()
    print("\nTest completed successfully.")


if __name__ == "__main__":
    main()
