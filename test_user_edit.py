#!/usr/bin/env python3
"""
Test script for user edit functionality
"""

import requests
import json

# Test configuration
BASE_URL = "http://localhost:5000"
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """Login and get session"""
    session = requests.Session()
    
    # Get login page first
    response = session.get(f"{BASE_URL}/login")
    print(f"Login page status: {response.status_code}")
    
    # Login
    login_data = {
        'username': USERNAME,
        'password': PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    print(f"Login status: {response.status_code}")
    
    if response.status_code == 200 and "dashboard" in response.url:
        print("✅ Login successful")
        return session
    else:
        print("❌ Login failed")
        return None

def test_get_users(session):
    """Test getting users list"""
    print("\n🔍 Testing get users...")
    
    response = session.get(f"{BASE_URL}/api/users")
    print(f"Get users status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            users = data.get('users', [])
            print(f"✅ Found {len(users)} users")
            return users
        else:
            print("❌ Failed to get users")
            return []
    else:
        print(f"❌ HTTP error: {response.status_code}")
        return []

def test_get_single_user(session, user_id):
    """Test getting single user"""
    print(f"\n🔍 Testing get user {user_id}...")
    
    response = session.get(f"{BASE_URL}/api/users/{user_id}")
    print(f"Get user status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            user = data.get('user')
            print(f"✅ User data: {user.get('username')} - {user.get('role')}")
            return user
        else:
            print(f"❌ Failed to get user: {data.get('message')}")
            return None
    else:
        print(f"❌ HTTP error: {response.status_code}")
        return None

def test_update_user(session, user_id, update_data):
    """Test updating user"""
    print(f"\n✏️ Testing update user {user_id}...")
    
    headers = {'Content-Type': 'application/json'}
    response = session.put(f"{BASE_URL}/api/users/{user_id}", 
                          json=update_data, headers=headers)
    
    print(f"Update user status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"✅ User updated successfully: {data.get('message')}")
            return True
        else:
            print(f"❌ Failed to update user: {data.get('message')}")
            return False
    else:
        print(f"❌ HTTP error: {response.status_code}")
        try:
            error_data = response.json()
            print(f"Error details: {error_data}")
        except:
            print(f"Response text: {response.text}")
        return False

def test_create_test_user(session):
    """Create a test user for editing"""
    print("\n➕ Creating test user...")
    
    test_user_data = {
        'username': 'test_edit_user',
        'password': 'TestPass123!',
        'full_name': 'Test Edit User',
        'email': '<EMAIL>',
        'role': 'user',
        'is_active': True
    }
    
    headers = {'Content-Type': 'application/json'}
    response = session.post(f"{BASE_URL}/api/users", 
                           json=test_user_data, headers=headers)
    
    print(f"Create user status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            user_id = data.get('user_id')
            print(f"✅ Test user created with ID: {user_id}")
            return user_id
        else:
            print(f"❌ Failed to create user: {data.get('message')}")
            return None
    else:
        print(f"❌ HTTP error: {response.status_code}")
        return None

def test_delete_user(session, user_id):
    """Delete test user"""
    print(f"\n🗑️ Deleting test user {user_id}...")
    
    response = session.delete(f"{BASE_URL}/api/users/{user_id}")
    print(f"Delete user status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"✅ User deleted successfully")
            return True
        else:
            print(f"❌ Failed to delete user: {data.get('message')}")
            return False
    else:
        print(f"❌ HTTP error: {response.status_code}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing User Edit Functionality")
    print("=" * 50)
    
    # Login
    session = login()
    if not session:
        print("❌ Cannot proceed without login")
        return
    
    # Get existing users
    users = test_get_users(session)
    if not users:
        print("❌ No users found")
        return
    
    # Create test user
    test_user_id = test_create_test_user(session)
    if not test_user_id:
        print("❌ Cannot create test user")
        return
    
    try:
        # Test getting single user
        user_data = test_get_single_user(session, test_user_id)
        if not user_data:
            print("❌ Cannot get test user data")
            return
        
        # Test updating user
        update_data = {
            'username': 'test_edit_user_updated',
            'full_name': 'Test Edit User Updated',
            'email': '<EMAIL>',
            'role': 'admin',
            'is_active': True
        }
        
        success = test_update_user(session, test_user_id, update_data)
        if success:
            print("✅ User edit functionality works!")
            
            # Verify the update
            updated_user = test_get_single_user(session, test_user_id)
            if updated_user:
                print(f"✅ Verified update: {updated_user.get('username')} - {updated_user.get('role')}")
        else:
            print("❌ User edit functionality failed!")
    
    finally:
        # Clean up - delete test user
        test_delete_user(session, test_user_id)
    
    print("\n" + "=" * 50)
    print("🎯 Test completed!")

if __name__ == "__main__":
    main()
