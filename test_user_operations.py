#!/usr/bin/env python3
"""
Comprehensive test for user management operations
Tests: Add, Edit, Delete, Reset Password, Toggle Status
"""

import requests
import json
import time

# Test configuration
BASE_URL = "http://localhost:5000"
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """Login and get session"""
    session = requests.Session()
    
    # Get login page first
    response = session.get(f"{BASE_URL}/login")
    print(f"📍 Login page status: {response.status_code}")
    
    # Login
    login_data = {
        'username': USERNAME,
        'password': PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    print(f"🔐 Login status: {response.status_code}")
    
    if response.status_code == 200 and ("dashboard" in response.url or "users" in response.url):
        print("✅ Login successful")
        return session
    else:
        print("❌ Login failed")
        return None

def test_add_user(session):
    """Test adding a new user"""
    print("\n➕ Testing Add User Operation...")
    
    test_user_data = {
        'username': 'test_user_french',
        'full_name': 'Utilisateur Test Français',
        'email': '<EMAIL>',
        'password': 'TestPass123!',
        'role': 'user',
        'is_active': True
    }
    
    headers = {'Content-Type': 'application/json'}
    response = session.post(f"{BASE_URL}/api/users", 
                           json=test_user_data, headers=headers)
    
    print(f"📊 Add user status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            user_id = data.get('user_id')
            print(f"✅ User added successfully with ID: {user_id}")
            return user_id
        else:
            print(f"❌ Failed to add user: {data.get('message')}")
            return None
    else:
        print(f"❌ HTTP error: {response.status_code}")
        try:
            error_data = response.json()
            print(f"Error details: {error_data}")
        except:
            print(f"Response text: {response.text}")
        return None

def test_get_user(session, user_id):
    """Test getting user data"""
    print(f"\n🔍 Testing Get User {user_id}...")
    
    response = session.get(f"{BASE_URL}/api/users/{user_id}")
    print(f"📊 Get user status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            user = data.get('user')
            print(f"✅ User data retrieved: {user.get('username')} - {user.get('role')}")
            return user
        else:
            print(f"❌ Failed to get user: {data.get('message')}")
            return None
    else:
        print(f"❌ HTTP error: {response.status_code}")
        return None

def test_edit_user(session, user_id):
    """Test editing user"""
    print(f"\n✏️ Testing Edit User {user_id}...")
    
    update_data = {
        'username': 'test_user_french_updated',
        'full_name': 'Utilisateur Test Français Modifié',
        'email': '<EMAIL>',
        'role': 'admin',
        'is_active': True
    }
    
    headers = {'Content-Type': 'application/json'}
    response = session.put(f"{BASE_URL}/api/users/{user_id}", 
                          json=update_data, headers=headers)
    
    print(f"📊 Edit user status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"✅ User updated successfully: {data.get('message')}")
            return True
        else:
            print(f"❌ Failed to update user: {data.get('message')}")
            return False
    else:
        print(f"❌ HTTP error: {response.status_code}")
        return False

def test_reset_password(session, user_id):
    """Test resetting user password"""
    print(f"\n🔑 Testing Reset Password for User {user_id}...")
    
    password_data = {
        'new_password': 'NewTestPass123!'
    }
    
    headers = {'Content-Type': 'application/json'}
    response = session.post(f"{BASE_URL}/api/users/{user_id}/reset-password", 
                           json=password_data, headers=headers)
    
    print(f"📊 Reset password status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"✅ Password reset successfully: {data.get('message')}")
            return True
        else:
            print(f"❌ Failed to reset password: {data.get('message')}")
            return False
    else:
        print(f"❌ HTTP error: {response.status_code}")
        return False

def test_toggle_status(session, user_id):
    """Test toggling user status"""
    print(f"\n🔄 Testing Toggle Status for User {user_id}...")
    
    headers = {'Content-Type': 'application/json'}
    response = session.post(f"{BASE_URL}/api/users/{user_id}/toggle-status", 
                           headers=headers)
    
    print(f"📊 Toggle status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"✅ Status toggled successfully: {data.get('message')}")
            return True
        else:
            print(f"❌ Failed to toggle status: {data.get('message')}")
            return False
    else:
        print(f"❌ HTTP error: {response.status_code}")
        return False

def test_delete_user(session, user_id):
    """Test deleting user"""
    print(f"\n🗑️ Testing Delete User {user_id}...")
    
    response = session.delete(f"{BASE_URL}/api/users/{user_id}")
    print(f"📊 Delete user status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"✅ User deleted successfully: {data.get('message')}")
            return True
        else:
            print(f"❌ Failed to delete user: {data.get('message')}")
            return False
    else:
        print(f"❌ HTTP error: {response.status_code}")
        return False

def test_users_page_access(session):
    """Test accessing users management page"""
    print("\n🌐 Testing Users Page Access...")
    
    response = session.get(f"{BASE_URL}/users")
    print(f"📊 Users page status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.text
        
        # Check for French translations
        french_terms = [
            "Gestion des Utilisateurs",
            "Ajouter un utilisateur",
            "Total Utilisateurs",
            "Utilisateurs Actifs"
        ]
        
        found_terms = 0
        for term in french_terms:
            if term in content:
                found_terms += 1
                print(f"✅ Found: {term}")
            else:
                print(f"❌ Missing: {term}")
        
        if found_terms >= len(french_terms) * 0.8:
            print("✅ Users page loaded with French interface")
            return True
        else:
            print("❌ Users page missing French translations")
            return False
    else:
        print(f"❌ Failed to load users page: {response.status_code}")
        return False

def main():
    """Main test function"""
    print("🧪 BANGHALAU User Management Operations Test")
    print("=" * 60)
    
    # Login
    session = login()
    if not session:
        print("❌ Cannot proceed without login")
        return False
    
    # Test page access
    page_access = test_users_page_access(session)
    
    # Test user operations
    test_user_id = None
    results = []
    
    try:
        # Test Add User
        test_user_id = test_add_user(session)
        results.append(test_user_id is not None)
        
        if test_user_id:
            # Test Get User
            user_data = test_get_user(session, test_user_id)
            results.append(user_data is not None)
            
            # Test Edit User
            edit_success = test_edit_user(session, test_user_id)
            results.append(edit_success)
            
            # Verify edit worked
            if edit_success:
                updated_user = test_get_user(session, test_user_id)
                if updated_user and updated_user.get('username') == 'test_user_french_updated':
                    print("✅ Edit verification successful")
                    results.append(True)
                else:
                    print("❌ Edit verification failed")
                    results.append(False)
            
            # Test Reset Password
            reset_success = test_reset_password(session, test_user_id)
            results.append(reset_success)
            
            # Test Toggle Status
            toggle_success = test_toggle_status(session, test_user_id)
            results.append(toggle_success)
            
            # Test Delete User
            delete_success = test_delete_user(session, test_user_id)
            results.append(delete_success)
    
    except Exception as e:
        print(f"💥 Test crashed: {e}")
        results.append(False)
    
    # Results
    print("\n" + "=" * 60)
    print("🎯 Test Results:")
    
    operations = [
        "Page Access (French)",
        "Add User",
        "Get User",
        "Edit User", 
        "Edit Verification",
        "Reset Password",
        "Toggle Status",
        "Delete User"
    ]
    
    results.insert(0, page_access)  # Add page access result
    
    passed = 0
    for i, (operation, result) in enumerate(zip(operations, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {i+1}. {operation}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n📊 Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All user management operations working perfectly!")
        print("✅ French interface is fully functional")
    else:
        print(f"⚠️ {total - passed} operations failed")
        print("🔧 Please check the failed operations")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 User management system is fully operational!")
        print("🇫🇷 French interface working perfectly!")
    else:
        print("\n❌ Some operations failed!")
        print("🔧 Please fix the issues before using the system.")
