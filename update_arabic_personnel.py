#!/usr/bin/env python3
"""
Script pour mettre à jour le personnel avec des grades et unités arabes
"""

from database.db_manager import DatabaseManager
from database.operations import DatabaseOperations

def update_arabic_personnel():
    """Mettre à jour le personnel avec des grades et unités arabes"""

    # إضافة أفراد جدد بالرتب والوحدات العربية الموجودة
    new_personnel_data = [
        {
            'numero': 'M020',
            'nom': 'سعد',
            'prenom': 'أحمد',
            'grade_name': 'عقيد',
            'unite_description': 'قيادة الناحية',
            'telephone': '+213 555 111 222',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M021',
            'nom': 'خديجة',
            'prenom': 'فاطمة',
            'grade_name': 'رائد',
            'unite_description': 'رئيس الأمانة',
            'telephone': '+213 555 333 444',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M022',
            'nom': 'محمد',
            'prenom': 'عبد الله',
            'grade_name': 'نقيب',
            'unite_description': 'مصلحة التشريفات',
            'telephone': '+213 555 555 666',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M023',
            'nom': 'عائشة',
            'prenom': 'زينب',
            'grade_name': 'ملازم',
            'unite_description': 'نائب قائد الناحية',
            'telephone': '+213 555 777 888',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M024',
            'nom': 'يوسف',
            'prenom': 'إبراهيم',
            'grade_name': 'رقيب أول',
            'unite_description': 'رئيس أركان الناحية',
            'telephone': '+213 555 999 000',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M025',
            'nom': 'فاطمة',
            'prenom': 'نور',
            'grade_name': 'رقيب',
            'unite_description': 'قيادة الجو',
            'telephone': '+213 555 111 333',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M026',
            'nom': 'علي',
            'prenom': 'حسن',
            'grade_name': 'عريف أول',
            'unite_description': 'القيادة الجهوية للدفاع الجوي',
            'telephone': '+213 555 222 444',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M027',
            'nom': 'مريم',
            'prenom': 'سارة',
            'grade_name': 'عريف',
            'unite_description': 'القيادة الجهوية اللوجستية',
            'telephone': '+213 555 333 555',
            'email': '<EMAIL>'
        },
        {
            'numero': 'M028',
            'nom': 'حسام',
            'prenom': 'طارق',
            'grade_name': 'جندي',
            'unite_description': 'القيادة الجهوية السادسة للدرك الوطني',
            'telephone': '+213 555 444 666',
            'email': '<EMAIL>'
        }
    ]

    # إضافة البيانات إلى قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    db_ops = DatabaseOperations(db_manager)

    success_count = 0
    error_count = 0

    for person in new_personnel_data:
        try:
            # البحث عن الرتبة (مع التعامل مع المسافات)
            grade = db_ops.get_grade_by_name(person['grade_name'])
            if not grade:
                # محاولة البحث بمسافة إضافية
                grade = db_ops.get_grade_by_name(person['grade_name'] + ' ')
            if not grade:
                print(f'❌ الرتبة {person["grade_name"]} غير موجودة')
                error_count += 1
                continue

            # البحث عن الوحدة
            unites = db_ops.list_unites()
            unite = None
            for u in unites:
                if u['description'] == person['unite_description']:
                    unite = u
                    break

            if not unite:
                print(f'❌ الوحدة {person["unite_description"]} غير موجودة')
                error_count += 1
                continue

            person_id = db_ops.create_personnel_militaire(
                matricule=person['numero'],
                nom=person['nom'],
                prenom=person['prenom'],
                grade_id=grade['id'],
                unite_id=unite['id'],
                numero=person['numero']
            )

            if person_id:
                print(f'✅ تم إضافة الفرد {person["nom"]} {person["prenom"]} بنجاح (ID: {person_id})')
                print(f'   الرتبة: {person["grade_name"]} | الوحدة: {person["unite_description"]}')
                success_count += 1
            else:
                print(f'❌ خطأ في إضافة الفرد {person["nom"]} {person["prenom"]}: الرقم موجود مسبقاً')
                error_count += 1
        except Exception as e:
            print(f'❌ خطأ في إضافة الفرد {person["nom"]} {person["prenom"]}: {str(e)}')
            error_count += 1

    db_manager.close()

    print(f'\n📊 النتائج:')
    print(f'✅ نجح: {success_count}')
    print(f'❌ أخطاء: {error_count}')
    print('🎉 تم الانتهاء من إضافة الأفراد العرب الجدد')

if __name__ == "__main__":
    update_arabic_personnel()
