@echo off
chcp 65001 >nul
title BANGHALAU - نظام إدارة توزيع البنغالوهات

echo ======================================================================
echo 🏠 BANGHALAU - نظام إدارة توزيع البنغالوهات
echo ======================================================================
echo.
echo 🚀 بدء تشغيل النظام...
echo.

REM Check if Python is available
py --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير متاح
    echo    يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM Check if Flask is installed
py -c "import flask" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Flask غير مثبت - جاري التثبيت...
    py -m pip install flask
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Flask
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت Flask بنجاح
)

echo ✅ Flask متوفر
echo.

REM Check database
if exist "banghalau.db" (
    echo ✅ قاعدة البيانات موجودة
) else (
    echo ⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها تلقائياً
)

echo.
echo 🌐 معلومات الوصول:
echo    📍 الرابط: http://localhost:5000
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🔄 بدء خادم الويب...
echo    (اضغط Ctrl+C لإيقاف الخادم)
echo ======================================================================
echo.

REM Start the application
py start_banghalau.py

echo.
echo 🛑 تم إيقاف الخادم
pause
